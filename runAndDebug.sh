#!/bin/bash

# Ensure we're in the right directory
cd "$(dirname "$0")"

# Colors for better output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}Building VitalSigns Debug APK...${NC}"
./gradlew assembleDebug

if [ $? -ne 0 ]; then
  echo -e "${RED}Build failed! Check errors above.${NC}"
  exit 1
fi

echo -e "${GREEN}Build successful!${NC}"

# Check if a device is connected
echo -e "${BLUE}Checking for connected devices...${NC}"
DEVICES=$(adb devices | grep -v "List" | grep -v "^$" | wc -l)

if [ $DEVICES -eq 0 ]; then
  echo -e "${RED}No devices connected. Please connect a device or start an emulator.${NC}"
  exit 1
fi

echo -e "${GREEN}Found $DEVICES device(s).${NC}"

# Install the APK
echo -e "${BLUE}Installing APK...${NC}"
adb install -r app/build/outputs/apk/debug/app-debug.apk

if [ $? -ne 0 ]; then
  echo -e "${RED}Installation failed!${NC}"
  exit 1
fi

echo -e "${GREEN}APK installed successfully!${NC}"

# Launch the app
echo -e "${BLUE}Launching app...${NC}"
adb shell am start -n com.example.myapplication/.MainActivity

echo -e "${YELLOW}Running logcat to show app output...${NC}"
echo -e "${YELLOW}Press Ctrl+C to stop logcat${NC}"
echo -e "${GREEN}=============== APP LOGS ===============${NC}"

# Clear logcat first
adb logcat -c

# Show only logs from our app
adb logcat -v time VitalSignsApp:V MainActivity:V VitalSignsViewModel:V *:E 