package com.example.myapplication

import android.app.Application
import android.util.Log
import com.example.myapplication.di.AppModule
import android.widget.Toast
import java.io.File
import com.example.myapplication.util.CrashLogger
import com.zoll.zoxseries.ZOXSeries

/**
 * Custom Application class for initialization
 */
class VitalSignsApplication : Application() {
    
    companion object {
        private const val TAG = "VitalSignsApp"
    }
    
    override fun onCreate() {
        super.onCreate()
        
        // Initialize crash logger first to catch all errors
        CrashLogger.initialize(this)
        
        // Initialize Zoll SDK
        ZOXSeries.initialize(applicationContext)
        
        try {
            // Log application start
            Log.i(TAG, "VitalSignsApplication onCreate started")
            
            // Create required directories
            try {
                createRequiredDirectories()
                Log.i(TAG, "Required directories created successfully")
            } catch (e: Exception) {
                Log.e(TAG, "Error creating required directories", e)
                CrashLogger.logError(TAG, "Error creating directories", e)
                Toast.makeText(this, "Error creating app directories: ${e.message}", Toast.LENGTH_LONG).show()
            }
            
            // Initialize repository early for access
            try {
                Log.d(TAG, "Initializing repository early")
                val repository = AppModule.provideRepository(this)
                Log.d(TAG, "Repository created successfully: ${repository.javaClass.name}")
                
                // Debug repository methods
                val methods = repository.javaClass.methods.map { it.name }
                Log.d(TAG, "Repository methods: ${methods.distinct().joinToString()}")
            } catch (e: Exception) {
                Log.e(TAG, "❌ Error initializing repository", e)
                CrashLogger.logError(TAG, "Error initializing repository", e)
                Toast.makeText(this, "Error initializing app: ${e.message}", Toast.LENGTH_LONG).show()
            }
            
            Log.i(TAG, "✅ VitalSignsApplication onCreate completed")
        } catch (e: Exception) {
            Log.e(TAG, "FATAL: Application initialization failed", e)
            CrashLogger.logError(TAG, "FATAL: Application failed to initialize", e)
            Toast.makeText(this, "Application failed to start properly: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    private fun createRequiredDirectories() {
        try {
            // Create directories for vital signs data
            val vitalSignsDir = File(getExternalFilesDir(null), "vital_signs")
            if (!vitalSignsDir.exists()) {
                val success = vitalSignsDir.mkdirs()
                Log.d(TAG, "Created vital_signs directory: $success")
            }
            
            // Create directory for cache
            val cacheDir = File(getExternalFilesDir(null), "cache")
            if (!cacheDir.exists()) {
                val success = cacheDir.mkdirs()
                Log.d(TAG, "Created cache directory: $success")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error creating directories", e)
            throw e
        }
    }
} 