package com.example.myapplication

import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.ui.Modifier
import androidx.core.view.WindowCompat
import com.example.myapplication.di.AppModule
import com.example.myapplication.ui.navigation.VitalSignsNavigation
import com.example.myapplication.ui.theme.MyApplicationTheme
import com.example.myapplication.util.CrashLogger

class MainActivity : ComponentActivity() {
    companion object {
        private const val TAG = "MainActivity"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        Log.d(TAG, "onCreate started")

        try {
            super.onCreate(savedInstanceState)

            // For Samsung devices, we need to be careful with edge-to-edge display
            // Setting this to true ensures the system windows are respected
            WindowCompat.setDecorFitsSystemWindows(window, true)

            // Initialize the view model
            Log.d(TAG, "Initializing repository and viewmodel")
            val repository = createRepositorySafely()
            val viewModel = createViewModelSafely(repository)

            Log.d(TAG, "Setting up UI content")
            // Using regular setContent without try-catch
            setContent {
                MyApplicationTheme {
                    // A surface container using the 'background' color from the theme
                    Surface(
                        modifier = Modifier.fillMaxSize(),
                        color = MaterialTheme.colorScheme.background
                    ) {
                        VitalSignsNavigation(viewModel)
                    }
                }
            }
            Log.d(TAG, "onCreate completed successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Fatal error in MainActivity.onCreate", e)
            CrashLogger.logError(TAG, "Fatal error in MainActivity.onCreate", e)
            // Re-throw the exception to crash the app
            throw e
        }
    }

    private fun createRepositorySafely() = try {
        AppModule.provideRepository(this)
    } catch (e: Exception) {
        Log.e(TAG, "Failed to create repository", e)
        CrashLogger.logError(TAG, "Failed to create repository", e)
        throw e
    }

    private fun createViewModelSafely(repository: com.example.myapplication.data.repository.VitalSignsRepository) = try {
        AppModule.provideViewModel(this, repository)
    } catch (e: Exception) {
        Log.e(TAG, "Failed to create viewModel", e)
        CrashLogger.logError(TAG, "Failed to create viewModel", e)
        throw e
    }
}