package com.example.myapplication.data.source

import com.example.myapplication.data.ClinicalPracticeGuidelines
import com.example.myapplication.data.model.ClinicalEvent
import com.example.myapplication.data.model.ClinicalEventType
import com.example.myapplication.data.model.VitalSign
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.isActive
import java.io.File
import java.util.Date
import kotlin.math.abs
import kotlin.random.Random

/**
 * Implementation of the VitalSignsDataSource interface that generates simulated vital signs
 */
class SimulatedVitalSignsDataSource : VitalSignsDataSource {
    
    private var isMonitoring = false
    private var updateIntervalMs = 2000L // 2 seconds by default
    private var currentScenario = "TBI" // Default scenario
    
    override suspend fun loadDataFromDirectory(directory: File): List<VitalSign> {
        // Simulated data source doesn't load from files
        return emptyList()
    }
    
    override suspend fun loadDataFromFile(file: File): List<VitalSign> {
        // Simulated data source doesn't load from files
        return emptyList()
    }
    
    override fun getRealtimeVitalSigns(): Flow<VitalSign> = callbackFlow {
        isMonitoring = true
        var previousVitalSign: VitalSign? = null
        
        while (isActive && isMonitoring) {
            val newVitalSign = generateSimulatedVitalSign(previousVitalSign, currentScenario)
            send(newVitalSign)
            previousVitalSign = newVitalSign
            delay(updateIntervalMs)
        }
        
        awaitClose { isMonitoring = false }
    }
    
    override suspend fun startMonitoring(
        ipAddress: String,
        port: Int,
        authRequired: Boolean,
        username: String,
        password: String
    ): Boolean {
        isMonitoring = true
        return true
    }
    
    override suspend fun stopMonitoring() {
        isMonitoring = false
    }
    
    /**
     * Generate a simulated vital sign based on a previous vital sign and optional scenario
     * @param previousVitalSign The previous vital sign, if any
     * @param scenario The clinical scenario to simulate, affects baseline values
     * @return A new VitalSign instance with simulated values
     */
    fun generateSimulatedVitalSign(previousVitalSign: VitalSign?, scenario: String = "TBI"): VitalSign {
        // Choose base values based on the scenario
        val baseValues = when (scenario) {
            "TBI" -> mapOf( // Traumatic Brain Injury
                "hr" to 110.0,     // Elevated due to trauma
                "spO2" to 94.0,    // Slightly reduced
                "nibpSys" to 160.0, // Elevated due to increased ICP
                "nibpDia" to 90.0,
                "nibpMap" to 113.0,
                "temp1" to 99.2,   // Slight fever
                "respRate" to 22.0, // Elevated
                "etCO2" to 30.0     // Lower due to hyperventilation
            )
            "Sepsis" -> mapOf(
                "hr" to 125.0,     // Tachycardia
                "spO2" to 92.0,    // Lower due to inflammatory response
                "nibpSys" to 85.0, // Hypotension
                "nibpDia" to 50.0,
                "nibpMap" to 62.0,
                "temp1" to 102.5,  // Significant fever
                "respRate" to 28.0, // Tachypnea
                "etCO2" to 32.0
            )
            "SepsisARDS" -> mapOf(
                "hr" to 130.0,     // Severe tachycardia
                "spO2" to 88.0,    // Hypoxemia
                "nibpSys" to 80.0, // Severe hypotension
                "nibpDia" to 45.0,
                "nibpMap" to 57.0,
                "temp1" to 103.0,  // High fever
                "respRate" to 32.0, // Severe tachypnea
                "etCO2" to 28.0    // Low due to ARDS
            )
            "ACS" -> mapOf( // Acute Coronary Syndrome
                "hr" to 95.0,      // Moderate tachycardia
                "spO2" to 95.0,    
                "nibpSys" to 145.0, // Hypertension
                "nibpDia" to 88.0,
                "nibpMap" to 107.0,
                "temp1" to 98.8,
                "respRate" to 20.0,
                "etCO2" to 35.0
            )
            "DCR" -> mapOf( // Damage Control Resuscitation (hemorrhagic shock)
                "hr" to 135.0,     // Severe tachycardia due to hypovolemia
                "spO2" to 90.0,    // Reduced due to poor perfusion
                "nibpSys" to 70.0, // Hypotension due to blood loss
                "nibpDia" to 40.0,
                "nibpMap" to 50.0,
                "temp1" to 96.8,   // Hypothermia due to shock
                "respRate" to 25.0, // Compensatory tachypnea
                "etCO2" to 30.0    // Reduced due to poor perfusion
            )
            "Burn" -> mapOf(
                "hr" to 120.0,     // Tachycardia due to pain and stress
                "spO2" to 93.0,    // Possible inhalation injury
                "nibpSys" to 150.0, // Initial hypertension from stress
                "nibpDia" to 85.0,
                "nibpMap" to 107.0,
                "temp1" to 97.8,   // Can be lower due to heat loss from burned skin
                "respRate" to 24.0, // Tachypnea
                "etCO2" to 33.0
            )
            "AFib" -> mapOf( // Atrial Fibrillation
                "hr" to 150.0,     // Rapid, irregular heart rate
                "spO2" to 96.0,
                "nibpSys" to 130.0,
                "nibpDia" to 80.0,
                "nibpMap" to 97.0,
                "temp1" to 98.6,
                "respRate" to 18.0,
                "etCO2" to 35.0
            )
            "Trauma" -> mapOf(
                "hr" to 115.0,     // Tachycardia due to pain and stress
                "spO2" to 95.0,
                "nibpSys" to 140.0, // Elevated due to stress response
                "nibpDia" to 85.0,
                "nibpMap" to 103.0,
                "temp1" to 98.8,
                "respRate" to 22.0, // Mild tachypnea
                "etCO2" to 34.0
            )
            "AorticDissectionStroke" -> mapOf(
                "hr" to 85.0,      // Can be normal or elevated
                "spO2" to 97.0,
                "nibpSys" to 180.0, // Severe hypertension
                "nibpDia" to 100.0,
                "nibpMap" to 127.0,
                "temp1" to 98.6,
                "respRate" to 16.0,
                "etCO2" to 35.0
            )
            else -> mapOf( // Default healthy values
                "hr" to 75.0,
                "spO2" to 98.0,
                "nibpSys" to 120.0,
                "nibpDia" to 80.0,
                "nibpMap" to 93.0,
                "temp1" to 98.6,
                "respRate" to 16.0,
                "etCO2" to 35.0
            )
        }
        
        // Variation ranges for each vital sign
        val variations = mapOf(
            "hr" to 3.0,
            "spO2" to 1.0,
            "nibpSys" to 5.0,
            "nibpDia" to 3.0,
            "nibpMap" to 3.0,
            "temp1" to 0.2,
            "respRate" to 1.0,
            "etCO2" to 2.0
        )
        
        // Create new values with variations
        val hr = generateVitalValue(previousVitalSign?.hr, baseValues["hr"]!!, variations["hr"]!!)
        val spO2 = generateVitalValue(previousVitalSign?.spO2, baseValues["spO2"]!!, variations["spO2"]!!)
            .coerceIn(85.0, 100.0) // SpO2 should be between 85-100
        val nibpSys = generateVitalValue(previousVitalSign?.nibpSys, baseValues["nibpSys"]!!, variations["nibpSys"]!!)
        val nibpDia = generateVitalValue(previousVitalSign?.nibpDia, baseValues["nibpDia"]!!, variations["nibpDia"]!!)
        
        // Calculate MAP if it's derived (approximation formula)
        val nibpMap = if (Random.nextBoolean()) {
            (nibpSys + 2 * nibpDia) / 3
        } else {
            generateVitalValue(previousVitalSign?.nibpMap, baseValues["nibpMap"]!!, variations["nibpMap"]!!)
        }
        
        val temp1 = generateVitalValue(previousVitalSign?.temp1, baseValues["temp1"]!!, variations["temp1"]!!)
        val respRate = generateVitalValue(previousVitalSign?.respRate, baseValues["respRate"]!!, variations["respRate"]!!)
        val etCO2 = generateVitalValue(previousVitalSign?.etCO2, baseValues["etCO2"]!!, variations["etCO2"]!!)
        
        // Generate current date
        val timeObj = Date()
        
        return VitalSign(
            timeObj = timeObj,
            timeStr = timeObj.toString(),
            hr = hr,
            spO2 = spO2,
            nibpSys = nibpSys,
            nibpDia = nibpDia,
            nibpMap = nibpMap,
            temp1 = temp1,
            respRate = respRate,
            etCO2 = etCO2,
            // Default to valid status for all vital signs
            hrStatus = "valid",
            spO2Status = "valid",
            nibpSysStatus = "valid",
            nibpDiaStatus = "valid",
            nibpMapStatus = "valid",
            temp1Status = "valid",
            respRateStatus = "valid",
            etCO2Status = "valid",
            
            // Metadata
            deviceSerial = "SIMULATOR",
            scenario = scenario, // Use the provided scenario
            isValid = true,
            inSimWindow = true,
            elapsedMin = previousVitalSign?.elapsedMin?.plus(updateIntervalMs / 60000.0) ?: 0.0
        )
    }
    
    /**
     * Generate a simulated vital sign for interface compatibility
     */
    override fun generateSimulatedVitalSign(previousVitalSign: VitalSign?): VitalSign {
        return generateSimulatedVitalSign(previousVitalSign, currentScenario)
    }
    
    override suspend fun detectClinicalEvents(
        vitalSigns: List<VitalSign>, 
        scenario: String
    ): List<ClinicalEvent> {
        if (vitalSigns.isEmpty()) {
            return emptyList()
        }
        
        val events = mutableListOf<ClinicalEvent>()
        // Get scenario-specific thresholds
        val thresholds = ClinicalPracticeGuidelines.ALL_THRESHOLDS[scenario] ?: return emptyList()
        
        // Check vital signs against thresholds
        for (vitalSign in vitalSigns) {
            // Check heart rate
            if (vitalSign.hr != null) {
                val hrThreshold = thresholds["Hr"]
                if (hrThreshold != null) {
                    val (minHr, maxHr) = hrThreshold
                    if (minHr != null && vitalSign.hr < minHr) {
                        events.add(
                            ClinicalEvent(
                                timestamp = vitalSign.timeObj,
                                vitalSign = "Hr",
                                type = ClinicalEventType.CRITICAL_LOW,
                                description = "Heart rate critically low: ${vitalSign.hr}",
                                value = vitalSign.hr
                            )
                        )
                    } else if (maxHr != null && vitalSign.hr > maxHr) {
                        events.add(
                            ClinicalEvent(
                                timestamp = vitalSign.timeObj,
                                vitalSign = "Hr",
                                type = ClinicalEventType.CRITICAL_HIGH,
                                description = "Heart rate critically high: ${vitalSign.hr}",
                                value = vitalSign.hr
                            )
                        )
                    }
                }
            }
            
            // Check SpO2
            if (vitalSign.spO2 != null) {
                val spO2Threshold = thresholds["SpO2"]
                if (spO2Threshold != null) {
                    val (minSpO2, _) = spO2Threshold
                    if (minSpO2 != null && vitalSign.spO2 < minSpO2) {
                        events.add(
                            ClinicalEvent(
                                timestamp = vitalSign.timeObj,
                                vitalSign = "SpO2",
                                type = ClinicalEventType.CRITICAL_LOW,
                                description = "SpO2 critically low: ${vitalSign.spO2}",
                                value = vitalSign.spO2
                            )
                        )
                    }
                }
            }
            
            // Check MAP
            if (vitalSign.nibpMap != null) {
                val mapThreshold = thresholds["NIBP_MAP"]
                if (mapThreshold != null) {
                    val (minMap, maxMap) = mapThreshold
                    if (minMap != null && vitalSign.nibpMap < minMap) {
                        events.add(
                            ClinicalEvent(
                                timestamp = vitalSign.timeObj,
                                vitalSign = "NIBP_MAP",
                                type = ClinicalEventType.CRITICAL_LOW,
                                description = "MAP critically low: ${vitalSign.nibpMap}",
                                value = vitalSign.nibpMap
                            )
                        )
                    } else if (maxMap != null && vitalSign.nibpMap > maxMap) {
                        events.add(
                            ClinicalEvent(
                                timestamp = vitalSign.timeObj,
                                vitalSign = "NIBP_MAP",
                                type = ClinicalEventType.CRITICAL_HIGH,
                                description = "MAP critically high: ${vitalSign.nibpMap}",
                                value = vitalSign.nibpMap
                            )
                        )
                    }
                }
            }
            
            // Add more vital sign checks as needed...
        }
        
        // Detect rapid changes
        if (vitalSigns.size > 1) {
            for (i in 1 until vitalSigns.size) {
                val current = vitalSigns[i]
                val previous = vitalSigns[i-1]
                
                // Check for rapid heart rate changes
                if (current.hr != null && previous.hr != null) {
                    val hrChange = abs(current.hr - previous.hr)
                    if (hrChange > 15) { // Arbitrary threshold for rapid change
                        events.add(
                            ClinicalEvent(
                                timestamp = current.timeObj,
                                vitalSign = "Hr",
                                type = ClinicalEventType.RAPID_CHANGE,
                                description = "Rapid change in heart rate: ${hrChange}",
                                value = current.hr,
                                rate = hrChange / (current.elapsedMin!! - previous.elapsedMin!!)
                            )
                        )
                    }
                }
                
                // Add more rapid change detection as needed...
            }
        }
        
        return events
    }
    
    override suspend fun getVitalSignsByDateRange(startDate: Date, endDate: Date): List<VitalSign> {
        // Simulated data source doesn't have historical data
        return emptyList()
    }
    
    override suspend fun getVitalSignsByScenarioAndMannequin(
        scenario: String, 
        mannequin: String
    ): List<VitalSign> {
        // Simulated data source doesn't have historical data
        return emptyList()
    }
    
    /**
     * Helper function to generate a realistic vital sign value
     * @param previousValue The previous value, if any
     * @param baseValue The base value to use if no previous value
     * @param variationRange The maximum allowed variation
     * @return A realistic vital sign value
     */
    private fun generateVitalValue(previousValue: Double?, baseValue: Double, variationRange: Double): Double {
        val basedOn = previousValue ?: baseValue
        val variation = Random.nextDouble(-variationRange, variationRange)
        return basedOn + variation
    }
    
    /**
     * Set the update interval for simulated vital signs
     * @param intervalMs The interval in milliseconds
     */
    fun setUpdateInterval(intervalMs: Long) {
        if (intervalMs > 0) {
            updateIntervalMs = intervalMs
        }
    }
    
    /**
     * Set the current scenario for simulation
     * @param scenario The scenario to simulate
     */
    fun setScenario(scenario: String) {
        currentScenario = scenario
    }
} 