package com.example.myapplication.data.model

import java.util.Date

/**
 * Data class representing a vital sign reading
 */
data class VitalSign(
    val timeObj: Date,
    val timeStr: String,
    val deviceSerial: String? = null,
    val sourceFile: String? = null,
    
    // Vital signs
    val hr: Double? = null,
    val spO2: Double? = null,
    val nibpSys: Double? = null,
    val nibpDia: Double? = null,
    val nibpMap: Double? = null,
    val temp1: Double? = null,
    val respRate: Double? = null,
    val etCO2: Double? = null,
    val fiCO2: Double? = null,
    
    // SpO2-related parameters
    val spMet: Double? = null,
    val spCo: Double? = null,
    val pvi: Double? = null,
    val pi: Double? = null,
    val spOC: Double? = null,
    val spHb: Double? = null,
    
    // IBP readings
    val ibp1Sys: Double? = null,
    val ibp1Dia: Double? = null,
    val ibp1Map: Double? = null,
    
    // Status information
    val hrStatus: String? = "valid",
    val spO2Status: String? = "valid",
    val nibpSysStatus: String? = "valid",
    val nibpDiaStatus: String? = "valid",
    val nibpMapStatus: String? = "valid",
    val temp1Status: String? = "valid",
    val respRateStatus: String? = "valid",
    val etCO2Status: String? = "valid",
    
    // Simulation and timing data
    val course: String? = null,
    val sim: String? = null,
    val simType: String? = null,
    val simRunNumber: String? = null,
    val rawMannequin: String? = null,
    val overrideMannequin: String? = null,
    val dateStr: String? = null,
    val scenario: String? = null,
    val inSimWindow: Boolean = false,
    val isValid: Boolean = false,
    val elapsedMin: Double? = null,
    // New property for precise simulation detection
    val inPreciseSimTimeframe: Boolean = false,
    
    // Type of simulation (Training or Graded)
    val simulationType: String? = null,
    
    // Vital sign status evaluations based on scenario thresholds
    val vitalSignStatuses: Map<String, VitalSignStatus> = emptyMap()
)

/**
 * Represents a collection of vital signs with relevant metadata
 */
data class VitalSignsCollection(
    val vitalSigns: List<VitalSign>,
    val startTime: Date,
    val endTime: Date,
    val scenario: String,
    val mannequin: String,
    val course: String? = null,
    val sim: String? = null
)

/**
 * Thresholds for vital signs based on clinical scenario
 */
data class VitalSignThresholds(
    val name: String,
    val minValue: Double?,
    val maxValue: Double?,
    val normalMin: Double?,
    val normalMax: Double?
)

/**
 * Legacy status of a vital sign reading from the monitor
 * This is different from the VitalSignStatus enum which represents
 * the clinical status (normal, warning, critical)
 */
enum class MonitorReadingStatus {
    VALID,
    UNMONITORED,
    INVALID,
    ERROR;
    
    companion object {
        fun fromString(status: String?): MonitorReadingStatus {
            return when (status?.lowercase()) {
                "valid" -> VALID
                "unmonitored" -> UNMONITORED
                "invalid", "---" -> INVALID
                else -> ERROR
            }
        }
    }
}

/**
 * Type of the clinical event
 */
enum class ClinicalEventType {
    CRITICAL_LOW,
    CRITICAL_HIGH,
    RAPID_CHANGE,
    SUSTAINED_CHANGE
}

/**
 * Represents a clinical event detected in vital signs
 */
data class ClinicalEvent(
    val timestamp: Date,
    val vitalSign: String,
    val type: ClinicalEventType,
    val description: String,
    val value: Double? = null,
    val rate: Double? = null,
    val mannequin: String? = null
)

/**
 * Represents a period when a vital sign was out of compliance with guidelines
 */
data class NonComplianceEvent(
    val startTime: Date,
    val endTime: Date, 
    val vitalSign: String,
    val minThreshold: Double?,
    val maxThreshold: Double?,
    val values: List<Double>,  // The values during this period
    val mannequin: String? = null,
    val scenario: String? = null,
    val description: String = ""
) {
    // Calculated duration in minutes
    val durationMin: Double
        get() = (endTime.time - startTime.time) / (60 * 1000.0)
        
    // Formatted description of the non-compliance
    val formattedDescription: String
        get() {
            val timeFormat = java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.US)
            val startTimeStr = timeFormat.format(startTime)
            val endTimeStr = timeFormat.format(endTime)
            val avgValue = values.average()
            
            return when {
                minThreshold != null && avgValue < minThreshold -> 
                    "$vitalSign was below minimum threshold ($minThreshold): ${String.format("%.1f", avgValue)}"
                maxThreshold != null && avgValue > maxThreshold -> 
                    "$vitalSign exceeded maximum threshold ($maxThreshold): ${String.format("%.1f", avgValue)}"
                else -> 
                    "$vitalSign was out of range"
            } + " from $startTimeStr to $endTimeStr (${String.format("%.1f", durationMin)} min)"
        }
} 