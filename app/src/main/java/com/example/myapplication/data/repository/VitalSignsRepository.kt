package com.example.myapplication.data.repository

import com.example.myapplication.data.model.ActiveNonComplianceEvent
import com.example.myapplication.data.model.Annotation
import com.example.myapplication.data.model.AnnotationType
import com.example.myapplication.data.model.ClinicalEvent
import com.example.myapplication.data.model.MannequinConfig
import com.example.myapplication.data.model.NonComplianceEvent
import com.example.myapplication.data.model.VitalSign
import com.example.myapplication.data.source.SimulatedVitalSignsDataSource
import com.example.myapplication.data.source.VitalSignsDataSource
import com.example.myapplication.data.source.ZollMonitorDataSource
import com.example.myapplication.data.source.JsonVitalSignsDataSource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import java.io.File
import java.util.Date
import java.util.concurrent.ConcurrentHashMap
import com.example.myapplication.data.util.SimulationTimeDetector
import android.content.Context
import kotlinx.coroutines.SupervisorJob
import android.net.Uri
import androidx.documentfile.provider.DocumentFile
import java.io.BufferedReader
import java.io.InputStreamReader
import com.example.myapplication.data.Constants
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale
import com.example.myapplication.data.SimulationScenarios
import com.example.myapplication.data.SimulationDurations
import com.example.myapplication.data.CourseDates
import com.example.myapplication.data.db.VitalSignsDatabase
import com.example.myapplication.data.source.DatabaseVitalSignsDataSource
import kotlinx.coroutines.flow.first
import android.util.Log
import com.example.myapplication.ui.viewmodel.VitalSignsViewModel
import com.example.myapplication.ui.viewmodel.SessionMetadata
import com.example.myapplication.data.model.ScenarioThresholds
import com.example.myapplication.data.model.VitalSignStatus
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.emptyPreferences
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.example.myapplication.ui.screens.MonitoringMode
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.io.IOException
import kotlinx.coroutines.flow.catch
import java.io.InputStream
import com.google.gson.stream.JsonReader
import com.google.gson.stream.JsonToken
import org.json.JSONArray
import org.json.JSONObject

// DataStore instance (top-level property delegate)
private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "monitor_settings")

/**
 * Configuration for a mannequin in a live Zoll SDK monitoring session.
 * This structure will be provided by the UI when starting a live session.
 */
data class LiveSessionMannequinConfig(
    val deviceSerial: String,    // e.g., "AI23F013939"
    val mannequinName: String,   // e.g., "Dave" (friendly name from Constants.MANNEQUIN_MAP)
    val simulationName: String,  // e.g., "Sim1" (from Constants.SIM_MANNEQUINS)
    val scenarioName: String,    // e.g., "TBI" (from Constants.SIM_MANNEQUIN_SCENARIO)
    val simulationType: String = "Training" // e.g., "Training" or "Graded"
)

/**
 * Repository to manage vital signs data from different sources
 */
class VitalSignsRepository(private val context: Context) {
    // Current data source type
    enum class DataSourceType {
        JSON_FILE,
        REAL_TIME_MONITOR,
        SIMULATION,
        WEBSOCKET,
        DATABASE
    }

    // Monitoring state
    enum class MonitoringState {
        STOPPED,
        STARTING,
        RUNNING,
        ERROR
    }

    // Data sources
    private val jsonDataSource = JsonVitalSignsDataSource()
    private val simulatedDataSource = SimulatedVitalSignsDataSource()
    private val zollMonitorDataSource = ZollMonitorDataSource(context)
    private val databaseDataSource = DatabaseVitalSignsDataSource(
        VitalSignsDatabase.getDatabase(context).vitalSignDao()
    )

    // Current data source
    private var currentSource = DataSourceType.SIMULATION

    // Coroutine scope for background jobs
    private val monitoringScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    // Jobs for multiple mannequin monitoring
    private val activeMonitorJobs = mutableMapOf<String, Job>()

    // Monitoring state
    private var monitoringState: MonitoringState = MonitoringState.STOPPED

    // State for vital signs
    private val _vitalSigns = MutableStateFlow<List<VitalSign>>(emptyList())
    val vitalSigns: StateFlow<List<VitalSign>> = _vitalSigns

    // Latest vital sign (for real-time display)
    private val _latestVitalSign = MutableStateFlow<VitalSign?>(null)
    val latestVitalSign: StateFlow<VitalSign?> = _latestVitalSign

    // Settings
    private val _settings = MutableStateFlow(MonitorSettings())
    val settings: StateFlow<MonitorSettings> = _settings

    // Collection of vital signs for all mannequins
    private val _vitalSignsForAllMannequins = MutableStateFlow<List<VitalSign>>(emptyList())
    val vitalSignsForAllMannequins: StateFlow<List<VitalSign>> = _vitalSignsForAllMannequins

    // Latest vital sign for each mannequin (keyed by mannequin name)
    private val _latestVitalSigns = MutableStateFlow<Map<String, VitalSign>>(emptyMap())

    // Latest vital signs by mannequin
    val latestVitalSignsByMannequin: StateFlow<Map<String, VitalSign>> = _latestVitalSigns

    // Detected clinical events
    private val _clinicalEvents = MutableStateFlow<List<ClinicalEvent>>(emptyList())
    val clinicalEvents: StateFlow<List<ClinicalEvent>> = _clinicalEvents

    // Connection status for each mannequin
    private val _connectionStatuses = MutableStateFlow<Map<String, ConnectionStatus>>(emptyMap())
    val connectionStatuses: StateFlow<Map<String, ConnectionStatus>> = _connectionStatuses

    // Overall connection status (for backwards compatibility)
    private val _connectionStatus = MutableStateFlow(ConnectionStatus.DISCONNECTED)
    val connectionStatus: StateFlow<ConnectionStatus> = _connectionStatus

    // Active non-compliance events (key: mannequin+vitalSign)
    private val _activeNonComplianceEvents = MutableStateFlow<Map<String, ActiveNonComplianceEvent>>(emptyMap())
    val activeNonComplianceEvents: StateFlow<Map<String, ActiveNonComplianceEvent>> = _activeNonComplianceEvents

    // Completed non-compliance events
    private val _nonComplianceEvents = MutableStateFlow<List<NonComplianceEvent>>(emptyList())
    val nonComplianceEvents: StateFlow<List<NonComplianceEvent>> = _nonComplianceEvents

    // Annotations
    private val _annotations = MutableStateFlow<List<Annotation>>(emptyList())
    val annotations: StateFlow<List<Annotation>> = _annotations

    /**
     * Add an annotation
     * @param annotation The annotation to add
     */
    fun addAnnotation(annotation: Annotation) {
        val currentAnnotations = _annotations.value.toMutableList()
        currentAnnotations.add(annotation)
        _annotations.value = currentAnnotations
    }

    /**
     * Delete an annotation
     * @param annotationId The ID of the annotation to delete
     */
    fun deleteAnnotation(annotationId: String) {
        val currentAnnotations = _annotations.value.toMutableList()
        currentAnnotations.removeIf { it.id == annotationId }
        _annotations.value = currentAnnotations
    }

    /**
     * Get annotations for a specific mannequin
     * @param mannequinName The mannequin name
     * @return List of annotations for the mannequin
     */
    fun getAnnotationsForMannequin(mannequinName: String): List<Annotation> {
        return _annotations.value.filter { it.mannequin == mannequinName }
    }

    /**
     * Get annotations for a specific vital sign
     * @param vitalSign The vital sign name
     * @return List of annotations for the vital sign
     */
    fun getAnnotationsForVitalSign(vitalSign: String): List<Annotation> {
        return _annotations.value.filter { it.vitalSign == vitalSign }
    }

    // DataStore Keys
    private object PreferencesKeys {
        val MONITORING_MODE = stringPreferencesKey("monitoring_mode")
        val LIVE_MONITOR_CONFIGS_JSON = stringPreferencesKey("live_monitor_configs_json")
    }

    /**
     * NEW: Save the selected monitoring mode to DataStore.
     */
    suspend fun saveMonitoringMode(mode: MonitoringMode) {
        context.dataStore.edit { preferences ->
            preferences[PreferencesKeys.MONITORING_MODE] = mode.name
        }
    }

    /**
     * NEW: Load the selected monitoring mode from DataStore.
     * Defaults to SIMULATION if not found or error occurs.
     */
    fun getMonitoringMode(): Flow<MonitoringMode> {
        return context.dataStore.data
            .catch { exception: Throwable ->
                // Handle IO exceptions
                if (exception is IOException) {
                    Log.e("VitalSignsRepository", "Error reading monitoring mode preferences.", exception)
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences: Preferences ->
                val modeName = preferences[PreferencesKeys.MONITORING_MODE] ?: MonitoringMode.SIMULATION.name
                try {
                    MonitoringMode.valueOf(modeName)
                } catch (e: IllegalArgumentException) {
                    // Handle case where saved value is invalid
                    Log.w("VitalSignsRepository", "Invalid monitoring mode found in preferences: $modeName, defaulting to SIMULATION.")
                    MonitoringMode.SIMULATION
                }
            }
    }

    /**
     * NEW: Save the list of live monitor configurations as a JSON string to DataStore.
     */
    suspend fun saveLiveMonitorConfigs(configs: List<LiveSessionMannequinConfig>) { // UPDATED to LiveSessionMannequinConfig
        val jsonString = Gson().toJson(configs)
        context.dataStore.edit { preferences ->
            preferences[PreferencesKeys.LIVE_MONITOR_CONFIGS_JSON] = jsonString
        }
    }

    /**
     * NEW: Load the list of live monitor configurations from DataStore.
     * Returns Flow<List<LiveSessionMannequinConfig>>.
     */
    fun getLiveMonitorConfigs(): Flow<List<LiveSessionMannequinConfig>> { // UPDATED to LiveSessionMannequinConfig
        return context.dataStore.data
            .catch { exception: Throwable ->
                // dataStore.data throws an IOException when an error is encountered when reading data
                if (exception is IOException) {
                    Log.e("VitalSignsRepository", "Error reading live monitor configs preferences.", exception)
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }.map { preferences: Preferences ->
                val jsonString = preferences[PreferencesKeys.LIVE_MONITOR_CONFIGS_JSON]
                if (jsonString.isNullOrEmpty()) {
                    emptyList<LiveSessionMannequinConfig>() // UPDATED type
                } else {
                    try {
                        // Use Gson to deserialize the JSON string back into a list
                        val type = object : TypeToken<List<LiveSessionMannequinConfig>>() {}.type // UPDATED type
                        Gson().fromJson<List<LiveSessionMannequinConfig>>(jsonString, type) ?: emptyList<LiveSessionMannequinConfig>() // UPDATED type
                    } catch (e: Exception) {
                        Log.e("VitalSignsRepository", "Error deserializing live monitor configs from JSON", e)
                        emptyList<LiveSessionMannequinConfig>() // UPDATED type // Return empty list on error
                    }
                }
            }
    }

    /**
     * Load data from a directory of JSON files
     * @param directory The directory containing JSON files
     * @return List of parsed VitalSign objects
     */
    suspend fun loadDataFromDirectory(directory: File): List<VitalSign> {
        currentSource = DataSourceType.JSON_FILE
        val loadedSigns = jsonDataSource.loadDataFromDirectory(directory)
        _vitalSigns.value = loadedSigns
        if (loadedSigns.isNotEmpty()) {
            _latestVitalSign.value = loadedSigns.last()
            // Process loaded data with accurate simulation timeframe detection
            // processLoadedData(true) // Removed - now handled in ViewModel
        }
        return loadedSigns
    }

    /**
     * Load data from a single JSON file
     * @param file The JSON file
     * @return List of parsed VitalSign objects
     */
    suspend fun loadDataFromFile(file: File): List<VitalSign> {
        currentSource = DataSourceType.JSON_FILE
        val loadedSigns = jsonDataSource.loadDataFromFile(file)
        _vitalSigns.value = loadedSigns
        if (loadedSigns.isNotEmpty()) {
            _latestVitalSign.value = loadedSigns.last()
            // Process loaded data with accurate simulation timeframe detection
            // processLoadedData(true) // Removed - now handled in ViewModel
        }
        return loadedSigns
    }

    /**
     * [DEPRECATED] Old startMonitoring based on internal settings. Use the overload accepting mode/configs.
     * @return Flow of VitalSign objects (Potentially misleading for multi-mannequin)
     */
    @Deprecated("Use startMonitoring that accepts mode and specific configurations")
    fun startMonitoring(): Flow<VitalSign> {
        // Set monitoring state to running
        monitoringState = MonitoringState.RUNNING

        when (settings.value.dataSourceType) {
            DataSourceType.SIMULATION -> {
                simulatedDataSource.setUpdateInterval(settings.value.updateIntervalMs)
                currentSource = DataSourceType.SIMULATION

                // Set the overall connection status to CONNECTED
                _connectionStatus.value = ConnectionStatus.CONNECTED

                // Check if we have a selected simulation for multiple mannequins
                if (settings.value.selectedSimulation.isNotEmpty()) {
                    // Get mannequins for this simulation
                    val mannequinConfigs = MannequinConfig.getMannequinConfigsForSimulation(settings.value.selectedSimulation)

                    // Clear previous monitoring jobs
                    stopAllMonitoring()

                    // Create a map to store connection statuses - set all to CONNECTED for simulation
                    val statuses = mutableMapOf<String, ConnectionStatus>()
                    mannequinConfigs.forEach { mannequinInfo ->
                        statuses[mannequinInfo.name] = ConnectionStatus.CONNECTED
                    }
                    _connectionStatuses.value = statuses

                    // Start a simulation job for each mannequin
                    mannequinConfigs.forEach { mannequinInfo ->
                        startSimulationForMannequin(
                            mannequinInfo = mannequinInfo,
                            simulationType = "DeprecatedCall" // Pass default for deprecated path
                        )
                    }

                    // For backward compatibility, return a flow from the first mannequin
                    if (mannequinConfigs.isNotEmpty()) {
                        val firstMannequin = mannequinConfigs.first()
                        simulatedDataSource.setScenario(firstMannequin.scenario)
                        return simulatedDataSource.getRealtimeVitalSigns().map { vitalSign ->
                            val enrichedSign = vitalSign.copy(
                                overrideMannequin = firstMannequin.name,
                                scenario = firstMannequin.scenario
                            )
                            processVitalSign(enrichedSign)
                            enrichedSign
                        }
                    }
                }

                // Fallback to single mannequin simulation
                return simulatedDataSource.getRealtimeVitalSigns().map { vitalSign ->
                    processVitalSign(vitalSign)
                    vitalSign
                }
            }
            DataSourceType.REAL_TIME_MONITOR -> {
                currentSource = DataSourceType.REAL_TIME_MONITOR
                val config = settings.value

                // Check if we have a selected simulation
                if (config.selectedSimulation.isNotEmpty()) {
                    // Get mannequins for this simulation
                    val mannequinConfigs = MannequinConfig.getMannequinConfigsForSimulation(config.selectedSimulation)

                    // Clear previous monitoring jobs
                    stopAllMonitoring()

                    // Create a map to store connection statuses
                    val statuses = mutableMapOf<String, ConnectionStatus>()
                    mannequinConfigs.forEach { mannequinInfo ->
                        statuses[mannequinInfo.name] = ConnectionStatus.CONNECTING
                    }
                    _connectionStatuses.value = statuses
                    _connectionStatus.value = ConnectionStatus.CONNECTING

                    // Start monitoring for each mannequin
                    mannequinConfigs.forEach { mannequinInfo ->
                        startMonitoringForMannequin(
                            mannequinInfo = mannequinInfo,
                            simulationType = "DeprecatedCall" // Pass default for deprecated path
                        )
                    }

                    // For backward compatibility, return the first mannequin's flow if available
                    if (mannequinConfigs.isNotEmpty()) {
                        val firstMannequin = mannequinConfigs.first()
                        // FIX 1: Add context
                        val mannequinZollMonitor = ZollMonitorDataSource(context)

                        mannequinZollMonitor.setUpdateInterval(config.updateIntervalMs)
                        mannequinZollMonitor.setConnectionMode(ZollMonitorDataSource.ConnectionMode.HTTP)

                        // Use stored IP if available, otherwise use default
                        val ipAddress = config.mannequinIpAddresses[firstMannequin.name] ?: firstMannequin.defaultIpAddress

                        mannequinZollMonitor.setConnectionDetails(
                            ipAddress = ipAddress,
                            port = 80,
                            mannequinName = firstMannequin.name
                        )

                        return mannequinZollMonitor.getRealtimeVitalSigns().map { vitalSign ->
                            val enrichedSign = vitalSign.copy(
                                overrideMannequin = firstMannequin.name,
                                scenario = firstMannequin.scenario
                            )
                            processVitalSign(enrichedSign)
                            enrichedSign
                        }
                    } else {
                        // Fallback to simulation if no mannequins
                        currentSource = DataSourceType.SIMULATION
                        _connectionStatus.value = ConnectionStatus.ERROR
                        return simulatedDataSource.getRealtimeVitalSigns().map { vitalSign ->
                            processVitalSign(vitalSign)
                            vitalSign
                        }
                    }
                } else {
                    // Fallback to old single-monitor mode
                    zollMonitorDataSource.setUpdateInterval(config.updateIntervalMs)
                    zollMonitorDataSource.setConnectionMode(ZollMonitorDataSource.ConnectionMode.HTTP)
                    zollMonitorDataSource.setConnectionDetails(
                        ipAddress = config.ipAddress,
                        port = config.port,
                        mannequinName = "Default"
                    )

                    _connectionStatus.value = ConnectionStatus.CONNECTING
                    return zollMonitorDataSource.getRealtimeVitalSigns().map { vitalSign ->
                        processVitalSign(vitalSign)
                        _connectionStatus.value = ConnectionStatus.CONNECTED
                        vitalSign
                    }
                }
            }
            DataSourceType.WEBSOCKET -> {
                currentSource = DataSourceType.WEBSOCKET
                val config = settings.value

                // Configure Zoll monitor data source for WebSocket
                zollMonitorDataSource.setConnectionMode(ZollMonitorDataSource.ConnectionMode.WEBSOCKET)
                zollMonitorDataSource.setConnectionDetails(
                    ipAddress = config.ipAddress,
                    port = config.port,
                    mannequinName = "Default"
                )

                _connectionStatus.value = ConnectionStatus.CONNECTING
                return zollMonitorDataSource.getRealtimeVitalSigns().map { vitalSign ->
                    processVitalSign(vitalSign)
                    _connectionStatus.value = ConnectionStatus.CONNECTED
                    vitalSign
                }
            }
            else -> {
                currentSource = DataSourceType.SIMULATION
                _connectionStatus.value = ConnectionStatus.CONNECTED
                return simulatedDataSource.getRealtimeVitalSigns().map { vitalSign ->
                    processVitalSign(vitalSign)
                    vitalSign
                }
            }
        }
    }

    /**
     * Start monitoring vital signs based on the selected mode and configuration.
     * This is the primary method for initiating monitoring from the UI.
     *
     * @param mode The MonitoringMode (SIMULATION or LIVE_ZOLL) to use.
     * @param liveConfigs The list of LiveSessionMannequinConfig for LIVE_ZOLL mode.
     * @param simulationMannequins The list of mannequin names for SIMULATION mode.
     * @param simulationScenarioMap Map of mannequin name to scenario for SIMULATION mode.
     * @param simulationType Categorization for the session (e.g., "Training", "Graded").
     *
     * @return Boolean indicating if monitoring was started successfully (basic validation).
     * Note: Actual connection status is observed via connectionStatuses Flow.
     */
    fun startMonitoring(
        mode: MonitoringMode,
        liveConfigs: List<LiveSessionMannequinConfig>? = null, // UPDATED to use LiveSessionMannequinConfig
        simulationMannequins: List<String>? = null, // Required for SIMULATION
        simulationScenarioMap: Map<String, String>? = null, // Required for SIMULATION
        simulationType: String = "Training" // Retained for categorization
        // Other params like thresholds/scenario might be needed per mannequin later
    ): Boolean {
        Log.i("VitalSignsRepository", "Attempting to start monitoring in $mode mode with simulationType: $simulationType")

        // --- Pre-checks and State Setup ---
        stopAllMonitoring() // Ensure any previous jobs are cancelled
        monitoringState = MonitoringState.STARTING // Set state to starting

        when (mode) {
            MonitoringMode.SIMULATION -> {
                if (simulationMannequins.isNullOrEmpty() || simulationScenarioMap.isNullOrEmpty()) {
                    Log.e("VitalSignsRepository", "Simulation mode requires mannequin list and scenario map.")
                    monitoringState = MonitoringState.ERROR
                    _connectionStatus.value = ConnectionStatus.ERROR
                    _connectionStatuses.value = emptyMap() // Clear statuses on error
                    return false // Indicate failure
                }

                currentSource = DataSourceType.SIMULATION // Set the underlying source type
                Log.i("VitalSignsRepository", "Starting SIMULATION for mannequins: ${simulationMannequins.joinToString()}")

                // Set connection statuses for selected mannequins
                val statuses = simulationMannequins.associateWith { ConnectionStatus.CONNECTED }.toMutableMap()
                _connectionStatuses.value = statuses
                _connectionStatus.value = ConnectionStatus.CONNECTED // Assume overall connected for simulation

                // Start simulation job for each selected mannequin
                simulationMannequins.forEach { mannequinName ->
                    val mannequinScenario = simulationScenarioMap[mannequinName]
                    if (mannequinScenario != null) {
                        startSimulationForMannequin(
                            MannequinConfig.MannequinInfo( // Construct necessary info
                                name = mannequinName,
                                scenario = mannequinScenario,
                                defaultIpAddress = "" // Not used for simulation
                            ),
                             simulationType = simulationType // Pass simulation type
                        )
                    } else {
                        Log.w("VitalSignsRepository", "Scenario not found for mannequin: $mannequinName")
                        updateMannequinConnectionStatus(mannequinName, ConnectionStatus.ERROR)
                    }
                }
                monitoringState = MonitoringState.RUNNING
                return true // Indicate success
            }

            MonitoringMode.LIVE_ZOLL -> {
                currentSource = DataSourceType.REAL_TIME_MONITOR
                Log.i("VitalSignsRepository", "Starting LIVE_ZOLL SDK-based monitoring.")

                // Initialize statuses for expected mannequins from liveConfigs if provided
                val initialStatuses = liveConfigs?.associate { it.mannequinName to ConnectionStatus.CONNECTING }?.toMutableMap() ?: mutableMapOf()
                _connectionStatuses.value = initialStatuses
                _connectionStatus.value = if (liveConfigs.isNullOrEmpty()) ConnectionStatus.DISCONNECTED else ConnectionStatus.CONNECTING


                // Stop any previous LIVE_ZOLL specific job first
                // activeMonitorJobs["LIVE_ZOLL_SDK_COLLECTOR"]?.cancel() // Done by stopAllMonitoring

                val job = monitoringScope.launch {
                    Log.d("VitalSignsRepository", "LIVE_ZOLL collecting from zollMonitorDataSource.getRealtimeVitalSigns()")
                    try {
                        zollMonitorDataSource.getRealtimeVitalSigns().collect { vitalSign ->
                            // vitalSign.overrideMannequin is the deviceSerial from ZollMonitorDataSource
                            val deviceSerial = vitalSign.overrideMannequin ?: "UnknownSerial"

                            val config = liveConfigs?.find { it.deviceSerial == deviceSerial }

                            val currentMannequinId: String
                            val currentScenario: String
                            val currentSim: String?

                            if (config != null) {
                                currentMannequinId = config.mannequinName
                                currentScenario = config.scenarioName
                                currentSim = config.simulationName
                                Log.d("VitalSignsRepository", "Device $deviceSerial mapped to Mannequin: $currentMannequinId, Sim: $currentSim, Scenario: $currentScenario")
                            } else {
                                currentMannequinId = deviceSerial // Default to serial if no config found
                                currentScenario = "Unknown Live Scenario"
                                currentSim = "Unknown Live Sim"
                                Log.w("VitalSignsRepository", "No LiveSessionMannequinConfig found for device serial: $deviceSerial. Using defaults.")
                            }

                            val enrichedSign = vitalSign.copy(
                                overrideMannequin = currentMannequinId,
                                scenario = currentScenario,
                                sim = currentSim, // Populate sim field
                                simulationType = simulationType // This is passed to startMonitoring
                            )

                            updateMannequinConnectionStatus(currentMannequinId, ConnectionStatus.CONNECTED)

                            // Fetch thresholds based on the now-correct scenario
                            val scenarioThresholds = com.example.myapplication.data.ClinicalPracticeGuidelines.ALL_THRESHOLDS[currentScenario]
                            val thresholdsToApply = scenarioThresholds?.let {
                                ScenarioThresholds.convertToNewFormat(it, currentScenario)
                            } ?: emptyMap()

                            processVitalSignWithThresholds(enrichedSign, thresholdsToApply)
                        }
                    } catch (e: Exception) {
                        Log.e("VitalSignsRepository", "Error collecting from Zoll SDK: ${e.message}", e)
                        _connectionStatus.value = ConnectionStatus.ERROR
                        // Update all known/expected live mannequin statuses to error
                        val errorStatuses = _connectionStatuses.value.toMutableMap()
                        liveConfigs?.forEach { config -> // Iterate over provided configs to mark them as error
                            errorStatuses[config.mannequinName] = ConnectionStatus.ERROR
                        }
                        _connectionStatuses.value = errorStatuses
                    } finally {
                        Log.i("VitalSignsRepository", "LIVE_ZOLL collection ended.")
                        val finalStatuses = _connectionStatuses.value.toMutableMap()
                        liveConfigs?.forEach { config ->
                            // Only mark as DISCONNECTED if it wasn't an ERROR state already
                            if (finalStatuses[config.mannequinName] != ConnectionStatus.ERROR) {
                                finalStatuses[config.mannequinName] = ConnectionStatus.DISCONNECTED
                            }
                        }
                        _connectionStatuses.value = finalStatuses
                        _connectionStatus.value = ConnectionStatus.DISCONNECTED
                        monitoringState = MonitoringState.STOPPED // Ensure state is updated
                    }
                }
                activeMonitorJobs["LIVE_ZOLL_SDK_COLLECTOR"] = job // Store the main collector job

                monitoringState = MonitoringState.RUNNING
                return true
            }
        }
    }

    /**
     * Start monitoring for a specific mannequin (Now primarily for SIMULATION)
     * The LIVE_ZOLL part of this function is deprecated by the new SDK approach.
     */
    private fun startMonitoringForMannequin(
        mannequinInfo: MannequinConfig.MannequinInfo,
        simulationType: String
    ) {
        // This function is now only relevant for settings.value.dataSourceType which is an older way.
        // The new startMonitoring(mode: MonitoringMode, ...) handles SDK directly.
        // For safety, if this is called with REAL_TIME_MONITOR, log warning and do nothing or redirect.
        Log.w("VitalSignsRepository", "startMonitoringForMannequin called - this path is primarily for SIMULATION or deprecated single monitor Zoll.")

        // Create a dedicated ZollMonitorDataSource for this mannequin -- THIS IS THE OLD WAY for single monitor
        // For SDK, we use the shared zollMonitorDataSource instance and its getRealtimeVitalSigns() flow.
        // This specific function (startMonitoringForMannequin) should ideally not be hit for LIVE_ZOLL anymore.
        // If settings.value.dataSourceType = REAL_TIME_MONITOR (old way) and selectedSimulation is used:

        // The following is legacy logic if startMonitoring() (deprecated one) was called with REAL_TIME_MONITOR
        // and it had multiple mannequins for that old non-SDK "real-time" mode.
        // This part will not be executed with the new UI flow using MonitoringMode.LIVE_ZOLL.
        // It's kept for context of what it used to do.

        // val mannequinZollMonitor = ZollMonitorDataSource(context) // Old: new instance per mannequin
        // mannequinZollMonitor.setUpdateInterval(settings.value.updateIntervalMs)
        // mannequinZollMonitor.setConnectionMode(ZollMonitorDataSource.ConnectionMode.HTTP) // Old: hardcoded HTTP
        // val ipAddress = settings.value.mannequinIpAddresses[mannequinInfo.name] ?: mannequinInfo.defaultIpAddress
        // mannequinZollMonitor.setConnectionDetails(
        //     ipAddress = ipAddress,
        //     port = 80, // Old: hardcoded port
        //     mannequinName = mannequinInfo.name
        // )
        // updateMannequinConnectionStatus(mannequinInfo.name, ConnectionStatus.CONNECTING)
        // val job = monitoringScope.launch {
        //     try {
        //         val thresholds: Map<String, ScenarioThresholds.VitalSignThreshold> = emptyMap()
        //         mannequinZollMonitor.getRealtimeVitalSigns().collect { vitalSign -> // Old: called per mannequin
        //             val enrichedSign = vitalSign.copy(
        //                 overrideMannequin = mannequinInfo.name,
        //                 scenario = mannequinInfo.scenario,
        //                 simulationType = simulationType
        //             )
        //             processVitalSignWithThresholds(enrichedSign, thresholds)
        //             updateMannequinConnectionStatus(mannequinInfo.name, ConnectionStatus.CONNECTED)
        //         }
        //     } catch (e: Exception) {
        //         updateMannequinConnectionStatus(mannequinInfo.name, ConnectionStatus.ERROR)
        //     }
        // }
        // activeMonitorJobs[mannequinInfo.name] = job
    }

    /**
     * Start simulation for a specific mannequin
     */
    private fun startSimulationForMannequin(
        mannequinInfo: MannequinConfig.MannequinInfo,
        simulationType: String // Added simulation type parameter
    ) {
        // Start a job to simulate data for this mannequin
        val job = monitoringScope.launch {
            // Get the corresponding scenario
            val scenario = mannequinInfo.scenario

            // Create a dedicated SimulatedVitalSignsDataSource for this mannequin
            val mannequinSimulator = SimulatedVitalSignsDataSource()
            mannequinSimulator.setUpdateInterval(settings.value.updateIntervalMs)

            // Set the scenario for the simulator
            mannequinSimulator.setScenario(scenario)

            try {
                // Set the connection status to CONNECTED for this mannequin
                updateMannequinConnectionStatus(mannequinInfo.name, ConnectionStatus.CONNECTED)

                // Collect the flow of simulated vital signs
                mannequinSimulator.getRealtimeVitalSigns().collect { vitalSign ->
                    // Enrich the vital sign with mannequin and scenario info
                    val enrichedSign = vitalSign.copy(
                        overrideMannequin = mannequinInfo.name,
                        scenario = scenario,
                        simulationType = simulationType // Add simulation type
                    )

                    // Process the vital sign (update latest values, detect events, etc.)
                    updateVitalSignForMannequin(mannequinInfo.name, enrichedSign)
                }
            } catch (e: Exception) {
                // If there's an error, update the connection status
                updateMannequinConnectionStatus(mannequinInfo.name, ConnectionStatus.ERROR)
            } finally {
                // When done, make sure the connection status is updated
                updateMannequinConnectionStatus(mannequinInfo.name, ConnectionStatus.DISCONNECTED)
            }
        }

        // Store the job so we can cancel it later
        activeMonitorJobs[mannequinInfo.name] = job
    }

    /**
     * Update connection status for a mannequin
     */
    private fun updateMannequinConnectionStatus(mannequin: String, status: ConnectionStatus) {
        val currentStatuses = _connectionStatuses.value.toMutableMap()
        currentStatuses[mannequin] = status
        _connectionStatuses.value = currentStatuses

        // Update overall status based on individual statuses
        if (currentStatuses.values.all { it == ConnectionStatus.CONNECTED }) {
            _connectionStatus.value = ConnectionStatus.CONNECTED
        } else if (currentStatuses.values.any { it == ConnectionStatus.ERROR }) {
            _connectionStatus.value = ConnectionStatus.ERROR
        } else if (currentStatuses.values.any { it == ConnectionStatus.CONNECTING }) {
            _connectionStatus.value = ConnectionStatus.CONNECTING
        } else {
            // If not all connected and none are error/connecting, assume disconnected
            // This might need refinement based on desired behavior when some are disconnected
            _connectionStatus.value = ConnectionStatus.DISCONNECTED
        }
    }

    /**
     * Stop monitoring vital signs for all mannequins
     */
    suspend fun stopMonitoring() {
        try {
            // First stop all active monitoring jobs
            stopAllMonitoring()

            // Then stop the specific data source
            when (currentSource) {
                DataSourceType.SIMULATION -> {
                    simulatedDataSource.stopMonitoring()
                }
                DataSourceType.REAL_TIME_MONITOR, DataSourceType.WEBSOCKET -> {
                    zollMonitorDataSource.stopMonitoring()
                }
                else -> {
                    // No action needed
                }
            }

            // Finalize any active non-compliance events
            finalizeActiveNonComplianceEvents()
        } catch (e: Exception) {
            Log.e("VitalSignsRepository", "Error in stopMonitoring", e)
        } finally {
            // Always update UI state even if there's an error

            // Update connection statuses
            val currentKeys = _connectionStatuses.value.keys
            val emptyStatuses = if (currentKeys.isEmpty()) {
                emptyMap()
            } else {
                currentKeys.associateWith { ConnectionStatus.DISCONNECTED }
            }
            _connectionStatuses.value = emptyStatuses
            _connectionStatus.value = ConnectionStatus.DISCONNECTED

            // Set monitoring state to stopped
            monitoringState = MonitoringState.STOPPED
        }
    }

    /**
     * Finalize any active non-compliance events when monitoring stops
     */
    private fun finalizeActiveNonComplianceEvents() {
        try {
            val currentTime = Date()
            val activeEvents = _activeNonComplianceEvents.value

            if (activeEvents.isNotEmpty()) {
                val completedEvents = activeEvents.values.map { it.toNonComplianceEvent(currentTime) }

                // Add to completed events list
                val allCompletedEvents = _nonComplianceEvents.value.toMutableList()
                allCompletedEvents.addAll(completedEvents)
                _nonComplianceEvents.value = allCompletedEvents
            }
        } catch (e: Exception) {
            Log.e("VitalSignsRepository", "Error finalizing non-compliance events", e)
        } finally {
            // Always clear active events, even if there was an error
            _activeNonComplianceEvents.value = emptyMap()
        }
    }



    /**
     * Stop all monitoring jobs
     */
    private fun stopAllMonitoring() { // Make it public or internal if needed elsewhere
        try {
            activeMonitorJobs.forEach { (name, job) ->
                try {
                    job.cancel()
                    Log.d("VitalSignsRepository", "Cancelled job for $name")
                } catch (e: Exception) {
                    Log.e("VitalSignsRepository", "Error cancelling job for $name", e)
                    // Continue with other jobs even if one fails
                }
            }
        } catch (e: Exception) {
            Log.e("VitalSignsRepository", "Error in stopAllMonitoring", e)
        } finally {
            // Always clear the jobs map
            activeMonitorJobs.clear()
        }
    }

    /**
     * Get vital signs by date range
     * @param startDate The start date
     * @param endDate The end date
     * @return List of VitalSign objects
     */
    suspend fun getVitalSignsByDateRange(startDate: Date, endDate: Date): List<VitalSign> {
        return when (currentSource) {
            DataSourceType.JSON_FILE -> {
                _vitalSigns.value.filter { it.timeObj in startDate..endDate }
            }
            else -> {
                emptyList()
            }
        }
    }

    /**
     * Get vital signs by scenario and mannequin
     * @param scenario The scenario name
     * @param mannequin The mannequin name
     * @return List of VitalSign objects
     */
    suspend fun getVitalSignsByScenarioAndMannequin(scenario: String, mannequin: String): List<VitalSign> {
        return _vitalSigns.value.filter {
            it.scenario == scenario && it.overrideMannequin == mannequin
        }
    }

    /**
     * Update the monitor settings
     * @param settings The new settings
     */
    fun updateSettings(settings: MonitorSettings) {
        _settings.value = settings

        // Update data source intervals based on settings
        when (currentSource) {
            DataSourceType.SIMULATION -> {
                simulatedDataSource.setUpdateInterval(settings.updateIntervalMs)
            }
            DataSourceType.REAL_TIME_MONITOR, DataSourceType.WEBSOCKET -> {
                zollMonitorDataSource.setUpdateInterval(settings.updateIntervalMs)
            }
            else -> {
                // No action needed
            }
        }
    }

    /**
     * Update specific monitor settings
     */
    fun updateSettings(
        dataSourceType: DataSourceType = settings.value.dataSourceType,
        scenario: String = settings.value.scenario,
        ipAddress: String = settings.value.ipAddress,
        port: Int = settings.value.port,
        authRequired: Boolean = settings.value.authRequired,
        username: String = settings.value.username,
        password: String = settings.value.password,
        updateIntervalMs: Long = settings.value.updateIntervalMs,
        selectedSimulation: String = settings.value.selectedSimulation,
        mannequinIpAddresses: Map<String, String> = settings.value.mannequinIpAddresses
    ) {
        val newSettings = MonitorSettings(
            dataSourceType = dataSourceType,
            scenario = scenario,
            ipAddress = ipAddress,
            port = port,
            authRequired = authRequired,
            username = username,
            password = password,
            authMethod = settings.value.authMethod,
            updateIntervalMs = updateIntervalMs,
            selectedSimulation = selectedSimulation,
            mannequinIpAddresses = mannequinIpAddresses
        )

        updateSettings(newSettings)
    }

    /**
     * Update the selected simulation
     */
    fun updateSelectedSimulation(simulation: String) {
        val currentSettings = settings.value
        updateSettings(
            selectedSimulation = simulation
        )
    }

    /**
     * Process a new vital sign
     * @param vitalSign The new vital sign
     */
    private suspend fun processVitalSign(vitalSign: VitalSign) {
        val mannequinName = vitalSign.overrideMannequin ?: "Default"

        // For real-time monitoring, mark the vital sign as being within the precise simulation timeframe and sim window
        // Since we're collecting data during an actual simulation run
        val updatedVitalSign = if (monitoringState == MonitoringState.RUNNING) {
            vitalSign.copy(
                inPreciseSimTimeframe = true,
                inSimWindow = true,
                isValid = true, // Also mark as valid since we're receiving it during active monitoring
                sim = vitalSign.scenario // Use scenario as a placeholder for 'sim' identifier if needed
            )
        } else {
            vitalSign
        }

        // Update latest vital sign for this mannequin
        val currentLatestSigns = _latestVitalSigns.value.toMutableMap()
        currentLatestSigns[mannequinName] = updatedVitalSign
        _latestVitalSigns.value = currentLatestSigns

        // Update overall latest vital sign (for backward compatibility)
        _latestVitalSign.value = updatedVitalSign

        // Add to list with a maximum size
        val currentList = _vitalSigns.value.toMutableList()
        currentList.add(updatedVitalSign)

        // Keep only the last 300 readings per mannequin (5 minutes at 1Hz)
        val mannequinReadings = currentList.filter { it.overrideMannequin == mannequinName }
        if (mannequinReadings.size > 300) {
            val oldestToRemove = mannequinReadings.first()
            currentList.remove(oldestToRemove)
        }

        _vitalSigns.value = currentList

        // Detect clinical events
        val scenario = updatedVitalSign.scenario ?: settings.value.scenario
        if (scenario.isNotEmpty() && currentList.size > 1) {
            // Get the previous vital sign for this mannequin
            val previousForMannequin = currentList
                .filter { it.overrideMannequin == mannequinName }
                .sortedBy { it.timeObj }
                .dropLast(1)
                .lastOrNull()

            if (previousForMannequin != null) {
                // Use the appropriate data source for event detection
                val newEvents = when (currentSource) {
                    DataSourceType.REAL_TIME_MONITOR, DataSourceType.WEBSOCKET -> {
                        zollMonitorDataSource.detectClinicalEvents(
                            listOf(previousForMannequin, updatedVitalSign),
                            scenario
                        )
                    }
                    else -> {
                        simulatedDataSource.detectClinicalEvents(
                            listOf(previousForMannequin, updatedVitalSign),
                            scenario
                        )
                    }
                }

                if (newEvents.isNotEmpty()) {
                    val currentEvents = _clinicalEvents.value.toMutableList()
                    currentEvents.addAll(newEvents)

                    // Keep only the last 50 events
                    if (currentEvents.size > 50) {
                        currentEvents.subList(0, currentEvents.size - 50).clear()
                    }

                    _clinicalEvents.value = currentEvents
                }
            }
        }
    }

    /**
     * Clear all data
     */
    fun clearData() {
        _vitalSigns.value = emptyList()
        _latestVitalSign.value = null
        _latestVitalSigns.value = emptyMap()
        _clinicalEvents.value = emptyList()
    }

    /**
     * Connection status enum
     */
    enum class ConnectionStatus {
        DISCONNECTED,
        CONNECTING,
        CONNECTED,
        ERROR
    }

    /**
     * Monitor settings data class
     */
    data class MonitorSettings(
        val dataSourceType: DataSourceType = DataSourceType.SIMULATION,
        val scenario: String = "TBI",
        val ipAddress: String = "",
        val port: Int = 0,
        val authRequired: Boolean = false,
        val username: String = "",
        val password: String = "",
        val authMethod: String = "Basic",
        val updateIntervalMs: Long = 1000,
        // New field for selected simulation
        val selectedSimulation: String = "",
        // Map of mannequin names to their configured IP addresses
        val mannequinIpAddresses: Map<String, String> = emptyMap()
    )

    /**
     * Process loaded data to identify simulation, scenarios, and time windows
     * @param usePreciseTimeframeDetection Whether to use precise timeframe detection
     */

    /**
     * Filter vital signs by selected criteria
     * @param vitalSignsToFilter The pre-filtered list of vital signs (already filtered by actual timeframe)
     * @param simFilter Simulation filter
     * @param dateFilter Date filter
     * @param mannequinFilter Mannequin filter
     * @param startTimeStr Start time filter
     * @param endTimeStr End time filter
     * @param simulationMode Filtering mode (Graded vs Training)
     * @return Filtered list of vital signs
     */
    fun filterVitalSigns(
        vitalSignsToFilter: List<VitalSign>, // Add parameter for pre-filtered list
        simFilter: String? = null,
        dateFilter: String? = null,
        mannequinFilter: String? = null,
        startTimeStr: String? = null,
        endTimeStr: String? = null,
        simulationMode: String = "Graded"
    ): List<VitalSign> {
        // Use the provided pre-filtered vital signs instead of all signs
        val vitalsToProcess = vitalSignsToFilter

        Log.i("RepoFilter_Debug", "Entering filterVitalSigns: Input size=${vitalsToProcess.size}, Sim=${simFilter ?: "N/A"}, Date=${dateFilter ?: "N/A"}, Mannequin=${mannequinFilter ?: "N/A"}, Mode=${simulationMode}")

        // Check if any specific filters are applied
        val noFiltersApplied = simFilter == null && dateFilter == null && mannequinFilter == null &&
            (startTimeStr == null || startTimeStr.isEmpty()) && (endTimeStr == null || endTimeStr.isEmpty())

        // Determine if we're using a specific simulation filter
        val hasSimFilter = simFilter != null && simFilter != "<all>"

        // Apply all filters simultaneously
        val filteredSigns = vitalsToProcess.filter { vitalSign ->
            // First filter by simulation mode (Graded vs Training)
            val modeMatches = when (simulationMode) {
                "Graded" -> vitalSign.simulationType == "Graded" || vitalSign.simulationType == null // Treat null simulationType as Graded for backward compatibility or general data
                "Training" -> vitalSign.simulationType == "Training"
                "<all>" -> true // New: if simulationMode filter is "<all>", then all types match
                else -> true // Default to show all if mode is invalid or not set in filter
            }

            // If mode doesn't match, skip this record immediately
            if (!modeMatches) {
                return@filter false
            }

            // 1. Check simulation match - primary filter
            val simMatches = simFilter == null || simFilter == "<all>" || vitalSign.sim == simFilter
            // *** DETAIL LOG ***
            if (!simMatches) {
                 Log.v("RepoFilter_Debug", "[${vitalSign.timeStr}] FAILED Sim Filter. Expected: '$simFilter', Actual: '${vitalSign.sim}'")
                 return@filter false // Early exit
            }

            // If we have a sim filter and this item doesn't match, skip checking other conditions
            // Removed redundant check - handled above
            // if (hasSimFilter && !simMatches) {
            //     return@filter false
            // }

            // 2. Check date match
            val dateMatches = dateFilter == null || dateFilter == "<all>" || vitalSign.dateStr == dateFilter
            // *** DETAIL LOG ***
            if (!dateMatches) {
                 Log.v("RepoFilter_Debug", "[${vitalSign.timeStr}] FAILED Date Filter. Expected: '$dateFilter', Actual: '${vitalSign.dateStr}'")
                 return@filter false // Early exit
            }

            // 3. Check mannequin match
            val mannequinMatches = mannequinFilter == null || mannequinFilter == "<all>" ||
                                vitalSign.overrideMannequin == mannequinFilter
            // *** DETAIL LOG ***
            if (!mannequinMatches) {
                 Log.v("RepoFilter_Debug", "[${vitalSign.timeStr}] FAILED Mannequin Filter. Expected: '$mannequinFilter', Actual: '${vitalSign.overrideMannequin}'")
                 return@filter false // Early exit
            }

            // 4. Apply Time range filter if specified
            var timeRangeMatches = true
            if (startTimeStr != null && startTimeStr.isNotEmpty()) {
                try {
                    val startTime = parseDateTime(startTimeStr)
                    timeRangeMatches = timeRangeMatches && vitalSign.timeObj >= startTime
                } catch (e: Exception) {
                    println("Error parsing start time: $startTimeStr")
                }
            }
            if (endTimeStr != null && endTimeStr.isNotEmpty()) {
                try {
                    val endTime = parseDateTime(endTimeStr)
                    timeRangeMatches = timeRangeMatches && vitalSign.timeObj <= endTime
                } catch (e: Exception) {
                    println("Error parsing end time: $endTimeStr")
                }
            }
            // *** DETAIL LOG ***
            if (!timeRangeMatches) {
                 Log.v("RepoFilter_Debug", "[${vitalSign.timeStr}] FAILED Time Range Filter. Start: '$startTimeStr', End: '$endTimeStr'")
                 return@filter false // Early exit
            }

            // Remove timeframe checks since data is already filtered by actual timeframe

            // Combine basic filters - If we got here, all individual checks passed
            // val matches = simMatches && dateMatches && mannequinMatches && timeRangeMatches
            Log.v("RepoFilter_Debug", "[${vitalSign.timeStr}] PASSED all filters.")
            return@filter true // Keep this record

            // Debug logging for excluded items - Removed as individual checks now log failures
            // if (!matches && simMatches) { ... }

            // matches
        }

        Log.i("RepoFilter_Debug", "Filtering produced ${filteredSigns.size} records before grouping.")

        // Group by mannequin and sim, then further by time blocks
        val groupedData = if (hasSimFilter) {
            // For a specific sim, group by mannequin and time blocks
            val mannequinGroups = filteredSigns.groupBy { it.overrideMannequin ?: "Unknown" }

            // For each mannequin, further group by time blocks
            val result = mutableListOf<VitalSign>()
            mannequinGroups.forEach { (mannequin, mannequinSigns) ->
                // Get time blocks for this simulation
                val simTimeBlocks = Constants.SIM_SCHEDULES[simFilter] ?: emptyList()

                // Group data by which time block it falls into
                val blockGroups = mutableMapOf<Pair<String, String>, MutableList<VitalSign>>()

                mannequinSigns.forEach { vitalSign ->
                    // Find which time block this vital sign belongs to
                    val timeBlock = findTimeBlock(vitalSign.timeObj, simTimeBlocks)
                    if (timeBlock != null) {
                        val key = Pair(timeBlock.first, timeBlock.second)
                        blockGroups.getOrPut(key) { mutableListOf() }.add(vitalSign)
                    } else {
                        // No matching time block, add to a default group
                        blockGroups.getOrPut(Pair("00:00", "23:59")) { mutableListOf() }.add(vitalSign)
                    }
                }

                // Sort each block group by time and add to result
                blockGroups.values.forEach { blockSigns ->
                    result.addAll(blockSigns.sortedBy { it.timeObj })
                }
            }

            result
        } else {
            // No specific sim selected, group by mannequin and sim
            val mannequinSimGroups = filteredSigns.groupBy { Pair(it.overrideMannequin, it.sim) }

            // Flatten while preserving grouping
            mannequinSimGroups.values.flatten().sortedWith(
                compareBy(
                    { it.overrideMannequin },
                    { it.sim },
                    { it.timeObj }
                )
            )
        }

        // Log how many records per mannequin
        val mannequinCounts = groupedData.groupBy { it.overrideMannequin }.mapValues { it.value.size }
        mannequinCounts.forEach { (mannequin, count) ->
            println("FILTERING - Mannequin: ${mannequin ?: "unspecified"}, Records: $count")
        }

        println("FILTERING - End: Count=${groupedData.size}") // Keep original log
        Log.i("RepoFilter_Debug", "Exiting filterVitalSigns: Final grouped count=${groupedData.size}")

        return groupedData
    }

    /**
     * Check if a time is precisely in the simulation window, accounting for setup and duration
     * This is a more specific version of isInSimTimeWindow that accounts for the actual
     * simulation duration rather than just the scheduled time block
     */
    private fun isPreciselyInSimTimeWindow(date: Date, sim: String): Boolean {
        val timeBlocks = Constants.SIM_SCHEDULES[sim] ?: return false
        val simDuration = SimulationDurations.SIM_DURATIONS[sim] ?: return false

        val calendar = Calendar.getInstance()
        calendar.time = date

        // Extract hour and minute
        val hour = calendar.get(Calendar.HOUR_OF_DAY)
        val minute = calendar.get(Calendar.MINUTE)

        // Convert to minutes since midnight for easier comparison
        val timeMinutes = hour * 60 + minute

        // Check if time is in any of the time blocks, accounting for setup time and duration
        for ((startTime, _) in timeBlocks) {
            // Parse start time
            val startParts = startTime.split(":")

            if (startParts.size == 2) {
                // Convert HH:MM format to minutes
                val blockStartMinutes = startParts[0].toInt() * 60 + startParts[1].toInt()

                // Add setup buffer (10 minutes)
                val setupBufferMin = 10
                val simStartMinutes = blockStartMinutes + setupBufferMin

                // Calculate actual simulation end time (start + duration)
                val simEndMinutes = simStartMinutes + simDuration

                // Check if timestamp falls within the actual simulation period
                if (timeMinutes in simStartMinutes..simEndMinutes) {
                    return true
                }
            }
        }

        return false
    }

    /**
     * Helper function to find which time block a timestamp falls into
     * @param timestamp The timestamp to check
     * @param timeBlocks List of time blocks in HH:MM format pairs
     * @return The matching time block or null if none found
     */
    private fun findTimeBlock(timestamp: Date, timeBlocks: List<Pair<String, String>>): Pair<String, String>? {
        val calendar = Calendar.getInstance()
        calendar.time = timestamp

        // Extract hour and minute
        val hour = calendar.get(Calendar.HOUR_OF_DAY)
        val minute = calendar.get(Calendar.MINUTE)

        // Convert to minutes since midnight
        val timeMinutes = hour * 60 + minute

        // Check each time block
        for (block in timeBlocks) {
            val (startTime, endTime) = block

            // Parse start and end times
            val startParts = startTime.split(":")
            val endParts = endTime.split(":")

            if (startParts.size == 2 && endParts.size == 2) {
                val startMinutes = startParts[0].toInt() * 60 + startParts[1].toInt()
                val endMinutes = endParts[0].toInt() * 60 + endParts[1].toInt()

                // Check if timestamp falls within this block
                if (timeMinutes in startMinutes..endMinutes) {
                    return block
                }
            }
        }

        return null
    }

    /**
     * Get unique values for filtering
     * @param vitalSignsToAnalyze Pre-filtered vital signs to analyze (optional)
     * @param selectedSim Currently selected simulation filter (if any)
     * @param dateFilter Currently selected date filter (if any)
     * @param forceAllMannequins Whether to always show all possible mannequins (ignores date/sim filtering)
     * @param validMannequins Optional list of valid mannequins to restrict the filter values to
     * @param simulationMode Filtering mode (Graded vs Training)
     * @return FilterValues containing lists of unique simulation, date, and mannequin values
     */
    fun getUniqueFilterValues(
        vitalSignsToAnalyze: List<VitalSign>? = null,
        selectedSim: String? = null,
        dateFilter: String? = null,
        forceAllMannequins: Boolean = false,
        validMannequins: List<String>? = null,
        simulationMode: String = "Graded"
    ): FilterValues {
        // Use provided vital signs if available, otherwise use all vital signs
        val vitalsToProcess = vitalSignsToAnalyze ?: _vitalSigns.value

        // Get values from the data
        val dataWithInfo = vitalsToProcess.filter {
            it.sim != null || it.overrideMannequin != null
        }

        // Get unique Sim values from data
        val simValuesFromData = dataWithInfo
            .mapNotNull { it.sim }
            .filter { it.isNotEmpty() }
            .distinct()
            .sorted()

        // Add hardcoded simulations if we have any data, filtered by simulationMode
        val simValues = if (dataWithInfo.isNotEmpty()) {
            // Filter simulations based on the simulationMode
            val hardcodedSims = when (simulationMode) {
                "Graded" -> listOf("Sim1", "Sim2", "Sim3", "Sim4", "Sim5")
                "Training" -> listOf("TrainingSim3", "TrainingSim5")
                else -> listOf("Sim1", "Sim2", "Sim3", "Sim4", "Sim5")
            }

            // Filter data-derived values based on simulationMode
            val filteredSimValues = simValuesFromData.filter { sim ->
                when (simulationMode) {
                    "Graded" -> !sim.startsWith("Training")
                    "Training" -> sim.startsWith("Training")
                    else -> true
                }
            }

            // Combine filtered data-derived values with filtered hardcoded values
            (filteredSimValues + hardcodedSims)
                .distinct()
                .sorted()
        } else {
            // If no data, return hardcoded list based on simulationMode
            when (simulationMode) {
                "Graded" -> listOf("Sim1", "Sim2", "Sim3", "Sim4", "Sim5")
                "Training" -> listOf("TrainingSim3", "TrainingSim5")
                else -> listOf("Sim1", "Sim2", "Sim3", "Sim4", "Sim5")
            }
        }

        // Get unique Date values - ensure non-null and non-empty
        val dateValues = dataWithInfo
            .mapNotNull { it.dateStr }
            .filter { it.isNotEmpty() }
            .distinct()
            .sorted()

        // Get unique Simulation Type values from data
        val simulationTypeValuesFromData = dataWithInfo
            .mapNotNull { it.simulationType }
            .filter { it.isNotEmpty() }
            .distinct()
            .sorted()

        // Ensure "Training" and "Graded" are always options, combined with data-derived types
        val allSimulationTypeValues = (simulationTypeValuesFromData + listOf("Training", "Graded"))
            .distinct()
            .sorted()

        // If validMannequins is provided, use those instead of deriving from data
        if (validMannequins != null) {
            println("Using provided valid mannequins: $validMannequins")
            return FilterValues(simValues, dateValues, validMannequins, allSimulationTypeValues)
        }

        // Get unique Mannequin values from data
        val mannequinValuesFromData = dataWithInfo
            .mapNotNull { it.overrideMannequin }
            .filter { it.isNotEmpty() }
            .distinct()
            .sorted()

        // If we want to force showing all mannequins, skip filtering
        if (forceAllMannequins) {
            // Include ALL standard mannequins and any others from the data
            val allStandardMannequins = listOf("Dave", "Chuck", "Freddy", "Oscar", "Matt")
            val combinedMannequins = (mannequinValuesFromData + allStandardMannequins).distinct().sorted()

            println("Force showing all mannequins: $combinedMannequins")
            return FilterValues(simValues, dateValues, combinedMannequins, allSimulationTypeValues)
        }

        // Get mannequins specific to selected sim or all detected sims
        val relevantMannequinValues = if (selectedSim != null && selectedSim != "<all>") {
            // Case 1: Get mannequins for specific selected simulation
            val mannequinsForSim = when (selectedSim) {
                "Sim1" -> listOf("Dave", "Chuck")
                "Sim2" -> listOf("Freddy", "Oscar")
                "Sim3" -> listOf("Dave", "Chuck")
                "Sim4" -> listOf("Dave", "Chuck")
                "Sim5" -> listOf("Freddy", "Oscar", "Matt")
                else -> emptyList()
            }

            // Add data-derived mannequins that match this sim
            val dataFilteredMannequins = dataWithInfo
                .filter { it.sim == selectedSim }
                .mapNotNull { it.overrideMannequin }
                .filter { it.isNotEmpty() }
                .distinct()

            // Combine with mapping mannequins
            (dataFilteredMannequins + mannequinsForSim).distinct().sorted()
        } else {
            // Case 2: No specific sim selected (<all>), show ALL possible mannequins
            // Define all standard mannequins used across all simulations
            val allStandardMannequins = listOf("Dave", "Chuck", "Freddy", "Oscar", "Matt")

            // Combine standard mannequins with any from the data
            (mannequinValuesFromData + allStandardMannequins).distinct().sorted()
        }

        println("Filter values - Sims: $simValues, Dates: $dateValues, Mannequins: $relevantMannequinValues, Selected Sim: $selectedSim")

        return FilterValues(simValues, dateValues, relevantMannequinValues, allSimulationTypeValues)
    }

    /**
     * Calculate statistical summary for vital signs
     * For each vital sign, calculate stats by mannequin
     */
    fun calculateStatsSummary(vitalSigns: List<VitalSign>): Map<String, StatsSummary> {
        val stats = mutableMapOf<String, StatsSummary>()

        // First group by mannequin
        val groupedByMannequin = vitalSigns.groupBy { it.overrideMannequin ?: "Unknown" }

        // Define vital signs to analyze
        val vitalsToAnalyze = listOf(
            "Hr" to { v: VitalSign -> v.hr },
            "SpO2" to { v: VitalSign -> v.spO2 },
            "NIBP_SYS" to { v: VitalSign -> v.nibpSys },
            "NIBP_DIA" to { v: VitalSign -> v.nibpDia },
            "NIBP_MAP" to { v: VitalSign -> v.nibpMap },
            "Temp1" to { v: VitalSign -> v.temp1 },
            "RespRate" to { v: VitalSign -> v.respRate },
            "EtCO2" to { v: VitalSign -> v.etCO2 }
        )

        // Calculate overall stats (across all mannequins)
        for ((vitalName, valueExtractor) in vitalsToAnalyze) {
            // Filter valid values
            val values = vitalSigns
                .mapNotNull { valueExtractor(it) }
                .filter { !it.isNaN() && it.isFinite() }

            if (values.isNotEmpty()) {
                val mean = values.average()
                val min = values.minOrNull() ?: 0.0
                val max = values.maxOrNull() ?: 0.0
                val count = values.size

                // Calculate standard deviation
                val variance = if (values.size > 1) {
                    values.map { (it - mean) * (it - mean) }.sum() / (values.size - 1)
                } else {
                    0.0
                }
                val stdDev = kotlin.math.sqrt(variance)

                stats[vitalName] = StatsSummary(count, mean, min, max, stdDev)
            }
        }

        return stats
    }

    /**
     * Get the course for a given date
     * @param date The date
     * @return The course label, or null if not found
     */
    private fun getCourseForDate(date: Date): String? {
        val calendar = Calendar.getInstance()
        calendar.time = date

        // Extract month and day
        val month = calendar.get(Calendar.MONTH) + 1  // Calendar.MONTH is 0-based
        val day = calendar.get(Calendar.DAY_OF_MONTH)

        // Format as MM/dd
        val dateStr = String.format("%02d/%02d", month, day)

        // Check all course date ranges
        for ((startDate, endDate, courseName) in CourseDates.COURSE_DATE_RANGES) {
            if (isDateInRange(dateStr, startDate, endDate)) {
                return courseName
            }
        }

        return null
    }

    /**
     * Check if a date string is in a date range
     * @param dateStr The date string (MM/dd)
     * @param startDate The start date string (MM/dd)
     * @param endDate The end date string (MM/dd)
     * @return True if the date is in the range, false otherwise
     */
    private fun isDateInRange(dateStr: String, startDate: String, endDate: String): Boolean {
        // Parse the dates - we're handling ranges that could cross year boundaries
        val dateMonth = dateStr.split("/")[0].toInt()
        val dateDay = dateStr.split("/")[1].toInt()

        val startMonth = startDate.split("/")[0].toInt()
        val startDay = startDate.split("/")[1].toInt()

        val endMonth = endDate.split("/")[0].toInt()
        val endDay = endDate.split("/")[1].toInt()

        // Check if the date is within the range
        return if (startMonth <= endMonth) {
            // Normal case (within same year)
            (dateMonth > startMonth || (dateMonth == startMonth && dateDay >= startDay)) &&
            (dateMonth < endMonth || (dateMonth == endMonth && dateDay <= endDay))
        } else {
            // Handle year boundary crossing (like 12/15 to 01/15)
            (dateMonth > startMonth || (dateMonth == startMonth && dateDay >= startDay)) ||
            (dateMonth < endMonth || (dateMonth == endMonth && dateDay <= endDay))
        }
    }

    private fun parseDateTime(dateTimeStr: String): Date {
        val formats = listOf(
            "yyyy-MM-dd HH:mm",
            "yyyy-MM-dd HH:mm:ss",
            "yyyy-MM-dd'T'HH:mm:ss"
        )

        for (format in formats) {
            try {
                val sdf = java.text.SimpleDateFormat(format, java.util.Locale.US)
                return sdf.parse(dateTimeStr) ?: throw Exception("Failed to parse date")
            } catch (e: Exception) {
                // Try next format
            }
        }

        throw Exception("Failed to parse date: $dateTimeStr")
    }

    private fun getSimForCourseDate(course: String, date: Date): String? {
        // Get course start date from our mapping
        val startDateStr = CourseDates.COURSE_START_DATES[course] ?: return null

        try {
            // Parse start date
            val sdf = SimpleDateFormat("yyyy-MM-dd", Locale.US)
            val startDate = sdf.parse(startDateStr) ?: return null

            // Calculate day offset
            val daysDiff = ((date.time - startDate.time) / (24 * 60 * 60 * 1000)).toInt()

            // Get sim for this day offset
            return CourseDates.DAY_OFFSET_TO_SIM[daysDiff]
        } catch (e: Exception) {
            e.printStackTrace()
        return null
        }
    }

    private fun getSimFromTimeOfDay(date: Date): String? {
        val calendar = Calendar.getInstance()
        calendar.time = date

        // Extract hour and minute
        val hour = calendar.get(Calendar.HOUR_OF_DAY)
        val minute = calendar.get(Calendar.MINUTE)

        // Convert to minutes since midnight for easier comparison
        val timeMinutes = hour * 60 + minute

        // Check each sim's schedule
        for ((sim, timeBlocks) in Constants.SIM_SCHEDULES) {
            for ((startTime, endTime) in timeBlocks) {
                // Convert HH:MM format to minutes
                val startParts = startTime.split(":")
                val endParts = endTime.split(":")

                if (startParts.size == 2 && endParts.size == 2) {
                    val startMinutes = startParts[0].toInt() * 60 + startParts[1].toInt()
                    val endMinutes = endParts[0].toInt() * 60 + endParts[1].toInt()

                    if (timeMinutes in startMinutes..endMinutes) {
                        return sim
                    }
                }
            }
        }

        return null
    }

    private fun isInSimTimeWindow(date: Date, sim: String): Boolean {
        val timeBlocks = Constants.SIM_SCHEDULES[sim] ?: return false

        val calendar = Calendar.getInstance()
        calendar.time = date

        // Extract hour and minute
        val hour = calendar.get(Calendar.HOUR_OF_DAY)
        val minute = calendar.get(Calendar.MINUTE)

        // Convert to minutes since midnight for easier comparison
        val timeMinutes = hour * 60 + minute

        // Check if time is in any of the time blocks
        for ((startTime, endTime) in timeBlocks) {
            // Convert HH:MM format to minutes
            val startParts = startTime.split(":")
            val endParts = endTime.split(":")

            if (startParts.size == 2 && endParts.size == 2) {
                val startMinutes = startParts[0].toInt() * 60 + startParts[1].toInt()
                val endMinutes = endParts[0].toInt() * 60 + endParts[1].toInt()

                if (timeMinutes in startMinutes..endMinutes) {
        return true
                }
            }
        }

        return false
    }

    private fun getScenario(mannequin: String?, sim: String?): String? {
        if (mannequin == null || sim == null) return null

        // Get the mannequin-to-scenario mapping for this sim
        val scenarioMap = SimulationScenarios.ALL_SCENARIOS[sim] ?: return null

        // Get scenario for this mannequin
        return scenarioMap[mannequin]
    }

    /**
     * Filter values data class
     */
    data class FilterValues(
        val simValues: List<String>,
        val dateValues: List<String>,
        val mannequinValues: List<String>,
        val simulationTypeValues: List<String> // Added field
    )

    /**
     * Statistical summary data class
     */
    data class StatsSummary(
        val count: Int,
        val mean: Double,
        val min: Double,
        val max: Double,
        val stdDev: Double
    )

    /**
     * Update a vital sign for a specific mannequin
     * @param mannequinName The name of the mannequin
     * @param vitalSign The vital sign to update
     */
    private fun updateVitalSignForMannequin(mannequinName: String, vitalSign: VitalSign) {
        // Simplified: Directly process the vital sign. Assume caller ensures correct tags.
        monitoringScope.launch {
            processVitalSign(vitalSign)
        }
    }

    /**
     * Load data from a directory URI
     * @param directoryUri The URI of the directory containing JSON files
     * @param context Android context for content resolver
     * @return List of parsed VitalSign objects
     */
    suspend fun loadDataFromDocumentUri(directoryUri: Uri, context: Context): List<VitalSign> {
        currentSource = DataSourceType.JSON_FILE
        val allVitalSigns = mutableListOf<VitalSign>()

        try {
            // Get directory as a DocumentFile
            val directoryDoc = DocumentFile.fromTreeUri(context, directoryUri)

            if (directoryDoc == null) {
                throw Exception("Failed to access directory at $directoryUri")
            }

            if (!directoryDoc.exists()) {
                throw Exception("Directory does not exist at $directoryUri")
            }

            if (!directoryDoc.isDirectory) {
                throw Exception("Uri does not point to a directory: $directoryUri")
            }

            // Find all JSON files in the directory
            val jsonFiles = directoryDoc.listFiles().filter {
                it.isFile && it.name?.endsWith(".json", ignoreCase = true) == true
            }

            if (jsonFiles.isEmpty()) {
                throw Exception("No JSON files found in the selected directory")
            }

            // Load data from each JSON file
            for (jsonFile in jsonFiles) {
                try {
                    Log.d("VitalSignsRepository", "Processing file: ${jsonFile.name}")
                    val loadedSigns = loadDataFromDocumentFile(jsonFile, context)
                    Log.d("VitalSignsRepository", "Loaded ${loadedSigns.size} records from ${jsonFile.name}")
                    allVitalSigns.addAll(loadedSigns)
                } catch (e: Exception) {
                    Log.e("VitalSignsRepository", "Error loading from ${jsonFile.name}: ${e.message}")
                    // Continue with other files even if one fails
                }
            }

            if (allVitalSigns.isEmpty()) {
                throw Exception("Could not parse any valid vital sign data from the JSON files")
            }

            _vitalSigns.value = allVitalSigns
            _latestVitalSign.value = allVitalSigns.lastOrNull()

            // Process loaded data with accurate simulation timeframe detection
            // processLoadedData(true)
        } catch (e: Exception) {
            Log.e("VitalSignsRepository", "Error processing directory URI", e)
            throw e // Re-throw to allow the ViewModel to handle it
        }

        return allVitalSigns
    }

    /**
     * Load data from a file URI
     * @param fileUri The URI of the JSON file
     * @param context Android context for content resolver
     * @return List of parsed VitalSign objects
     */
    suspend fun loadDataFromFileUri(fileUri: Uri, context: Context): List<VitalSign> {
        currentSource = DataSourceType.JSON_FILE

        try {
            // Get file as a DocumentFile
            val fileDoc = DocumentFile.fromSingleUri(context, fileUri)

            if (fileDoc == null) {
                throw Exception("Failed to access file at $fileUri")
            }

            if (!fileDoc.exists()) {
                throw Exception("File does not exist at $fileUri")
            }

            if (fileDoc.isDirectory) {
                throw Exception("Uri points to a directory, not a file: $fileUri")
            }

            // Check if it's a JSON file
            if (fileDoc.name?.endsWith(".json", ignoreCase = true) != true) {
                throw Exception("Selected file is not a JSON file: ${fileDoc.name}")
            }

            Log.d("VitalSignsRepository", "Processing file: ${fileDoc.name}")

            // Use our streaming implementation to handle large files
            val loadedSigns = loadDataFromDocumentFile(fileDoc, context)

            Log.d("VitalSignsRepository", "Loaded ${loadedSigns.size} records from ${fileDoc.name}")

            if (loadedSigns.isEmpty()) {
                throw Exception("Could not parse any valid vital sign data from the JSON file")
            }

            _vitalSigns.value = loadedSigns
            _latestVitalSign.value = loadedSigns.lastOrNull()

            // Process loaded data with accurate simulation timeframe detection
            // processLoadedData(true)

            return loadedSigns
        } catch (e: Exception) {
            Log.e("VitalSignsRepository", "Error loading file URI", e)
            throw e // Re-throw to allow the ViewModel to handle it
        }
    }

    /**
     * Load data from a DocumentFile
     * @param fileDoc The DocumentFile representing a JSON file
     * @param context Android context for content resolver
     * @return List of parsed VitalSign objects
     */
    private fun loadDataFromDocumentFile(fileDoc: DocumentFile, context: Context): List<VitalSign> {
        if (!fileDoc.exists()) {
            throw Exception("File does not exist: ${fileDoc.uri}")
        }

        if (fileDoc.isDirectory) {
            throw Exception("Expected a file but got a directory: ${fileDoc.uri}")
        }

        try {
            // Stream and parse JSON content instead of loading it all at once
            val inputStream = context.contentResolver.openInputStream(fileDoc.uri)
                ?: throw Exception("Could not open input stream for file: ${fileDoc.uri}")

            // Parse the JSON stream
            return parseJsonStream(inputStream, fileDoc.name ?: "unknown.json")
        } catch (e: Exception) {
            Log.e("VitalSignsRepository", "Error reading JSON file", e)
            throw Exception("Error reading JSON file: ${e.message}")
        }
    }

    /**
     * Parse JSON from an input stream using a true streaming approach to avoid memory issues
     * @param inputStream The input stream containing JSON data
     * @param fileName The name of the file (for logging/debugging)
     * @return List of parsed VitalSign objects
     */
    private fun parseJsonStream(inputStream: InputStream, fileName: String): List<VitalSign> {
        val vitalSigns = mutableListOf<VitalSign>()
        val startTime = System.currentTimeMillis()
        var recordCount = 0

        try {
            Log.d("VitalSignsRepository", "Starting to parse JSON stream from $fileName")

            // Use JsonReader for true streaming JSON parsing
            val reader = JsonReader(InputStreamReader(inputStream, "UTF-8"))
            reader.setLenient(true) // More tolerant of malformed JSON

            try {
                var deviceSerial: String? = null

                // Fast-track parsing for ZOLL format
                fastTrackParse(reader, fileName) { trendRpt, foundDeviceSerial ->
                    // Update device serial if found
                    if (foundDeviceSerial != null) {
                        deviceSerial = foundDeviceSerial
                    }

                    // Process trend report if found
                    if (trendRpt != null) {
                        jsonDataSource.parseTrendReport(trendRpt, deviceSerial, fileName)?.let { vitalSign ->
                            vitalSigns.add(vitalSign)
                            recordCount++

                            // Log progress periodically
                            if (recordCount % 500 == 0) {
                                val elapsedSeconds = (System.currentTimeMillis() - startTime) / 1000
                                Log.d("VitalSignsRepository", "Parsed $recordCount records from $fileName in ${elapsedSeconds}s")
                            }
                        }
                    }
                }
            } finally {
                reader.close()
            }

            val totalTime = (System.currentTimeMillis() - startTime) / 1000
            Log.d("VitalSignsRepository", "Finished parsing $fileName: $recordCount records in ${totalTime}s")

            if (vitalSigns.isEmpty()) {
                Log.w("VitalSignsRepository", "No vital signs parsed from $fileName")
            }

            return vitalSigns

        } catch (e: OutOfMemoryError) {
            Log.e("VitalSignsRepository", "Out of memory while parsing $fileName", e)
            throw Exception("File too large to process: ${e.message}")
        } catch (e: Exception) {
            Log.e("VitalSignsRepository", "Error parsing JSON stream from $fileName", e)
            throw Exception("Error reading JSON file: ${e.message}")
        }
    }

    /**
     * Fast-track parse for ZOLL format JSON - optimized for speed and memory
     *
     * @param reader The JsonReader to read from
     * @param fileName The name of the file being processed (for logging)
     * @param onFound Callback when a TrendRpt or DeviceSerial is found
     */
    private fun fastTrackParse(
        reader: JsonReader,
        fileName: String,
        onFound: (trendRpt: JSONObject?, deviceSerial: String?) -> Unit
    ) {
        try {
            // Start at the top level
            reader.beginObject()

            while (reader.hasNext()) {
                val topName = reader.nextName()

                // We're only interested in the ZOLL object
                if (topName == "ZOLL") {
                    reader.beginObject()

                    while (reader.hasNext()) {
                        val zollField = reader.nextName()

                        // We're only interested in FullDisclosure
                        if (zollField == "FullDisclosure") {
                            reader.beginArray()

                            while (reader.hasNext()) {
                                reader.beginObject()

                                while (reader.hasNext()) {
                                    val disclosureField = reader.nextName()

                                    // We're only interested in FullDisclosureRecord
                                    if (disclosureField == "FullDisclosureRecord") {
                                        reader.beginArray()

                                        while (reader.hasNext()) {
                                            reader.beginObject()

                                            var currentTrendRpt: JSONObject? = null
                                            var currentDeviceSerial: String? = null

                                            while (reader.hasNext()) {
                                                val recordField = reader.nextName()

                                                when (recordField) {
                                                    "DeviceConfiguration" -> {
                                                        reader.beginObject()

                                                        while (reader.hasNext()) {
                                                            val configField = reader.nextName()

                                                            if (configField == "DeviceSerialNumber") {
                                                                currentDeviceSerial = reader.nextString()
                                                            } else {
                                                                reader.skipValue()
                                                            }
                                                        }

                                                        reader.endObject()
                                                    }
                                                    "TrendRpt" -> {
                                                        // Efficiently capture TrendRpt as JSONObject
                                                        currentTrendRpt = captureObject(reader)
                                                    }
                                                    else -> {
                                                        reader.skipValue()
                                                    }
                                                }
                                            }

                                            reader.endObject()

                                            // Call back with what we found for this record
                                            onFound(currentTrendRpt, currentDeviceSerial)
                                        }

                                        reader.endArray() // End FullDisclosureRecord
                                    } else {
                                        reader.skipValue()
                                    }
                                }

                                reader.endObject()
                            }

                            reader.endArray() // End FullDisclosure
                        } else {
                            reader.skipValue()
                        }
                    }

                    reader.endObject() // End ZOLL
                } else {
                    reader.skipValue()
                }
            }

            reader.endObject() // End top-level object
        } catch (e: Exception) {
            Log.e("VitalSignsRepository", "Error in fastTrackParse: ${e.message}")
            throw e
        }
    }

    /**
     * Efficiently capture a JSON object from a JsonReader without the overhead of building
     * a full object tree for parts we don't need
     */
    private fun captureObject(reader: JsonReader): JSONObject {
        val jsonObject = JSONObject()

        reader.beginObject()

        while (reader.hasNext()) {
            val name = reader.nextName()

            when (name) {
                "StdHdr" -> {
                    // We need StdHdr for datetime
                    val stdHdr = JSONObject()
                    reader.beginObject()

                    while (reader.hasNext()) {
                        val hdrField = reader.nextName()

                        if (hdrField == "DevDateTime") {
                            stdHdr.put(hdrField, reader.nextString())
                        } else {
                            reader.skipValue()
                        }
                    }

                    reader.endObject()
                    jsonObject.put("StdHdr", stdHdr)
                }
                "Trend" -> {
                    // We need Trend for vital signs
                    jsonObject.put("Trend", captureFullObject(reader))
                }
                else -> {
                    reader.skipValue()
                }
            }
        }

        reader.endObject()
        return jsonObject
    }

    /**
     * Capture a complete JSON object structure (used for Trend data which we need entirely)
     */
    private fun captureFullObject(reader: JsonReader): JSONObject {
        val result = JSONObject()

        reader.beginObject()

        while (reader.hasNext()) {
            val name = reader.nextName()
            when (reader.peek()) {
                JsonToken.BEGIN_ARRAY -> result.put(name, captureArray(reader))
                JsonToken.BEGIN_OBJECT -> result.put(name, captureFullObject(reader))
                JsonToken.BOOLEAN -> result.put(name, reader.nextBoolean())
                JsonToken.NULL -> { reader.nextNull(); result.put(name, JSONObject.NULL) }
                JsonToken.NUMBER -> result.put(name, parseNumber(reader))
                JsonToken.STRING -> result.put(name, reader.nextString())
                else -> reader.skipValue()
            }
        }

        reader.endObject()
        return result
    }

    /**
     * Capture a JSON array
     */
    private fun captureArray(reader: JsonReader): JSONArray {
        val jsonArray = JSONArray()

        reader.beginArray()

        while (reader.hasNext()) {
            when (reader.peek()) {
                JsonToken.BEGIN_ARRAY -> jsonArray.put(captureArray(reader))
                JsonToken.BEGIN_OBJECT -> jsonArray.put(captureFullObject(reader))
                JsonToken.BOOLEAN -> jsonArray.put(reader.nextBoolean())
                JsonToken.NULL -> { reader.nextNull(); jsonArray.put(JSONObject.NULL) }
                JsonToken.NUMBER -> jsonArray.put(parseNumber(reader))
                JsonToken.STRING -> jsonArray.put(reader.nextString())
                else -> reader.skipValue()
            }
        }

        reader.endArray()
        return jsonArray
    }

    /**
     * Parse a number from JsonReader
     */
    private fun parseNumber(reader: JsonReader): Number {
        val stringValue = reader.nextString()

        return try {
            when {
                stringValue.contains(".") -> stringValue.toDouble()
                stringValue.toLong() in Int.MIN_VALUE..Int.MAX_VALUE -> stringValue.toInt()
                else -> stringValue.toLong()
            }
        } catch (e: NumberFormatException) {
            0
        }
    }

    /**
     * Consolidate vital signs from the same device at the same timestamp
     * This combines fragmented readings into a single comprehensive vital sign
     */
    private fun consolidateVitalSigns(vitalSigns: List<VitalSign>): List<VitalSign> {
        // Group by device serial and timestamp
        val groupedSigns = vitalSigns.groupBy {
            Pair(it.deviceSerial ?: "unknown", it.timeObj.time)
        }

        return groupedSigns.map { (_, signs) ->
            // If only one reading, return it directly
            if (signs.size == 1) {
                return@map signs.first()
            }

            // Otherwise, combine all readings
            var merged = signs.first()

            for (i in 1 until signs.size) {
                val current = signs[i]

                // Take non-null values from current sign
                merged = merged.copy(
                    hr = current.hr ?: merged.hr,
                    spO2 = current.spO2 ?: merged.spO2,
                    nibpSys = current.nibpSys ?: merged.nibpSys,
                    nibpDia = current.nibpDia ?: merged.nibpDia,
                    nibpMap = current.nibpMap ?: merged.nibpMap,
                    temp1 = current.temp1 ?: merged.temp1,
                    respRate = current.respRate ?: merged.respRate,
                    etCO2 = current.etCO2 ?: merged.etCO2,
                    fiCO2 = current.fiCO2 ?: merged.fiCO2,
                    spMet = current.spMet ?: merged.spMet,
                    spCo = current.spCo ?: merged.spCo,
                    pvi = current.pvi ?: merged.pvi,
                    pi = current.pi ?: merged.pi,
                    spOC = current.spOC ?: merged.spOC,
                    spHb = current.spHb ?: merged.spHb,
                    ibp1Sys = current.ibp1Sys ?: merged.ibp1Sys,
                    ibp1Dia = current.ibp1Dia ?: merged.ibp1Dia,
                    ibp1Map = current.ibp1Map ?: merged.ibp1Map,

                    // Also merge status values
                    hrStatus = current.hrStatus ?: merged.hrStatus,
                    spO2Status = current.spO2Status ?: merged.spO2Status,
                    nibpSysStatus = current.nibpSysStatus ?: merged.nibpSysStatus,
                    nibpDiaStatus = current.nibpDiaStatus ?: merged.nibpDiaStatus,
                    nibpMapStatus = current.nibpMapStatus ?: merged.nibpMapStatus,
                    temp1Status = current.temp1Status ?: merged.temp1Status,
                    respRateStatus = current.respRateStatus ?: merged.respRateStatus,
                    etCO2Status = current.etCO2Status ?: merged.etCO2Status
                )
            }

            merged
        }
    }

    // Helper method to determine sim from mannequin
    private fun getSimFromMannequin(mannequinName: String, date: Date): String? {
        // Get all sims that use this mannequin
        val possibleSims = SimulationScenarios.SIM_MANNEQUINS
            .filter { it.value.contains(mannequinName) }
            .keys

        // If there's only one sim, use it
        if (possibleSims.size == 1) {
            return possibleSims.first()
        }

        // Try to narrow down by checking time windows
        for (sim in possibleSims) {
            if (isInSimTimeWindow(date, sim)) {
                return sim
            }
        }

        // Default to first sim that uses this mannequin
        return possibleSims.firstOrNull()
    }

    // Helper method to determine sim from date
    private fun getSimFromDate(date: Date): String? {
        // Check current courses and simulation assignments
        return getCourseForDate(date)?.let { course ->
            getSimForCourseDate(course, date)
        }
    }

    // Get the scenario for a simulation and mannequin directly from the mapping
    private fun getScenarioFromMapping(sim: String, mannequin: String): String? {
        // Use the constant mapping from the provided information
        return when (sim) {
            "Sim1" -> when (mannequin) {
                "Dave" -> "TBI"
                "Chuck" -> "Sepsis"
                else -> null
            }
            "Sim2" -> when (mannequin) {
                "Freddy" -> "TBI_unmonitored"
                "Oscar" -> "DCR"
                else -> null
            }
            "Sim3" -> when (mannequin) {
                "Dave" -> "SepsisARDS"
                "Chuck" -> "ACS"
                else -> null
            }
            "Sim4" -> when (mannequin) {
                "Dave" -> "TBI"
                "Chuck" -> "AorticDissectionStroke"
                else -> null
            }
            "Sim5" -> when (mannequin) {
                "Freddy" -> "Burn"
                "Oscar" -> "AFib"
                "Matt" -> "PenetratingThoracoabdominalInjury"
                else -> null
            }
            else -> null
        }
    }

    /**
     * Save current data to database with session metadata
     * @param sessionMetadata Metadata about the monitoring session
     * @return Number of records saved
     */
    suspend fun saveCurrentDataToDatabase(sessionMetadata: SessionMetadata): Int {
        if (_vitalSigns.value.isEmpty()) {
            return 0
        }

        // Update vital signs with session metadata before saving
        val vitalSignsToSave = _vitalSigns.value.map { vs ->
            vs.copy(
                sim = sessionMetadata.name,
                dateStr = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(sessionMetadata.startTime),
                // Add simulation type and run number as additional metadata
                simType = sessionMetadata.type,
                simRunNumber = sessionMetadata.runNumber
            )
        }

        // Save to database and return the count of records
        databaseDataSource.saveVitalSigns(vitalSignsToSave)
        return vitalSignsToSave.size
    }

    /**
     * Save current vital signs data to the database
     * @return Number of records saved
     */
    suspend fun saveCurrentDataToDatabase(): Int {
        val dataToSave = _vitalSigns.value
        if (dataToSave.isNotEmpty()) {
            databaseDataSource.saveVitalSigns(dataToSave)
            return dataToSave.size
        }
        return 0
    }

    /**
     * Load data from the database
     * @return List of vital signs loaded from the database
     */
    suspend fun loadDataFromDatabase(): List<VitalSign> {
        currentSource = DataSourceType.DATABASE
        val loadedSigns = databaseDataSource.getAllVitalSigns().first()
        _vitalSigns.value = loadedSigns
        if (loadedSigns.isNotEmpty()) {
            _latestVitalSign.value = loadedSigns.last()
        }
        return loadedSigns
    }

    /**
     * Get count of vital signs in the database
     * @return Count of records in the database
     */
    suspend fun getDatabaseRecordCount(): Int {
        return databaseDataSource.getCount()
    }

    /**
     * Clear all data from the database
     */
    suspend fun clearDatabase() {
        databaseDataSource.clearAll()
    }

    /**
     * Get all distinct mannequins in the database
     * @return List of mannequin names
     */
    suspend fun getDistinctMannequinsFromDatabase(): List<String> {
        return databaseDataSource.getDistinctMannequins()
    }

    /**
     * Get all distinct scenarios in the database
     * @return List of scenario names
     */
    suspend fun getDistinctScenariosFromDatabase(): List<String> {
        return databaseDataSource.getDistinctScenarios()
    }

    // After loading data from any source, process it and optionally save to database
    suspend fun processAndSaveData(saveToDatabase: Boolean = false) {
        // Process the data
        // processLoadedData(true) // Removed - functionality moved to ViewModel

        // Optionally save to database
        if (saveToDatabase) {
            saveCurrentDataToDatabase()
        }
    }

    /**
     * Save data to CSV file
     * @param filename Name of the CSV file
     * @param csvContent Content of the CSV file
     * @return True if successful, false otherwise
     */
    suspend fun saveDataToCsvFile(filename: String, csvContent: String): Boolean {
        try {
            // Create the exports directory if it doesn't exist
            val exportsDir = File(context.getExternalFilesDir(null), "exports")
            if (!exportsDir.exists()) {
                exportsDir.mkdirs()
            }

            // Create the file
            val file = File(exportsDir, filename)

            // Write the CSV content
            file.writeText(csvContent)

            // Return success
            return true
        } catch (e: Exception) {
            Log.e("VitalSignsRepository", "Error saving data to CSV file", e)
            return false
        }
    }

    /**
     * Start monitoring for a specific mannequin with scenario-based thresholds
     * @param mannequin The mannequin name
     * @param scenario The scenario name
     * @param thresholds The thresholds for the scenario
     * @param simulationType The type of simulation (Training or Graded)
     */
    fun startMonitoring(
        mannequin: String,
        scenario: String,
        thresholds: Map<String, ScenarioThresholds.VitalSignThreshold>,
        simulationType: String = "Training"
    ) {
        // Set monitoring state to running
        monitoringState = MonitoringState.RUNNING

        // Clear any previous monitoring for this mannequin
        activeMonitorJobs[mannequin]?.cancel()

        // Determine what data source to use based on settings
        when (settings.value.dataSourceType) {
            DataSourceType.SIMULATION -> {
                // Create a dedicated simulator for this mannequin
                val mannequinSimulator = SimulatedVitalSignsDataSource()
                mannequinSimulator.setUpdateInterval(settings.value.updateIntervalMs)

                // Configure the simulator with the scenario
                mannequinSimulator.setScenario(scenario)

                // Set the connection status to CONNECTED
                updateMannequinConnectionStatus(mannequin, ConnectionStatus.CONNECTED)

                // Start a new coroutine for this mannequin's monitoring
                val job = monitoringScope.launch {
                    try {
                        // Start collecting simulated vital signs
                        mannequinSimulator.getRealtimeVitalSigns().collect { vitalSign ->
                            // Enrich with mannequin and scenario info
                            val enrichedSign = vitalSign.copy(
                                overrideMannequin = mannequin,
                                scenario = scenario,
                                simulationType = simulationType
                            )

                            // Process the vital sign, including threshold evaluation
                            processVitalSignWithThresholds(enrichedSign, thresholds)
                        }
                    } catch (e: Exception) {
                        // Update connection status on error
                        updateMannequinConnectionStatus(mannequin, ConnectionStatus.ERROR)
                    }
                }

                // Store the job
                activeMonitorJobs[mannequin] = job
            }

            DataSourceType.REAL_TIME_MONITOR -> {
                // Create a dedicated ZollMonitorDataSource for this mannequin
                // FIX 5: Add context
                val mannequinZollMonitor = ZollMonitorDataSource(context)

                // Configure the monitor
                mannequinZollMonitor.setUpdateInterval(settings.value.updateIntervalMs)
                mannequinZollMonitor.setConnectionMode(ZollMonitorDataSource.ConnectionMode.HTTP)

                // Use stored IP if available, otherwise use default
                val ipAddress = settings.value.mannequinIpAddresses[mannequin] ?:
                                MannequinConfig.getMannequinInfo(mannequin)?.defaultIpAddress ?:
                                "*************" // Fallback default

                mannequinZollMonitor.setConnectionDetails(
                    ipAddress = ipAddress,
                    port = 80,
                    mannequinName = mannequin
                )

                // Update connection status
                updateMannequinConnectionStatus(mannequin, ConnectionStatus.CONNECTING)

                // Start a new coroutine for this mannequin's monitoring
                val job = monitoringScope.launch {
                    try {
                        // Start collecting real-time vital signs
                        mannequinZollMonitor.getRealtimeVitalSigns().collect { vitalSign ->
                            // Enrich with mannequin and scenario info
                            val enrichedSign = vitalSign.copy(
                                overrideMannequin = mannequin,
                                scenario = scenario,
                                simulationType = simulationType
                            )

                            // Process the vital sign, including threshold evaluation
                            processVitalSignWithThresholds(enrichedSign, thresholds)

                            // Update connection status
                            updateMannequinConnectionStatus(mannequin, ConnectionStatus.CONNECTED)
                        }
                    } catch (e: Exception) {
                        // Update connection status on error
                        updateMannequinConnectionStatus(mannequin, ConnectionStatus.ERROR)
                    }
                }

                // Store the job
                activeMonitorJobs[mannequin] = job
            }

            else -> {
                // Default to simulation for other data source types
                startMonitoring(mannequin, scenario, thresholds, simulationType)
            }
        }
    }

    /**
     * Process a vital sign with scenario-specific thresholds
     * @param vitalSign The vital sign to process
     * @param thresholds The thresholds to apply
     */
    private suspend fun processVitalSignWithThresholds(
        vitalSign: VitalSign,
        thresholds: Map<String, ScenarioThresholds.VitalSignThreshold>
    ) {
        val mannequinName = vitalSign.overrideMannequin ?: "Default"

        // Evaluate the vital sign against thresholds
        val evaluatedVitalSign = evaluateVitalSignThresholds(vitalSign, thresholds)

        // For real-time monitoring, mark the vital sign as being within the sim window
        val updatedVitalSign = if (monitoringState == MonitoringState.RUNNING) {
            if (currentSource == DataSourceType.REAL_TIME_MONITOR) { // Specifically for LIVE_ZOLL SDK data
                evaluatedVitalSign.copy(
                    // sim, scenario, simulationType, overrideMannequin are already correctly set by startMonitoring
                    inPreciseSimTimeframe = true,
                    inSimWindow = true, // Explicitly true for any data coming from a running REAL_TIME_MONITOR
                    isValid = true // Assume valid if we're processing it from the SDK
                )
            } else { // For other running modes like SIMULATION
                 evaluatedVitalSign.copy(
                    inPreciseSimTimeframe = true,
                    inSimWindow = true,
                    isValid = true,
                    // Preserve existing logic for these other modes if it relies on global settings.
                    // However, if 'evaluatedVitalSign.sim' is already populated (e.g. by startSimulationForMannequin), prefer that.
                    sim = if (evaluatedVitalSign.sim.isNullOrEmpty()) {
                              settings.value.selectedSimulation.takeIf { it.isNotEmpty() }
                          } else {
                              evaluatedVitalSign.sim
                          } ?: evaluatedVitalSign.sim // Fallback to original if all else fails
                )
            }
        } else { // Not in a running monitoring state (e.g. processing loaded historical data)
            evaluatedVitalSign
        }

        // Update latest vital sign for this mannequin
        val currentLatestSigns = _latestVitalSigns.value.toMutableMap()
        currentLatestSigns[mannequinName] = updatedVitalSign
        _latestVitalSigns.value = currentLatestSigns

        // Update overall latest vital sign (for backward compatibility)
        _latestVitalSign.value = updatedVitalSign

        // Add to list with a maximum size
        val currentList = _vitalSigns.value.toMutableList()
        currentList.add(updatedVitalSign)

        // Keep only the last 300 readings per mannequin (5 minutes at 1Hz)
        val mannequinReadings = currentList.filter { it.overrideMannequin == mannequinName }
        if (mannequinReadings.size > 300) {
            val oldestToRemove = mannequinReadings.first()
            currentList.remove(oldestToRemove)
        }

        _vitalSigns.value = currentList

        // Track non-compliance events
        trackNonComplianceEvents(updatedVitalSign, thresholds)

        // Detect clinical events
        val scenario = updatedVitalSign.scenario ?: settings.value.scenario
        if (scenario.isNotEmpty() && currentList.size > 1) {
            // Get the previous vital sign for this mannequin
            val previousForMannequin = currentList
                .filter { it.overrideMannequin == mannequinName }
                .sortedBy { it.timeObj }
                .dropLast(1)
                .lastOrNull()

            if (previousForMannequin != null) {
                // Use the appropriate data source for event detection
                val newEvents = when (currentSource) {
                    DataSourceType.REAL_TIME_MONITOR, DataSourceType.WEBSOCKET -> {
                        zollMonitorDataSource.detectClinicalEvents(
                            listOf(previousForMannequin, updatedVitalSign),
                            scenario
                        )
                    }
                    else -> {
                        simulatedDataSource.detectClinicalEvents(
                            listOf(previousForMannequin, updatedVitalSign),
                            scenario
                        )
                    }
                }

                if (newEvents.isNotEmpty()) {
                    val currentEvents = _clinicalEvents.value.toMutableList()
                    currentEvents.addAll(newEvents)

                    // Keep only the last 50 events
                    if (currentEvents.size > 50) {
                        currentEvents.subList(0, currentEvents.size - 50).clear()
                    }

                    _clinicalEvents.value = currentEvents
                }
            }
        }
    }

    /**
     * Track non-compliance events based on vital sign status
     * @param vitalSign The vital sign to check
     * @param thresholds The thresholds for the scenario
     */
    private fun trackNonComplianceEvents(
        vitalSign: VitalSign,
        thresholds: Map<String, ScenarioThresholds.VitalSignThreshold>
    ) {
        val mannequinName = vitalSign.overrideMannequin ?: "Default"
        val scenario = vitalSign.scenario ?: settings.value.scenario

        // Check each vital sign status
        val currentActiveEvents = _activeNonComplianceEvents.value.toMutableMap()
        val completedEvents = mutableListOf<NonComplianceEvent>()

        // Process each vital sign type
        processVitalSignNonCompliance(
            vitalSign = vitalSign,
            vitalSignKey = "hr",
            vitalSignName = "Heart Rate",
            value = vitalSign.hr,
            status = vitalSign.vitalSignStatuses["hr"],
            threshold = thresholds["hr"],
            mannequinName = mannequinName,
            scenario = scenario,
            currentActiveEvents = currentActiveEvents,
            completedEvents = completedEvents
        )

        processVitalSignNonCompliance(
            vitalSign = vitalSign,
            vitalSignKey = "spo2",
            vitalSignName = "SpO2",
            value = vitalSign.spO2,
            status = vitalSign.vitalSignStatuses["spo2"],
            threshold = thresholds["spo2"],
            mannequinName = mannequinName,
            scenario = scenario,
            currentActiveEvents = currentActiveEvents,
            completedEvents = completedEvents
        )

        processVitalSignNonCompliance(
            vitalSign = vitalSign,
            vitalSignKey = "nibp_sys",
            vitalSignName = "Systolic BP",
            value = vitalSign.nibpSys,
            status = vitalSign.vitalSignStatuses["nibp_sys"],
            threshold = thresholds["nibp_sys"],
            mannequinName = mannequinName,
            scenario = scenario,
            currentActiveEvents = currentActiveEvents,
            completedEvents = completedEvents
        )

        processVitalSignNonCompliance(
            vitalSign = vitalSign,
            vitalSignKey = "nibp_dia",
            vitalSignName = "Diastolic BP",
            value = vitalSign.nibpDia,
            status = vitalSign.vitalSignStatuses["nibp_dia"],
            threshold = thresholds["nibp_dia"],
            mannequinName = mannequinName,
            scenario = scenario,
            currentActiveEvents = currentActiveEvents,
            completedEvents = completedEvents
        )

        processVitalSignNonCompliance(
            vitalSign = vitalSign,
            vitalSignKey = "nibp_map",
            vitalSignName = "Mean Arterial Pressure",
            value = vitalSign.nibpMap,
            status = vitalSign.vitalSignStatuses["nibp_map"],
            threshold = thresholds["nibp_map"],
            mannequinName = mannequinName,
            scenario = scenario,
            currentActiveEvents = currentActiveEvents,
            completedEvents = completedEvents
        )

        processVitalSignNonCompliance(
            vitalSign = vitalSign,
            vitalSignKey = "temp",
            vitalSignName = "Temperature",
            value = vitalSign.temp1,
            status = vitalSign.vitalSignStatuses["temp"],
            threshold = thresholds["temp"],
            mannequinName = mannequinName,
            scenario = scenario,
            currentActiveEvents = currentActiveEvents,
            completedEvents = completedEvents
        )

        processVitalSignNonCompliance(
            vitalSign = vitalSign,
            vitalSignKey = "resp_rate",
            vitalSignName = "Respiratory Rate",
            value = vitalSign.respRate,
            status = vitalSign.vitalSignStatuses["resp_rate"],
            threshold = thresholds["resp_rate"],
            mannequinName = mannequinName,
            scenario = scenario,
            currentActiveEvents = currentActiveEvents,
            completedEvents = completedEvents
        )

        processVitalSignNonCompliance(
            vitalSign = vitalSign,
            vitalSignKey = "etco2",
            vitalSignName = "EtCO2",
            value = vitalSign.etCO2,
            status = vitalSign.vitalSignStatuses["etco2"],
            threshold = thresholds["etco2"],
            mannequinName = mannequinName,
            scenario = scenario,
            currentActiveEvents = currentActiveEvents,
            completedEvents = completedEvents
        )

        // Update active events
        _activeNonComplianceEvents.value = currentActiveEvents

        // Add completed events to the list
        if (completedEvents.isNotEmpty()) {
            val allCompletedEvents = _nonComplianceEvents.value.toMutableList()
            allCompletedEvents.addAll(completedEvents)
            _nonComplianceEvents.value = allCompletedEvents
        }
    }

    /**
     * Process a specific vital sign for non-compliance
     */
    private fun processVitalSignNonCompliance(
        vitalSign: VitalSign,
        vitalSignKey: String,
        vitalSignName: String,
        value: Double?,
        status: VitalSignStatus?,
        threshold: ScenarioThresholds.VitalSignThreshold?,
        mannequinName: String,
        scenario: String,
        currentActiveEvents: MutableMap<String, ActiveNonComplianceEvent>,
        completedEvents: MutableList<NonComplianceEvent>
    ) {
        // Skip processing if value is null or threshold is null
        if (value == null || threshold == null) {
            if (threshold == null) {
                Log.w("VitalSignsRepository", "No threshold found for $vitalSignName ($vitalSignKey) in scenario $scenario")
            }
            return
        }

        // Use the provided status or calculate it based on the threshold
        val effectiveStatus = status ?: when {
            value < threshold.criticalLow -> VitalSignStatus.CRITICAL_LOW
            value < threshold.warningLow -> VitalSignStatus.WARNING_LOW
            value > threshold.criticalHigh -> VitalSignStatus.CRITICAL_HIGH
            value > threshold.warningHigh -> VitalSignStatus.WARNING_HIGH
            else -> VitalSignStatus.NORMAL
        }

        // Create a unique key for this mannequin + vital sign
        val eventKey = "$mannequinName:$vitalSignKey"

        // Check if this vital sign is non-compliant
        val isNonCompliant = effectiveStatus != VitalSignStatus.NORMAL && effectiveStatus != VitalSignStatus.UNKNOWN

        if (isNonCompliant) {
            // Check if we already have an active event for this vital sign
            if (eventKey in currentActiveEvents) {
                // Update the existing event
                val existingEvent = currentActiveEvents[eventKey]!!
                existingEvent.values.add(value)
                // Status might have changed from WARNING to CRITICAL or vice versa
                currentActiveEvents[eventKey] = existingEvent.copy(
                    currentValue = value,
                    status = effectiveStatus
                )

                // Log the update of an existing non-compliance event
                Log.d("VitalSignsRepository", "Updated non-compliance event: $vitalSignName = $value, status = $effectiveStatus")
            } else {
                // Create a new active event
                val newEvent = ActiveNonComplianceEvent(
                    startTime = vitalSign.timeObj,
                    vitalSign = vitalSignName,
                    vitalSignKey = vitalSignKey,
                    currentValue = value,
                    minThreshold = if (effectiveStatus == VitalSignStatus.WARNING_LOW || effectiveStatus == VitalSignStatus.CRITICAL_LOW)
                                    threshold.warningLow else null,
                    maxThreshold = if (effectiveStatus == VitalSignStatus.WARNING_HIGH || effectiveStatus == VitalSignStatus.CRITICAL_HIGH)
                                    threshold.warningHigh else null,
                    values = mutableListOf(value),
                    mannequin = mannequinName,
                    scenario = scenario,
                    status = effectiveStatus
                )
                currentActiveEvents[eventKey] = newEvent

                // Log the creation of a new non-compliance event
                Log.d("VitalSignsRepository", "Created new non-compliance event: $vitalSignName = $value, status = $effectiveStatus, threshold = ${threshold.warningLow}-${threshold.warningHigh}")
            }
        } else {
            // Check if we had an active event that is now over
            if (eventKey in currentActiveEvents) {
                // Convert active event to completed event
                val activeEvent = currentActiveEvents[eventKey]!!
                val completedEvent = activeEvent.toNonComplianceEvent(vitalSign.timeObj)
                completedEvents.add(completedEvent)

                // Log the completion of a non-compliance event
                Log.d("VitalSignsRepository", "Completed non-compliance event: ${activeEvent.vitalSign}, duration: ${(vitalSign.timeObj.time - activeEvent.startTime.time) / 1000}s")

                // Remove from active events
                currentActiveEvents.remove(eventKey)
            }
        }
    }

    /**
     * Evaluate vital sign thresholds to determine status (normal, warning, critical)
     * @param vitalSign The vital sign to evaluate
     * @param thresholds The scenario-specific thresholds
     * @return Status-updated vital sign
     */
    private fun evaluateVitalSignThresholds(
        vitalSign: VitalSign,
        thresholds: Map<String, ScenarioThresholds.VitalSignThreshold>
    ): VitalSign {
        val vitalSignStatuses = mutableMapOf<String, VitalSignStatus>()

        // Log the thresholds for debugging
        Log.d("VitalSignsRepository", "Evaluating vital sign thresholds for ${vitalSign.overrideMannequin}, scenario: ${vitalSign.scenario}")
        Log.d("VitalSignsRepository", "Available thresholds: ${thresholds.keys}")

        // Evaluate Heart Rate
        thresholds["hr"]?.let { threshold ->
            val status = when {
                vitalSign.hr == null -> VitalSignStatus.UNKNOWN
                vitalSign.hr < threshold.criticalLow -> VitalSignStatus.CRITICAL_LOW
                vitalSign.hr < threshold.warningLow -> VitalSignStatus.WARNING_LOW
                vitalSign.hr > threshold.criticalHigh -> VitalSignStatus.CRITICAL_HIGH
                vitalSign.hr > threshold.warningHigh -> VitalSignStatus.WARNING_HIGH
                else -> VitalSignStatus.NORMAL
            }
            vitalSignStatuses["hr"] = status
            Log.d("VitalSignsRepository", "HR: ${vitalSign.hr} → $status (thresholds: ${threshold.warningLow}-${threshold.warningHigh})")
        }

        // Evaluate SpO2
        thresholds["spo2"]?.let { threshold ->
            val status = when {
                vitalSign.spO2 == null -> VitalSignStatus.UNKNOWN
                vitalSign.spO2 < threshold.criticalLow -> VitalSignStatus.CRITICAL_LOW
                vitalSign.spO2 < threshold.warningLow -> VitalSignStatus.WARNING_LOW
                vitalSign.spO2 > threshold.criticalHigh -> VitalSignStatus.CRITICAL_HIGH
                vitalSign.spO2 > threshold.warningHigh -> VitalSignStatus.WARNING_HIGH
                else -> VitalSignStatus.NORMAL
            }
            vitalSignStatuses["spo2"] = status
            Log.d("VitalSignsRepository", "SpO2: ${vitalSign.spO2} → $status (thresholds: ${threshold.warningLow}-${threshold.warningHigh})")
        }

        // Evaluate Systolic BP
        thresholds["nibp_sys"]?.let { threshold ->
            val status = when {
                vitalSign.nibpSys == null -> VitalSignStatus.UNKNOWN
                vitalSign.nibpSys < threshold.criticalLow -> VitalSignStatus.CRITICAL_LOW
                vitalSign.nibpSys < threshold.warningLow -> VitalSignStatus.WARNING_LOW
                vitalSign.nibpSys > threshold.criticalHigh -> VitalSignStatus.CRITICAL_HIGH
                vitalSign.nibpSys > threshold.warningHigh -> VitalSignStatus.WARNING_HIGH
                else -> VitalSignStatus.NORMAL
            }
            vitalSignStatuses["nibp_sys"] = status
            Log.d("VitalSignsRepository", "NIBP Sys: ${vitalSign.nibpSys} → $status (thresholds: ${threshold.warningLow}-${threshold.warningHigh})")
        }

        // Evaluate Diastolic BP
        thresholds["nibp_dia"]?.let { threshold ->
            val status = when {
                vitalSign.nibpDia == null -> VitalSignStatus.UNKNOWN
                vitalSign.nibpDia < threshold.criticalLow -> VitalSignStatus.CRITICAL_LOW
                vitalSign.nibpDia < threshold.warningLow -> VitalSignStatus.WARNING_LOW
                vitalSign.nibpDia > threshold.criticalHigh -> VitalSignStatus.CRITICAL_HIGH
                vitalSign.nibpDia > threshold.warningHigh -> VitalSignStatus.WARNING_HIGH
                else -> VitalSignStatus.NORMAL
            }
            vitalSignStatuses["nibp_dia"] = status
            Log.d("VitalSignsRepository", "NIBP Dia: ${vitalSign.nibpDia} → $status (thresholds: ${threshold.warningLow}-${threshold.warningHigh})")
        }

        // Evaluate Mean Arterial Pressure
        thresholds["nibp_map"]?.let { threshold ->
            val status = when {
                vitalSign.nibpMap == null -> VitalSignStatus.UNKNOWN
                vitalSign.nibpMap < threshold.criticalLow -> VitalSignStatus.CRITICAL_LOW
                vitalSign.nibpMap < threshold.warningLow -> VitalSignStatus.WARNING_LOW
                vitalSign.nibpMap > threshold.criticalHigh -> VitalSignStatus.CRITICAL_HIGH
                vitalSign.nibpMap > threshold.warningHigh -> VitalSignStatus.WARNING_HIGH
                else -> VitalSignStatus.NORMAL
            }
            vitalSignStatuses["nibp_map"] = status
            Log.d("VitalSignsRepository", "NIBP MAP: ${vitalSign.nibpMap} → $status (thresholds: ${threshold.warningLow}-${threshold.warningHigh})")
        }

        // Evaluate Temperature
        thresholds["temp"]?.let { threshold ->
            val status = when {
                vitalSign.temp1 == null -> VitalSignStatus.UNKNOWN
                vitalSign.temp1 < threshold.criticalLow -> VitalSignStatus.CRITICAL_LOW
                vitalSign.temp1 < threshold.warningLow -> VitalSignStatus.WARNING_LOW
                vitalSign.temp1 > threshold.criticalHigh -> VitalSignStatus.CRITICAL_HIGH
                vitalSign.temp1 > threshold.warningHigh -> VitalSignStatus.WARNING_HIGH
                else -> VitalSignStatus.NORMAL
            }
            vitalSignStatuses["temp"] = status
            Log.d("VitalSignsRepository", "Temp: ${vitalSign.temp1} → $status (thresholds: ${threshold.warningLow}-${threshold.warningHigh})")
        }

        // Evaluate Respiratory Rate
        thresholds["resp_rate"]?.let { threshold ->
            val status = when {
                vitalSign.respRate == null -> VitalSignStatus.UNKNOWN
                vitalSign.respRate < threshold.criticalLow -> VitalSignStatus.CRITICAL_LOW
                vitalSign.respRate < threshold.warningLow -> VitalSignStatus.WARNING_LOW
                vitalSign.respRate > threshold.criticalHigh -> VitalSignStatus.CRITICAL_HIGH
                vitalSign.respRate > threshold.warningHigh -> VitalSignStatus.WARNING_HIGH
                else -> VitalSignStatus.NORMAL
            }
            vitalSignStatuses["resp_rate"] = status
            Log.d("VitalSignsRepository", "Resp Rate: ${vitalSign.respRate} → $status (thresholds: ${threshold.warningLow}-${threshold.warningHigh})")
        }

        // Evaluate EtCO2
        thresholds["etco2"]?.let { threshold ->
            val status = when {
                vitalSign.etCO2 == null -> VitalSignStatus.UNKNOWN
                vitalSign.etCO2 < threshold.criticalLow -> VitalSignStatus.CRITICAL_LOW
                vitalSign.etCO2 < threshold.warningLow -> VitalSignStatus.WARNING_LOW
                vitalSign.etCO2 > threshold.criticalHigh -> VitalSignStatus.CRITICAL_HIGH
                vitalSign.etCO2 > threshold.warningHigh -> VitalSignStatus.WARNING_HIGH
                else -> VitalSignStatus.NORMAL
            }
            vitalSignStatuses["etco2"] = status
            Log.d("VitalSignsRepository", "EtCO2: ${vitalSign.etCO2} → $status (thresholds: ${threshold.warningLow}-${threshold.warningHigh})")
        }

        // We can't check ICP and CPP yet, as they aren't in the VitalSign class
        // TODO: Add these fields to VitalSign and then add evaluations here

        // Log the final statuses
        Log.d("VitalSignsRepository", "Final vitalSignStatuses: ${vitalSignStatuses.keys}")

        // Return updated vital sign with statuses
        return vitalSign.copy(
            vitalSignStatuses = vitalSignStatuses
        )
    }

    companion object {
        // Singleton instance
        @Volatile
        private var INSTANCE: VitalSignsRepository? = null

        /**
         * Get or create singleton instance
         */
        fun getInstance(context: Context): VitalSignsRepository {
            return INSTANCE ?: synchronized(this) {
                try {
                    val instance = VitalSignsRepository(context)
                    INSTANCE = instance
                    instance
                } catch (e: RuntimeException) {
                    // If the database can't be created, fall back to a version without database
                    if (e.message?.contains("VitalSignsDatabase") == true) {
                        Log.e("VitalSignsRepository", "Database initialization failed - using no-db instance", e)
                        val instance = createNoDatabaseInstance(context)
                        INSTANCE = instance
                        instance
                    } else {
                        throw e
                    }
                }
            }
        }

        /**
         * Create a version of the repository that doesn't use the database
         */
        private fun createNoDatabaseInstance(context: Context): VitalSignsRepository {
            // Create a regular instance without using the database initialization
            val instance = VitalSignsRepository(context.applicationContext)

            // No need to override methods since we'll use try-catch in the methods themselves
            Log.i("VitalSignsRepository", "Created no-database fallback instance")
            return instance
        }
    }
}