package com.example.myapplication.data.util

import androidx.collection.LruCache
import com.example.myapplication.data.model.VitalSign
import org.json.JSONArray
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.concurrent.ConcurrentHashMap

/**
 * Utility class for robust JSON parsing using techniques from the Python implementation
 * Focuses on accuracy and edge case handling
 */
object RobustJsonParser {

    private const val CACHE_SIZE = 2048

    // LRU Cache for trend value extraction (similar to Python's @lru_cache)
    private val trendValueCache = LruCache<String, Pair<Double?, String>>(CACHE_SIZE)

    // Mapping of device serial numbers to mannequin names
    private val MANNEQUIN_MAP = mapOf(
        "AI23F013939" to "<PERSON>",
        "AI23H014090" to "<PERSON>",
        "AI15F004305" to "<PERSON>",
        "AI15D003889" to "<PERSON>",
        "AI20C009617" to "<PERSON>"
    )

    // Class to hold extracted trend data components
    data class ExtractedTrendData(
        val dataState: String?,
        val dataStatus: Any?,
        val valueText: Any?
    )

    /**
     * Parse a JSON file string into VitalSign objects
     * @param jsonString The JSON file content as string
     * @param sourceFileName The name of the source file
     * @return List of parsed VitalSign objects
     */
    fun parseJsonString(jsonString: String, sourceFileName: String): List<VitalSign> {
        val result = mutableListOf<VitalSign>()
        
        // Map to track already processed timestamps per device to prevent duplicates
        val processedTimestamps = mutableMapOf<Pair<Date, String?>, VitalSign>()
        
        try {
            val jsonObject = JSONObject(jsonString)
            
            // Navigate through ZOLL structure
            val zollObj = jsonObject.optJSONObject("ZOLL") ?: return emptyList()
            val fullDisclosure = zollObj.optJSONArray("FullDisclosure") ?: return emptyList()
            if (fullDisclosure.length() == 0) return emptyList()
            
            val records = fullDisclosure.optJSONObject(0)?.optJSONArray("FullDisclosureRecord") 
                ?: return emptyList()
            
            // Extract device serial number
            var deviceSerial: String? = null
            for (i in 0 until records.length()) {
                val record = records.optJSONObject(i) ?: continue
                if (record.has("DeviceConfiguration")) {
                    deviceSerial = record.optJSONObject("DeviceConfiguration")
                        ?.optString("DeviceSerialNumber")
                    break
                }
            }
            
            // Process trend reports
            for (i in 0 until records.length()) {
                val record = records.optJSONObject(i) ?: continue
                if (record.has("TrendRpt")) {
                    val trendRpt = record.optJSONObject("TrendRpt") ?: continue
                    parseTrendReport(trendRpt, deviceSerial, sourceFileName)?.let { vitalSign -> 
                        // Create a key from timestamp and device serial to check for duplicates
                        val timeDeviceKey = Pair(vitalSign.timeObj, vitalSign.deviceSerial)
                        
                        // Only add this vital sign if we haven't seen this exact timestamp for this device
                        if (!processedTimestamps.containsKey(timeDeviceKey)) {
                            processedTimestamps[timeDeviceKey] = vitalSign
                            result.add(vitalSign)
                        }
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        
        return result
    }
    
    /**
     * Parse a single JSON object into a VitalSign object
     * Used for streaming approach to prevent memory issues with large files
     * 
     * @param jsonObjectString The JSON object as a string
     * @param sourceFileName The name of the source file
     * @return A VitalSign object or null if parsing fails
     */
    fun parseJsonObject(jsonObjectString: String, sourceFileName: String): VitalSign? {
        try {
            val jsonObject = JSONObject(jsonObjectString)
            
            // Check if this is a ZOLL-style object
            if (jsonObject.has("ZOLL")) {
                return parseZollObject(jsonObject, sourceFileName)
            }
            
            // Check if this is a direct TrendRpt object
            if (jsonObject.has("TrendRpt")) {
                val trendRpt = jsonObject.optJSONObject("TrendRpt")
                val deviceSerial = jsonObject.optString("DeviceSerialNumber") 
                return parseTrendReport(trendRpt, deviceSerial, sourceFileName)
            }
            
            // Check if this is a direct vital sign object (simpler format)
            if (jsonObject.has("timeStr") || jsonObject.has("timeObj") || jsonObject.has("timestamp")) {
                return parseDirectVitalSignObject(jsonObject, sourceFileName)
            }
            
            // Unsupported format
            return null
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
    }
    
    /**
     * Parse a ZOLL-style JSON object (top level contains ZOLL key)
     */
    private fun parseZollObject(jsonObject: JSONObject, sourceFileName: String): VitalSign? {
        try {
            // Navigate through ZOLL structure
            val zollObj = jsonObject.optJSONObject("ZOLL") ?: return null
            val fullDisclosure = zollObj.optJSONArray("FullDisclosure") ?: return null
            if (fullDisclosure.length() == 0) return null
            
            val records = fullDisclosure.optJSONObject(0)?.optJSONArray("FullDisclosureRecord") 
                ?: return null
            
            // Extract device serial number
            var deviceSerial: String? = null
            for (i in 0 until records.length()) {
                val record = records.optJSONObject(i) ?: continue
                if (record.has("DeviceConfiguration")) {
                    deviceSerial = record.optJSONObject("DeviceConfiguration")
                        ?.optString("DeviceSerialNumber")
                    break
                }
            }
            
            // Find and parse the first trend report
            for (i in 0 until records.length()) {
                val record = records.optJSONObject(i) ?: continue
                if (record.has("TrendRpt")) {
                    val trendRpt = record.optJSONObject("TrendRpt") ?: continue
                    return parseTrendReport(trendRpt, deviceSerial, sourceFileName)
                }
            }
            
            return null
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
    }
    
    /**
     * Parse a direct vital sign object (simpler format with properties directly accessible)
     */
    private fun parseDirectVitalSignObject(jsonObject: JSONObject, sourceFileName: String): VitalSign? {
        try {
            // Initialize vital data map
            val vitalData = mutableMapOf<String, Any?>()
            
            // Extract time information
            if (jsonObject.has("timeStr")) {
                val dateTimeStr = jsonObject.optString("timeStr")
                val timeObj = parseDateTime(dateTimeStr)
                vitalData["timeObj"] = timeObj
                vitalData["timeStr"] = dateTimeStr
            } else if (jsonObject.has("timestamp")) {
                val timestamp = jsonObject.optLong("timestamp")
                val timeObj = Date(timestamp)
                vitalData["timeObj"] = timeObj
                vitalData["timeStr"] = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.US).format(timeObj)
            }
            
            // Check if we have a valid time object
            if (vitalData["timeObj"] == null) {
                return null
            }
            
            // Add metadata
            vitalData["sourceFile"] = sourceFileName
            vitalData["deviceSerial"] = jsonObject.optString("deviceSerial", null)
            
            // Map vital sign fields
            val vitalSignFields = arrayOf(
                "hr", "spO2", "nibpSys", "nibpDia", "nibpMap", "temp1", "respRate", 
                "etCO2", "fiCO2", "spMet", "spCo", "pvi", "pi", "spOC", "spHb", 
                "ibp1Sys", "ibp1Dia", "ibp1Map"
            )
            
            for (field in vitalSignFields) {
                if (jsonObject.has(field)) {
                    val value = jsonObject.opt(field)
                    if (value is Number) {
                        vitalData[field] = value.toDouble()
                    }
                }
            }
            
            return createVitalSignFromData(vitalData)
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
    }
    
    /**
     * Parse a JSON array string into a list of VitalSign objects
     * Used for streaming approach with better memory management
     * 
     * @param jsonArrayString The JSON array as a string
     * @param sourceFileName The name of the source file
     * @return A list of VitalSign objects
     */
    fun parseJsonArray(jsonArrayString: String, sourceFileName: String): List<VitalSign> {
        val result = mutableListOf<VitalSign>()
        
        try {
            val jsonArray = JSONArray(jsonArrayString)
            
            // Process each item in the array
            for (i in 0 until jsonArray.length()) {
                val item = jsonArray.optJSONObject(i) ?: continue
                
                // Convert the item to string and parse it
                val itemString = item.toString()
                parseJsonObject(itemString, sourceFileName)?.let { vitalSign ->
                    result.add(vitalSign)
                }
                
                // If we're processing a very large array, free memory after each item
                if (i % 100 == 0) {
                    System.gc()
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        
        return result
    }
    
    /**
     * Parse a trend report into a VitalSign object
     * Handles edge cases and different data formats
     */
    fun parseTrendReport(trendRpt: JSONObject?, deviceSerial: String?, sourceFileName: String): VitalSign? {
        try {
            // Extract timestamp
            val stdHdr = trendRpt?.optJSONObject("StdHdr") ?: return null
            val dateTimeStr = stdHdr.optString("DevDateTime") ?: return null
            
            // Parse date/time with flexible format handling
            val timeObj = parseDateTime(dateTimeStr) ?: return null
            
            // Get trend data
            val trend = trendRpt?.optJSONObject("Trend") ?: return null
            
            // Initialize vital data map
            val vitalData = mutableMapOf<String, Any?>()
            
            // Add base information
            vitalData["timeObj"] = timeObj
            vitalData["timeStr"] = dateTimeStr
            vitalData["deviceSerial"] = deviceSerial
            vitalData["sourceFile"] = sourceFileName
            
            // Process temperature readings with decimal correction
            processTempReadings(trend, vitalData)
            
            // Process common vital signs using systematic mapping
            processCommonVitals(trend, vitalData)
            
            // Process SpO2-related parameters
            processSpO2Parameters(trend, vitalData)
            
            // Process NIBP readings
            processNIBPReadings(trend, vitalData)
            
            // Process IBP readings
            processIBPReadings(trend, vitalData)
            
            // Create VitalSign object from collected data
            return createVitalSignFromData(vitalData)
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
    }
    
    /**
     * Parse datetime string with flexible format handling
     */
    private fun parseDateTime(dateTimeStr: String): Date? {
        val formats = arrayOf(
            "yyyy-MM-dd'T'HH:mm:ss",
            "yyyy-MM-dd'T'HH:mm:ss.SSS",
            "yyyy-MM-dd HH:mm:ss",
            "yyyy/MM/dd HH:mm:ss"
        )
        
        for (format in formats) {
            try {
                val sdf = SimpleDateFormat(format, Locale.US)
                return sdf.parse(dateTimeStr)
            } catch (e: Exception) {
                // Try next format
            }
        }
        
        return try {
            // Last resort: try system date parsing
            val fallbackSdf = SimpleDateFormat()
            fallbackSdf.parse(dateTimeStr)
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * Extract trend data components with improved handling of nested structures
     * Similar to Python's extract_trend_data function
     */
    private fun extractTrendData(trendData: JSONObject?): ExtractedTrendData {
        if (trendData == null) {
            return ExtractedTrendData(null, null, null)
        }
        
        // Extract data state with fallback handling
        val dataState = trendData.optString("DataState", null)
        
        // Extract data status with fallback handling
        val dataStatus = when {
            trendData.has("DataStatus") -> trendData.opt("DataStatus")
            else -> null
        }
        
        // Extract value with improved handling of nested structures
        var valueText: Any? = null
        
        when {
            // Direct #text key in trendData
            trendData.has("#text") -> {
                valueText = trendData.opt("#text")
            }
            // Val object with nested structure
            trendData.has("Val") -> {
                val valObj = trendData.opt("Val")
                valueText = when {
                    valObj is JSONObject && valObj.has("#text") -> valObj.opt("#text")
                    valObj is JSONObject && valObj.has("value") -> valObj.opt("value")
                    valObj is JSONObject && valObj.has("Value") -> valObj.opt("Value")
                    else -> valObj // Use direct value
                }
            }
        }
        
        // If we have a value but no data state, assume it's valid
        val finalDataState = if (valueText != null && dataState == null) "valid" else dataState
        
        return ExtractedTrendData(finalDataState, dataStatus, valueText)
    }
    
    /**
     * Enhanced version of trend value extraction that explicitly handles different states and formats
     * Similar to Python's get_trend_val function with caching
     */
    private fun getTrendVal(dataState: String?, dataStatus: Any?, valueText: Any?): Pair<Double?, String> {
        // Create cache key
        val cacheKey = "$dataState:$dataStatus:$valueText"
        
        // Check cache first
        trendValueCache.get(cacheKey)?.let { return it }
        
        // Process value and status
        val result = calculateTrendVal(dataState, dataStatus, valueText)
        
        // Cache the result
        trendValueCache.put(cacheKey, result)
        
        return result
    }
    
    /**
     * Actual calculation logic for trend values - separated from caching
     */
    private fun calculateTrendVal(dataState: String?, dataStatus: Any?, valueText: Any?): Pair<Double?, String> {
        // First determine the status based on dataState and dataStatus
        val determinedStatus = when {
            dataState == "---" || dataState == "unmonitored" -> "unmonitored"
            dataState == "invalid" || dataState == "INVALID" -> "invalid"
            (dataStatus is Int && dataStatus == 1) || (dataStatus is String && dataStatus == "1") -> "invalid" // Treat DataStatus 1 as invalid
            dataState == "valid" -> "valid" // Explicitly handle "valid"
            // If dataState is null or something else, but we have a value later, 
            // we'll default to "valid". If no value, it becomes "error".
            else -> dataState ?: "unknown" // Keep original state if not null, else mark as unknown temporarily
        }

        // If status is clearly unmonitored or invalid, return immediately
        if (determinedStatus == "unmonitored" || determinedStatus == "invalid") {
            return Pair(null, determinedStatus)
        }
        
        // Try to parse the value
        var value: Double? = null
        try {
            if (valueText != null) {
                value = when (valueText) {
                // Handle different data formats
                is Int -> valueText.toDouble()
                is Long -> valueText.toDouble()
                is Float -> valueText.toDouble()
                is Double -> valueText
                is String -> {
                    // Clean string value - remove non-numeric characters except decimal and negative
                    val cleanVal = valueText.filter { it.isDigit() || it == '.' || it == '-' }
                        if (cleanVal.isNotEmpty()) cleanVal.toDoubleOrNull() else null
                }
                is JSONObject -> {
                    if (valueText.has("#text")) {
                        val textValue = valueText.opt("#text")
                        when (textValue) {
                            is Number -> textValue.toDouble()
                            is String -> {
                                val cleanVal = textValue.filter { it.isDigit() || it == '.' || it == '-' }
                                    if (cleanVal.isNotEmpty()) cleanVal.toDoubleOrNull() else null
                            }
                            else -> null
                        }
                    } else null
                }
                else -> null
            }
            }
        } catch (e: Exception) {
            // Parsing error
            return Pair(null, "error")
        }

        // Final decision based on parsed value and determined status
        return when {
            value != null && determinedStatus == "valid" -> Pair(value, "valid")
            value != null && determinedStatus == "unknown" -> Pair(value, "valid") // If state was unknown but value exists, treat as valid
            value == null && determinedStatus == "valid" -> Pair(null, "error") // State said valid, but no value found
            value == null && determinedStatus == "unknown" -> Pair(null, "unmonitored") // No value and unknown state -> likely unmonitored
            else -> Pair(value, determinedStatus) // Return original determined status (e.g., error, or maybe other specific states)
        }
    }
    
    /**
     * Process temperature readings from trend data
     */
    private fun processTempReadings(trend: JSONObject, vitalData: MutableMap<String, Any?>) {
        // Get temperature array or single object
        val tempItem = trend.opt("Temp")
        
        // Handle both array and single object formats
        val tempArray = when {
            tempItem is JSONArray -> tempItem
            tempItem is JSONObject -> JSONArray().apply { put(tempItem) }
            else -> JSONArray()
        }
        
        // Process each temperature channel
        for (i in 0 until tempArray.length()) {
            val tempObj = tempArray.optJSONObject(i) ?: continue
            val trendData = tempObj.optJSONObject("TrendData") ?: continue
            
            val extractedData = extractTrendData(trendData)
            val (tempVal, tempStatus) = getTrendVal(
                extractedData.dataState, 
                extractedData.dataStatus, 
                extractedData.valueText
            )
            
            // Always divide temperature by 10 to correct decimal placement
            val correctedTemp = tempVal?.div(10.0)
            
            // Store in data map with 1-based index
            vitalData["temp${i+1}"] = correctedTemp
            vitalData["temp${i+1}Status"] = tempStatus
        }
    }
    
    /**
     * Process common vital signs using systematic mapping approach
     */
    private fun processCommonVitals(trend: JSONObject, vitalData: MutableMap<String, Any?>) {
        // Define mapping of output column names to JSON paths
        val vitalMappings = mapOf(
            "hr" to Pair("Hr", "TrendData"),
            "fiCO2" to Pair("Fico2", "TrendData"),
            "spO2" to Pair("Spo2", "TrendData"),
            "etCO2" to Pair("Etco2", "TrendData"),
            "respRate" to Pair("Resp", "TrendData")
        )
        
        // Process each vital sign using the same extraction logic
        for ((colName, path) in vitalMappings) {
            val (trendKey, dataKey) = path
            
            // Get trend object and trend data
            val trendObj = trend.optJSONObject(trendKey) ?: continue
            val trendData = trendObj.optJSONObject(dataKey) ?: continue
            
            // Extract data components
            val extractedData = extractTrendData(trendData)
            
            // Get value and status
            val (value, status) = getTrendVal(
                extractedData.dataState,
                extractedData.dataStatus,
                extractedData.valueText
            )
            
            // Store in data map with lowercase keys for consistency
            vitalData[colName.lowercase()] = value
            val statusKey = colName + "Status"
            vitalData[statusKey] = status
        }
    }
    
    /**
     * Process SpO2-related parameters
     */
    private fun processSpO2Parameters(trend: JSONObject, vitalData: MutableMap<String, Any?>) {
        val spO2Obj = trend.optJSONObject("Spo2") ?: return
        
        // SpO2 parameters mapping
        val spO2Params = listOf("SpMet", "SpCo", "PVI", "PI", "SpOC", "SpHb")
        
        for (param in spO2Params) {
            val paramObj = spO2Obj.optJSONObject(param) ?: continue
            val trendData = paramObj.optJSONObject("TrendData") ?: continue
            
            // Extract data components
            val extractedData = extractTrendData(trendData)
            
            // Get value and status
            val (value, status) = getTrendVal(
                extractedData.dataState,
                extractedData.dataStatus,
                extractedData.valueText
            )
            
            // Store in data map - convert to lowercase for consistency
            val camelCaseParam = param.substring(0, 1).lowercase() + param.substring(1)
            vitalData[camelCaseParam.lowercase()] = value
            vitalData["${camelCaseParam}Status"] = status
        }
    }
    
    /**
     * Process NIBP (non-invasive blood pressure) readings
     */
    private fun processNIBPReadings(trend: JSONObject, vitalData: MutableMap<String, Any?>) {
        val nibpObj = trend.optJSONObject("Nibp")
        
        if (nibpObj != null) {
            // Process each component - both lowercase and original case
            for (component in listOf("Sys", "Dia", "Map")) {
                val lowerComponent = component.lowercase()
                
                // Try both lowercase and original case to handle inconsistencies
                val componentObj = nibpObj.optJSONObject(lowerComponent) 
                    ?: nibpObj.optJSONObject(component)
                    ?: continue
                    
                val trendData = componentObj.optJSONObject("TrendData") ?: continue
                
                // Extract data components
                val extractedData = extractTrendData(trendData)
                
                // Get value and status
                val (value, status) = getTrendVal(
                    extractedData.dataState,
                    extractedData.dataStatus,
                    extractedData.valueText
                )
                
                // Store in standardized format with consistent lowercase keys
                val valueKey = "nibp${component.lowercase()}"
                val statusKey = "nibp${component}Status"
                vitalData[valueKey] = value
                vitalData[statusKey] = status
            }
        } else {
            // If NIBP data is completely missing, add entries with unmonitored status
            for (component in listOf("SYS", "DIA", "MAP")) {
                val valueKey = "nibp${component.lowercase()}"
                val statusKey = "nibp${component}Status"
                vitalData[valueKey] = null
                vitalData[statusKey] = "unmonitored"
            }
        }
    }
    
    /**
     * Process IBP (invasive blood pressure) readings
     */
    private fun processIBPReadings(trend: JSONObject, vitalData: MutableMap<String, Any?>) {
        // Handle both array and single object formats
        val ibpItem = trend.opt("Ibp")
        val ibpArray = when {
            ibpItem is JSONArray -> ibpItem
            ibpItem is JSONObject -> JSONArray().apply { put(ibpItem) }
            else -> return
        }
        
        for (i in 0 until ibpArray.length()) {
            val ibpObj = ibpArray.optJSONObject(i) ?: continue
            
            // Get channel number - default to "1" if not specified
            val channelNum = ibpObj.optString("@ChanNum", "1")
            
            // Process each component
            for (component in listOf("Sys", "Dia", "Map")) {
                val lowerComponent = component.lowercase()
                
                // Try both lowercase and original case
                val componentObj = ibpObj.optJSONObject(lowerComponent) 
                    ?: ibpObj.optJSONObject(component)
                    ?: continue
                    
                val trendData = componentObj.optJSONObject("TrendData") ?: continue
                
                // Extract data components
                val extractedData = extractTrendData(trendData)
                
                // Get value and status
                val (value, status) = getTrendVal(
                    extractedData.dataState,
                    extractedData.dataStatus,
                    extractedData.valueText
                )
                
                if (value != null) {
                    // Store with consistent lowercase keys
                    val valueKey = "ibp${channelNum}${component.lowercase()}"
                    val statusKey = "ibp${channelNum}${component}Status"
                    vitalData[valueKey] = value
                    vitalData[statusKey] = status
                }
            }
        }
    }
    
    /**
     * Create a VitalSign object from collected data
     */
    private fun createVitalSignFromData(data: Map<String, Any?>): VitalSign {
        // Get device serial and timeObj
        val deviceSerial = data["deviceSerial"] as? String
        val timeObj = data["timeObj"] as Date
        
        // Map device serial to mannequin name
        val rawMannequin = MANNEQUIN_MAP[deviceSerial]
        
        // Format date string
        val dateStr = SimpleDateFormat("yyyy-MM-dd", Locale.US).format(timeObj)
        
        return VitalSign(
            timeObj = timeObj,
            timeStr = data["timeStr"] as String,
            deviceSerial = deviceSerial,
            sourceFile = data["sourceFile"] as? String,
            
            // Vital signs
            hr = data["hr"] as? Double,
            spO2 = data["spo2"] as? Double,
            nibpSys = data["nibpsys"] as? Double,
            nibpDia = data["nibpdia"] as? Double,
            nibpMap = data["nibpmap"] as? Double,
            temp1 = data["temp1"] as? Double,
            respRate = data["resprate"] as? Double,
            etCO2 = data["etco2"] as? Double,
            fiCO2 = data["fico2"] as? Double,
            
            // Status information
            hrStatus = data["hrStatus"] as? String,
            spO2Status = data["spO2Status"] as? String,
            nibpSysStatus = data["nibpSysStatus"] as? String,
            nibpDiaStatus = data["nibpDiaStatus"] as? String,
            nibpMapStatus = data["nibpMapStatus"] as? String,
            temp1Status = data["temp1Status"] as? String,
            respRateStatus = data["respRateStatus"] as? String,
            etCO2Status = data["etCO2Status"] as? String,
            
            // SpO2-related parameters
            spMet = data["spmet"] as? Double,
            spCo = data["spco"] as? Double,
            pvi = data["pvi"] as? Double,
            pi = data["pi"] as? Double,
            spOC = data["spoc"] as? Double,
            spHb = data["sphb"] as? Double,
            
            // IBP readings
            ibp1Sys = data["ibp1sys"] as? Double,
            ibp1Dia = data["ibp1dia"] as? Double,
            ibp1Map = data["ibp1map"] as? Double,
            
            // Additional fields
            rawMannequin = rawMannequin,
            dateStr = dateStr,
            overrideMannequin = rawMannequin,
            
            // These fields will be populated by the repository
            course = null,
            sim = null,
            scenario = null,
            inSimWindow = false,
            isValid = false
        )
    }
} 