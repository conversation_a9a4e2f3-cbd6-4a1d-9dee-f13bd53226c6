package com.example.myapplication.data

/**
 * Constants and configuration data for the app
 */
object Constants {
    /**
     * Simulation configuration for mannequins and scenarios
     */
    
    /** Mapping from simulation to a list of mannequins for that simulation */
    val SIM_MANNEQUINS = mapOf(
        "Sim1" to list<PERSON><PERSON>("<PERSON>", "<PERSON>"),
        "Sim2" to list<PERSON><PERSON>("<PERSON>", "<PERSON>"),
        "Sim3" to list<PERSON><PERSON>("<PERSON>", "<PERSON>"),
        "Sim4" to list<PERSON><PERSON>("<PERSON>", "<PERSON>"),
        "Sim5" to list<PERSON>f("<PERSON>", "<PERSON>", "<PERSON>"),
        "TrainingSim3" to list<PERSON>f("<PERSON>", "<PERSON>"),
        "TrainingSim5" to list<PERSON>f("<PERSON>", "<PERSON>")
    )
    
    /** Mapping from simulation and mannequin to a specific scenario */
    val SIM_MANNEQUIN_SCENARIO = mapOf(
        "Sim1" to mapOf(
            "<PERSON>" to "T<PERSON>",
            "<PERSON>" to "Pneumonia"
        ),
        "Sim2" to map<PERSON>f(
            "<PERSON>" to "Unmonitored Severe TBI",
            "<PERSON>" to "<PERSON><PERSON>rauma (DCR)"
        ),
        "Sim3" to map<PERSON>f(
            "<PERSON>" to "<PERSON><PERSON>",
            "<PERSON>" to "ACS/STEMI"
        ),
        "Sim4" to map<PERSON><PERSON>(
            "<PERSON>" to "Catastrophic T<PERSON>",
            "<PERSON>" to "Aortic Dissection"
        ),
        "Sim5" to mapOf(
            "<PERSON>" to "Burn",
            "<PERSON>" to "Atrial Fibrillation",
            "Matt" to "Penetrating Thoracoabdominal Injury (DCR)"
        ),
        "TrainingSim3" to mapOf(
            "<PERSON>" to "DCR",
            "Freddy" to "DCR"
        ),
        "TrainingSim5" to mapOf(
            "Dave" to "DCR",
            "Chuck" to "Fever/Seizure/Intubation"
        )
    )
    
    /** Mapping from device serial number to mannequin name */
    val MANNEQUIN_MAP = mapOf(
        "AI23F013939" to "Dave",
        "AI23H014090" to "Chuck",
        "AI15F004305" to "Freddy",
        "AI15D003889" to "Matt",
        "AI20C009617" to "Oscar"
    )
    
    /** Scheduled time blocks for each simulation */
    val SIM_SCHEDULES = mapOf(
        "Sim1" to listOf(
            Pair("08:30", "09:55"),
            Pair("09:55", "11:20"),
            Pair("11:20", "12:45"),
            Pair("13:05", "14:30"),
            Pair("14:30", "15:55"),
            Pair("15:55", "17:20")
        ),
        "Sim2" to listOf(
            Pair("08:00", "09:20"),
            Pair("09:20", "10:40"),
            Pair("10:40", "12:00"),
            Pair("12:20", "13:40"),
            Pair("13:40", "15:00"),
            Pair("15:00", "16:20")
        ),
        "Sim3" to listOf(
            Pair("08:00", "09:30"),
            Pair("09:30", "11:00"),
            Pair("11:00", "12:30"),
            Pair("12:50", "14:20"),
            Pair("14:20", "15:50"),
            Pair("15:50", "17:20")
        ),
        "Sim4" to listOf(
            Pair("08:00", "09:25"),
            Pair("09:25", "10:50"),
            Pair("10:50", "12:15"),
            Pair("12:35", "14:00"),
            Pair("14:00", "15:25"),
            Pair("15:25", "16:50")
        ),
        "Sim5" to listOf(
            Pair("08:00", "09:10"),
            Pair("09:10", "10:20"),
            Pair("10:20", "11:30"),
            Pair("11:50", "13:00"),
            Pair("13:00", "14:10"),
            Pair("14:10", "15:20")
        ),
        "TrainingSim3" to listOf(
            Pair("09:00", "10:30"),
            Pair("10:30", "12:00"),
            Pair("13:00", "14:30"),
            Pair("14:30", "16:00")
        ),
        "TrainingSim5" to listOf(
            Pair("09:00", "10:30"),
            Pair("10:30", "12:00"),
            Pair("13:00", "14:30"),
            Pair("14:30", "16:00")
        )
    )
    
    /** Expected duration in minutes for each simulation */
    val SIM_DURATIONS = mapOf(
        "Sim1" to 45, // 45-minute simulation (begins at cruising altitude; after sim: 5–10 min cleanup + 10-min group debrief + 10-min individual debrief)
        "Sim2" to 35, // 35-minute simulation (7 min prep before landing, 10 min on ground, then 15 min flight; ends with cleanup/debrief)
        "Sim3" to 50, // 50-minute simulation (begins at altitude; no explicit flight phase changes; 5–10 min cleanup + 10-min group + 10-min individual debrief)
        "Sim4" to 55, // 55-minute simulation (time in sim room is 55 total; includes a final ~5-min handoff at the end, then cleanup/debrief)
        "Sim5" to 40,  // 40-minute simulation (10 min initial setup, 10 min ground, 13 min flight, ~7 min buffer; ends with cleanup + debrief)
        "TrainingSim3" to 30, // Training simulation with flexible duration (30-60 minutes) for DCR scenarios
        "TrainingSim5" to 30  // Training simulation with flexible duration (30-60 minutes) for DCR and fever/seizure/intubation scenarios
    )
    
    /** Course date ranges (start_mm/dd, end_mm/dd, course_label) */
    val COURSE_DATE_RANGES = listOf(
        Triple("10/14", "10/18", "2025A"),
        Triple("10/28", "11/01", "2025B"),
        Triple("11/18", "11/22", "2025C"),
        Triple("12/09", "12/13", "2025D"),
        Triple("01/13", "01/17", "2025E"),
        Triple("01/27", "01/31", "2025F"),
        Triple("02/17", "02/21", "2025G/H"),
        Triple("02/24", "02/28", "2025H"),
        Triple("03/24", "03/28", "2025I"),
        Triple("04/14", "04/18", "2025J"),
        Triple("05/05", "05/09", "2025K/L"),
        Triple("05/12", "05/16", "2025L"),
        Triple("06/09", "06/13", "2025M"),
        Triple("06/23", "06/27", "2025N")
    )
    
    /** Course start dates mapping (course -> start date) */
    val COURSE_START_DATES = mapOf(
        "2025A" to "2024-10-14",
        "2025B" to "2024-10-28",
        "2025C" to "2024-11-18",
        "2025D" to "2024-12-09",
        "2025E" to "2025-01-13",
        "2025F" to "2025-01-27",
        "2025G/H" to "2025-02-17",
        "2025H" to "2025-02-24",
        "2025I" to "2025-03-24",
        "2025J" to "2025-04-14",
        "2025K/L" to "2025-05-05",
        "2025L" to "2025-05-12",
        "2025M" to "2025-06-09",
        "2025N" to "2025-06-23"
    )
    
    /** Mapping from day offset to simulation name */
    val DAY_OFFSET_TO_SIM = mapOf(
        0 to "Sim1",
        1 to "Sim2",
        2 to "Sim3",
        3 to "Sim4",
        4 to "Sim5"
    )
}

/**
 * Compatibility objects to maintain backward compatibility with code that references
 * the previous structure.
 */

/**
 * Simulation scenarios for different mannequins (Legacy compatibility object)
 * References the canonical definitions in Constants
 */
object SimulationScenarios {
    val SIM1 = Constants.SIM_MANNEQUIN_SCENARIO["Sim1"] ?: mapOf()
    val SIM2 = Constants.SIM_MANNEQUIN_SCENARIO["Sim2"] ?: mapOf()
    val SIM3 = Constants.SIM_MANNEQUIN_SCENARIO["Sim3"] ?: mapOf()
    val SIM4 = Constants.SIM_MANNEQUIN_SCENARIO["Sim4"] ?: mapOf()
    val SIM5 = Constants.SIM_MANNEQUIN_SCENARIO["Sim5"] ?: mapOf()
    
    val ALL_SCENARIOS = mapOf(
        "Sim1" to SIM1,
        "Sim2" to SIM2,
        "Sim3" to SIM3,
        "Sim4" to SIM4,
        "Sim5" to SIM5
    )
    
    val SIM_MANNEQUINS = Constants.SIM_MANNEQUINS
    val MANNEQUIN_MAP = Constants.MANNEQUIN_MAP
}

/**
 * Course dates (Legacy compatibility object)
 * References the canonical definitions in Constants
 */
object CourseDates {
    val COURSE_DATE_RANGES = Constants.COURSE_DATE_RANGES
    val COURSE_START_DATES = Constants.COURSE_START_DATES
    val DAY_OFFSET_TO_SIM = Constants.DAY_OFFSET_TO_SIM
}

/**
 * Simulation durations (Legacy compatibility object)
 * References the canonical definitions in Constants
 */
object SimulationDurations {
    val SIM_DURATIONS = Constants.SIM_DURATIONS
}

/**
 * Vital sign thresholds for different scenarios
 */
object ClinicalPracticeGuidelines {
    
    // TBI Thresholds (Dave - Sim1)
    val TBI = mapOf(
        "NIBP_SYS" to Pair(110.0, 160.0),
        "IBP_ICP" to Pair(null, 22.0),
        "NIBP_MAP" to Pair(60.0, null),
        "IBP_CPP" to Pair(60.0, 70.0),
        "IBP_CVP" to Pair(5.0, 12.0),
        "Hr" to Pair(60.0, 100.0),
        "SpO2" to Pair(90.0, 96.0),
        "EtCO2" to Pair(35.0, 45.0),
        "TempF" to Pair(96.0, 99.0),
        "SI" to Pair(null, 0.9),
        "RespRate" to Pair(12.0, 20.0)
    )
    
    // Pneumonia Thresholds (Chuck - Sim1)
    // Note: Chuck has primary condition of Pneumonia and secondary conditions of Sepsis and Acute Hypoxic Respiratory Failure
    val PNEUMONIA = mapOf(
        "NIBP_SYS" to Pair(90.0, 140.0),
        "NIBP_MAP" to Pair(65.0, null),
        "Hr" to Pair(60.0, 100.0),
        "SpO2" to Pair(90.0, null),
        "EtCO2" to Pair(35.0, 45.0),
        "TempF" to Pair(96.8, 100.4),
        "RespRate" to Pair(12.0, 20.0)
    )
    
    // TBI Unmonitored Thresholds (Freddy - Sim2)
    val TBI_UNMONITORED = mapOf(
        "NIBP_SYS" to Pair(110.0, 160.0),
        "NIBP_MAP" to Pair(60.0, null),
        "Hr" to Pair(60.0, 100.0),
        "SpO2" to Pair(93.0, null),
        "EtCO2" to Pair(35.0, 45.0),
        "TempF" to Pair(96.0, 99.0),
        "RespRate" to Pair(12.0, 20.0)
    )
    
    // Damage Control Resuscitation Thresholds (Oscar - Sim2)
    val DCR = mapOf(
        "NIBP_SYS" to Pair(100.0, 140.0),
        "NIBP_MAP" to Pair(60.0, null),
        "Hr" to Pair(60.0, 100.0),
        "SpO2" to Pair(92.0, null),
        "EtCO2" to Pair(35.0, 45.0),
        "TempF" to Pair(95.0, null),
        "RespRate" to Pair(12.0, 20.0),
        "SI" to Pair(0.9, null)
    )
    
    // Sepsis ARDS Thresholds (Dave - Sim3)
    val SEPSIS_ARDS = mapOf(
        "NIBP_SYS" to Pair(90.0, 140.0),
        "NIBP_MAP" to Pair(65.0, null),
        "Hr" to Pair(60.0, 90.0),
        "RespRate" to Pair(12.0, 22.0),
        "SpO2" to Pair(88.0, 95.0),
        "EtCO2" to Pair(35.0, 45.0),
        "TempF" to Pair(96.8, 100.4)
    )
    
    // Acute Coronary Syndrome Thresholds (Chuck - Sim3)
    val ACS = mapOf(
        "NIBP_SYS" to Pair(90.0, 160.0),
        "NIBP_MAP" to Pair(70.0, 110.0),
        "Hr" to Pair(60.0, 100.0),
        "SpO2" to Pair(90.0, 98.0),
        "EtCO2" to Pair(35.0, 45.0),
        "TempF" to Pair(96.8, 99.5),
        "RespRate" to Pair(12.0, 20.0),
        "PP" to Pair(40.0, 60.0)
    )
    
    // Catastrophic TBI Thresholds (Dave - Sim4)
    val CATASTROPHIC_TBI = mapOf(
        "NIBP_SYS" to Pair(100.0, 160.0),
        "NIBP_MAP" to Pair(65.0, null),
        "IBP_ICP" to Pair(null, 20.0),
        "IBP_CPP" to Pair(60.0, 70.0),
        "IBP_CVP" to Pair(7.0, 12.0),
        "Hr" to Pair(60.0, 100.0),
        "SpO2" to Pair(93.0, null),
        "EtCO2" to Pair(35.0, 45.0),
        "TempF" to Pair(96.8, 99.0),
        "RespRate" to Pair(12.0, 20.0)
    )
    
    // Aortic Dissection Stroke Thresholds (Chuck - Sim4)
    val AORTIC_DISSECTION_STROKE = mapOf(
        "NIBP_SYS" to Pair(100.0, 120.0),
        "NIBP_MAP" to Pair(65.0, 110.0),
        "Hr" to Pair(60.0, 80.0),
        "SpO2" to Pair(94.0, null),
        "EtCO2" to Pair(35.0, 45.0),
        "TempF" to Pair(96.8, 99.5),
        "RespRate" to Pair(12.0, 20.0)
    )
    
    // Burn Thresholds (Freddy - Sim5)
    val BURN = mapOf(
        "NIBP_SYS" to Pair(90.0, 140.0),
        "NIBP_MAP" to Pair(55.0, null),
        "IBP_CVP" to Pair(6.0, 8.0),
        "Hr" to Pair(60.0, 140.0),
        "SpO2" to Pair(92.0, null),
        "EtCO2" to Pair(35.0, 45.0),
        "TempF" to Pair(98.6, null),
        "RespRate" to Pair(12.0, 20.0)
    )
    
    // Atrial Fibrillation Thresholds (Oscar - Sim5)
    val AFIB = mapOf(
        "NIBP_SYS" to Pair(90.0, 140.0),
        "NIBP_MAP" to Pair(65.0, 110.0),
        "Hr" to Pair(60.0, 110.0),
        "SpO2" to Pair(93.0, null),
        "EtCO2" to Pair(35.0, 45.0),
        "TempF" to Pair(96.8, 99.5),
        "RespRate" to Pair(12.0, 20.0)
    )
    
    // Penetrating Thoracoabdominal Injury Thresholds (Matt - Sim5)
    // Matt has primary condition of Penetrating Thoracoabdominal Injury and secondary condition of Acute Hypoxic Respiratory Failure
    val PENETRATING_THORACOABDOMINAL_INJURY = mapOf(
        "NIBP_SYS" to Pair(100.0, 140.0),
        "NIBP_MAP" to Pair(60.0, null),
        "Hr" to Pair(60.0, 100.0),
        "SpO2" to Pair(92.0, null),
        "EtCO2" to Pair(35.0, 45.0),
        "TempF" to Pair(95.0, null),
        "RespRate" to Pair(12.0, 20.0),
        "SI" to Pair(0.9, null)
    )
    
    // Fever Seizure Intubation Thresholds (Chuck - Training Sim5)
    val FEVER_SEIZURE_INTUBATION = mapOf(
        "NIBP_SYS" to Pair(90.0, 140.0),
        "NIBP_MAP" to Pair(65.0, null),
        "Hr" to Pair(60.0, 120.0),
        "SpO2" to Pair(93.0, null),
        "EtCO2" to Pair(35.0, 45.0),
        "TempF" to Pair(98.6, 101.5),
        "RespRate" to Pair(12.0, 24.0)
    )
    
    // Legacy aliases for compatibility
    val SEPSIS = PNEUMONIA
    
    // All thresholds mapped by scenario name
    val ALL_THRESHOLDS = mapOf(
        "TBI" to TBI,
        "Unmonitored Severe TBI" to TBI_UNMONITORED,
        "Pneumonia" to PNEUMONIA,
        "Sepsis" to SEPSIS_ARDS,
        "ACS/STEMI" to ACS,
        "Catastrophic TBI" to CATASTROPHIC_TBI,
        "Aortic Dissection" to AORTIC_DISSECTION_STROKE,
        "Burn" to BURN,
        "Atrial Fibrillation" to AFIB,
        "Penetrating Thoracoabdominal Injury (DCR)" to PENETRATING_THORACOABDOMINAL_INJURY,
        "DCR" to DCR,
        "Fever/Seizure/Intubation" to FEVER_SEIZURE_INTUBATION
    )
}

/**
 * Requirements for different scenarios
 */
object ScenarioRequirements {
    val TBI = mapOf(
        "critical_vitals" to listOf("NIBP_MAP", "ICP", "CPP", "Hr", "SpO2", "EtCO2", "TempF", "RespRate"),
        "labs" to listOf("Glucose", "Na", "K"),
        "interventions" to listOf("EVD_zeroing", "sedation_adjustment", "3%_saline", "insulin", "vasopressors"),
        "events" to listOf("ICP_spike", "CPP_low", "sedation_light", "hyperglycemia", "hypernatremia", "fever")
    )
    
    val CATASTROPHIC_TBI = mapOf(
        "critical_vitals" to listOf("NIBP_MAP", "ICP", "CPP", "Hr", "SpO2", "EtCO2", "TempF", "RespRate"),
        "labs" to listOf("Glucose", "Na", "K"),
        "interventions" to listOf("EVD_zeroing", "sedation_adjustment", "3%_saline", "insulin", "vasopressors"),
        "events" to listOf("ICP_spike", "CPP_low", "sedation_light", "hyperglycemia", "hypernatremia", "fever")
    )
    
    val TBI_UNMONITORED = mapOf(
        "critical_vitals" to listOf("NIBP_MAP", "Hr", "SpO2", "EtCO2", "TempF", "RespRate"),
        "labs" to listOf("INR", "PT", "Glucose"),
        "interventions" to listOf("blood_products", "vasopressors", "sedation_adjustment", "glucose_control"),
        "events" to listOf("coagulopathy", "hypotension", "under_sedation", "hyperglycemia")
    )
    
    val PNEUMONIA_SEPSIS = mapOf(
        "critical_vitals" to listOf("NIBP_MAP", "Hr", "SpO2", "RespRate", "TempF"),
        "labs" to listOf("Lactate", "WBC", "Creatinine"),
        "interventions" to listOf("antibiotics", "fluid_bolus", "vasopressors", "oxygen_therapy"),
        "events" to listOf("hypotension", "hypoxia", "fever", "tachycardia")
    )
    
    val SEPSIS_ARDS = mapOf(
        "critical_vitals" to listOf("NIBP_MAP", "Hr", "SpO2", "RespRate", "TempF", "PEEP", "FiO2"),
        "labs" to listOf("K", "Lactate", "ABG"),
        "interventions" to listOf("vent_adjustment", "proning", "antibiotics", "vasopressors"),
        "events" to listOf("hypoxia", "fever", "shock", "ventilator_dyssynchrony")
    )
    
    val ACS = mapOf(
        "critical_vitals" to listOf("NIBP_SYS", "NIBP_MAP", "Hr", "SpO2", "RespRate"),
        "labs" to listOf("Troponin", "CK_MB", "K"),
        "interventions" to listOf("antiplatelet", "anticoagulation", "pain_control", "beta_blockers"),
        "events" to listOf("chest_pain", "arrhythmia", "hypotension", "heart_failure")
    )
    
    val DCR = mapOf(
        "critical_vitals" to listOf("NIBP_SYS", "NIBP_MAP", "Hr", "SpO2", "TempF", "RespRate"),
        "labs" to listOf("Hgb", "INR", "Lactate", "Base_deficit"),
        "interventions" to listOf("blood_products", "TXA", "calcium", "warming"),
        "events" to listOf("hemorrhage", "coagulopathy", "hypothermia", "acidosis")
    )
    
    val BURN = mapOf(
        "critical_vitals" to listOf("NIBP_MAP", "Hr", "SpO2", "TempF", "RespRate", "UOP"),
        "labs" to listOf("HCT", "Lactate", "Base_deficit", "K"),
        "interventions" to listOf("fluid_resuscitation", "escharotomy", "pain_control", "airway_management"),
        "events" to listOf("airway_compromise", "compartment_syndrome", "hypovolemia", "hypothermia")
    )
    
    val AFIB = mapOf(
        "critical_vitals" to listOf("NIBP_SYS", "Hr", "SpO2", "RespRate"),
        "labs" to listOf("K", "Mg", "Digoxin_level"),
        "interventions" to listOf("rate_control", "rhythm_control", "anticoagulation", "cardioversion"),
        "events" to listOf("rapid_ventricular_response", "heart_failure", "stroke", "hypotension")
    )
    
    val PENETRATING_THORACOABDOMINAL_INJURY = mapOf(
        "critical_vitals" to listOf("NIBP_SYS", "NIBP_MAP", "Hr", "SpO2", "TempF", "RespRate", "GCS"),
        "labs" to listOf("Hgb", "INR", "Base_deficit", "Lactate"),
        "interventions" to listOf("blood_products", "chest_tube", "FAST_exam", "surgical_intervention"),
        "events" to listOf("pneumothorax", "hemorrhage", "hypotension", "respiratory_failure")
    )
    
    val AORTIC_DISSECTION_STROKE = mapOf(
        "critical_vitals" to listOf("NIBP_SYS", "NIBP_MAP", "Hr", "SpO2", "RespRate", "Pain_scale"),
        "labs" to listOf("Troponin", "D-dimer", "CT_angiogram"),
        "interventions" to listOf("beta_blockers", "pain_control", "blood_pressure_control", "surgical_intervention"),
        "events" to listOf("severe_pain", "pulse_deficit", "neurological_deficit", "heart_failure")
    )
    
    val FEVER_SEIZURE_INTUBATION = mapOf(
        "critical_vitals" to listOf("NIBP_SYS", "NIBP_MAP", "Hr", "SpO2", "TempF", "RespRate", "EtCO2"),
        "labs" to listOf("Glucose", "Na", "CBC", "CSF_Studies"),
        "interventions" to listOf("antipyretics", "anticonvulsants", "intubation", "mechanical_ventilation"),
        "events" to listOf("seizure", "hyperthermia", "respiratory_depression", "increased_ICP")
    )
    
    // Legacy aliases for compatibility
    val SEPSIS = PNEUMONIA_SEPSIS
    val PNEUMONIA = PNEUMONIA_SEPSIS
    
    // All requirements mapped by scenario name
    val ALL_REQUIREMENTS = mapOf(
        "TBI" to TBI,
        "Unmonitored Severe TBI" to TBI_UNMONITORED,
        "Pneumonia" to PNEUMONIA_SEPSIS,
        "Sepsis" to SEPSIS_ARDS,
        "ACS/STEMI" to ACS,
        "Catastrophic TBI" to CATASTROPHIC_TBI,
        "Aortic Dissection" to AORTIC_DISSECTION_STROKE,
        "Burn" to BURN,
        "Atrial Fibrillation" to AFIB,
        "Penetrating Thoracoabdominal Injury (DCR)" to PENETRATING_THORACOABDOMINAL_INJURY,
        "DCR" to DCR,
        "Fever/Seizure/Intubation" to FEVER_SEIZURE_INTUBATION
    )
} 