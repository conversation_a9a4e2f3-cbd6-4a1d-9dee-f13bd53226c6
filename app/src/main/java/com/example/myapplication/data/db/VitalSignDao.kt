package com.example.myapplication.data.db

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import kotlinx.coroutines.flow.Flow
import java.util.Date

/**
 * Data Access Object for VitalSign database operations
 */
@Dao
interface VitalSignDao {
    /**
     * Insert a single vital sign record
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(vitalSign: VitalSignEntity): Long
    
    /**
     * Insert multiple vital sign records
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAll(vitalSigns: List<VitalSignEntity>): List<Long>
    
    /**
     * Delete a vital sign record
     */
    @Delete
    suspend fun delete(vitalSign: VitalSignEntity)
    
    /**
     * Delete all vital signs
     */
    @Query("DELETE FROM vital_signs")
    suspend fun deleteAll()
    
    /**
     * Get all vital signs
     */
    @Query("SELECT * FROM vital_signs ORDER BY timeObj ASC")
    fun getAllVitalSigns(): Flow<List<VitalSignEntity>>
    
    /**
     * Get vital signs within a date range
     */
    @Query("SELECT * FROM vital_signs WHERE timeObj BETWEEN :startDate AND :endDate ORDER BY timeObj ASC")
    fun getVitalSignsByDateRange(startDate: Date, endDate: Date): Flow<List<VitalSignEntity>>
    
    /**
     * Get vital signs for a specific mannequin
     */
    @Query("SELECT * FROM vital_signs WHERE overrideMannequin = :mannequin ORDER BY timeObj ASC")
    fun getVitalSignsByMannequin(mannequin: String): Flow<List<VitalSignEntity>>
    
    /**
     * Get vital signs for a specific scenario
     */
    @Query("SELECT * FROM vital_signs WHERE scenario = :scenario ORDER BY timeObj ASC")
    fun getVitalSignsByScenario(scenario: String): Flow<List<VitalSignEntity>>
    
    /**
     * Get vital signs for a specific mannequin and scenario
     */
    @Query("SELECT * FROM vital_signs WHERE overrideMannequin = :mannequin AND scenario = :scenario ORDER BY timeObj ASC")
    fun getVitalSignsByMannequinAndScenario(mannequin: String, scenario: String): Flow<List<VitalSignEntity>>
    
    /**
     * Get vital signs for a specific simulation
     */
    @Query("SELECT * FROM vital_signs WHERE sim = :simulation ORDER BY timeObj ASC")
    fun getVitalSignsBySimulation(simulation: String): Flow<List<VitalSignEntity>>
    
    /**
     * Get count of vital signs in the database
     */
    @Query("SELECT COUNT(*) FROM vital_signs")
    suspend fun getCount(): Int
    
    /**
     * Get latest vital sign for a mannequin
     */
    @Query("SELECT * FROM vital_signs WHERE overrideMannequin = :mannequin ORDER BY timeObj DESC LIMIT 1")
    suspend fun getLatestVitalSignForMannequin(mannequin: String): VitalSignEntity?
    
    /**
     * Get all distinct mannequins in the database
     */
    @Query("SELECT DISTINCT overrideMannequin FROM vital_signs WHERE overrideMannequin IS NOT NULL")
    suspend fun getDistinctMannequins(): List<String>
    
    /**
     * Get all distinct scenarios in the database
     */
    @Query("SELECT DISTINCT scenario FROM vital_signs WHERE scenario IS NOT NULL")
    suspend fun getDistinctScenarios(): List<String>
} 