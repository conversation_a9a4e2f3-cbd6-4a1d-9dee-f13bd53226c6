package com.example.myapplication.data.model

import android.util.Log

/**
 * Constants for scenario thresholds for vital signs
 */
object ScenarioThresholds {
    /**
     * Threshold class for vital signs with warning and critical levels
     */
    data class VitalSignThreshold(
        val warningLow: Double,
        val warningHigh: Double,
        val criticalLow: Double,
        val criticalHigh: Double
    )

    // TBI (Traumatic Brain Injury) scenario thresholds
    val TBI = mapOf(
        "hr" to VitalSignThreshold(
            warningLow = 50.0, warningHigh = 100.0,
            criticalLow = 40.0, criticalHigh = 120.0
        ),
        "spo2" to VitalSignThreshold(
            warningLow = 90.0, warningHigh = 96.0,
            criticalLow = 85.0, criticalHigh = 100.0
        ),
        "nibp_sys" to VitalSignThreshold(
            warningLow = 110.0, warningHigh = 160.0,
            criticalLow = 100.0, criticalHigh = 180.0
        ),
        "nibp_dia" to VitalSignThreshold(
            warningLow = 60.0, warningHigh = 90.0,
            criticalLow = 50.0, criticalHigh = 100.0
        ),
        "nibp_map" to VitalSignThreshold(
            warningLow = 60.0, warningHigh = 105.0,
            criticalLow = 55.0, criticalHigh = 120.0
        ),
        "temp" to VitalSignThreshold(
            warningLow = 96.0, warningHigh = 99.0,
            criticalLow = 95.0, criticalHigh = 100.0
        ),
        "etco2" to VitalSignThreshold(
            warningLow = 35.0, warningHigh = 45.0,
            criticalLow = 30.0, criticalHigh = 50.0
        ),
        "icp" to VitalSignThreshold(
            warningLow = 0.0, warningHigh = 22.0,
            criticalLow = 0.0, criticalHigh = 25.0
        ),
        "cpp" to VitalSignThreshold(
            warningLow = 60.0, warningHigh = 70.0,
            criticalLow = 55.0, criticalHigh = 80.0
        )
    )

    // PNEUMONIA scenario thresholds
    val PNEUMONIA = mapOf(
        "hr" to VitalSignThreshold(
            warningLow = 60.0, warningHigh = 100.0,
            criticalLow = 50.0, criticalHigh = 120.0
        ),
        "spo2" to VitalSignThreshold(
            warningLow = 90.0, warningHigh = 100.0,
            criticalLow = 85.0, criticalHigh = 100.0
        ),
        "nibp_sys" to VitalSignThreshold(
            warningLow = 90.0, warningHigh = 140.0,
            criticalLow = 85.0, criticalHigh = 160.0
        ),
        "nibp_dia" to VitalSignThreshold(
            warningLow = 60.0, warningHigh = 90.0,
            criticalLow = 50.0, criticalHigh = 100.0
        ),
        "nibp_map" to VitalSignThreshold(
            warningLow = 65.0, warningHigh = 105.0,
            criticalLow = 60.0, criticalHigh = 110.0
        ),
        "temp" to VitalSignThreshold(
            warningLow = 96.8, warningHigh = 100.4,
            criticalLow = 96.0, criticalHigh = 101.5
        ),
        "etco2" to VitalSignThreshold(
            warningLow = 35.0, warningHigh = 45.0,
            criticalLow = 30.0, criticalHigh = 50.0
        ),
        "resp_rate" to VitalSignThreshold(
            warningLow = 12.0, warningHigh = 22.0,
            criticalLow = 10.0, criticalHigh = 30.0
        )
    )

    // UNMONITORED_SEVERE_TBI scenario thresholds
    val UNMONITORED_SEVERE_TBI = mapOf(
        "hr" to VitalSignThreshold(
            warningLow = 50.0, warningHigh = 100.0,
            criticalLow = 40.0, criticalHigh = 120.0
        ),
        "spo2" to VitalSignThreshold(
            warningLow = 93.0, warningHigh = 100.0,
            criticalLow = 90.0, criticalHigh = 100.0
        ),
        "nibp_sys" to VitalSignThreshold(
            warningLow = 110.0, warningHigh = 160.0,
            criticalLow = 100.0, criticalHigh = 180.0
        ),
        "nibp_map" to VitalSignThreshold(
            warningLow = 60.0, warningHigh = 105.0,
            criticalLow = 55.0, criticalHigh = 110.0
        ),
        "temp" to VitalSignThreshold(
            warningLow = 96.0, warningHigh = 99.0,
            criticalLow = 95.0, criticalHigh = 100.0
        ),
        "etco2" to VitalSignThreshold(
            warningLow = 35.0, warningHigh = 45.0,
            criticalLow = 30.0, criticalHigh = 50.0
        ),
        "resp_rate" to VitalSignThreshold(
            warningLow = 12.0, warningHigh = 20.0,
            criticalLow = 10.0, criticalHigh = 25.0
        )
    )

    // POLYTRAUMA_DCR scenario thresholds (Damage Control Resuscitation)
    val POLYTRAUMA_DCR = mapOf(
        "hr" to VitalSignThreshold(
            warningLow = 60.0, warningHigh = 100.0,
            criticalLow = 50.0, criticalHigh = 120.0
        ),
        "spo2" to VitalSignThreshold(
            warningLow = 92.0, warningHigh = 100.0,
            criticalLow = 88.0, criticalHigh = 100.0
        ),
        "nibp_sys" to VitalSignThreshold(
            warningLow = 100.0, warningHigh = 140.0,
            criticalLow = 90.0, criticalHigh = 160.0
        ),
        "nibp_map" to VitalSignThreshold(
            warningLow = 60.0, warningHigh = 100.0,
            criticalLow = 55.0, criticalHigh = 110.0
        ),
        "temp" to VitalSignThreshold(
            warningLow = 95.0, warningHigh = 99.0,
            criticalLow = 94.0, criticalHigh = 100.0
        ),
        "etco2" to VitalSignThreshold(
            warningLow = 35.0, warningHigh = 45.0,
            criticalLow = 30.0, criticalHigh = 50.0
        ),
        "shock_index" to VitalSignThreshold(
            warningLow = 0.9, warningHigh = 1.2,
            criticalLow = 0.8, criticalHigh = 1.4
        )
    )

    // SEPSIS scenario thresholds
    val SEPSIS = mapOf(
        "hr" to VitalSignThreshold(
            warningLow = 60.0, warningHigh = 90.0,
            criticalLow = 50.0, criticalHigh = 110.0
        ),
        "spo2" to VitalSignThreshold(
            warningLow = 88.0, warningHigh = 95.0,
            criticalLow = 85.0, criticalHigh = 100.0
        ),
        "nibp_sys" to VitalSignThreshold(
            warningLow = 90.0, warningHigh = 140.0,
            criticalLow = 85.0, criticalHigh = 160.0
        ),
        "nibp_map" to VitalSignThreshold(
            warningLow = 65.0, warningHigh = 100.0,
            criticalLow = 60.0, criticalHigh = 110.0
        ),
        "temp" to VitalSignThreshold(
            warningLow = 96.8, warningHigh = 100.4,
            criticalLow = 96.0, criticalHigh = 101.5
        ),
        "resp_rate" to VitalSignThreshold(
            warningLow = 12.0, warningHigh = 22.0,
            criticalLow = 10.0, criticalHigh = 25.0
        )
    )

    // ACS_STEMI scenario thresholds (Acute Coronary Syndrome / ST-Elevation Myocardial Infarction)
    val ACS_STEMI = mapOf(
        "hr" to VitalSignThreshold(
            warningLow = 60.0, warningHigh = 100.0,
            criticalLow = 55.0, criticalHigh = 110.0
        ),
        "spo2" to VitalSignThreshold(
            warningLow = 90.0, warningHigh = 98.0,
            criticalLow = 88.0, criticalHigh = 100.0
        ),
        "nibp_sys" to VitalSignThreshold(
            warningLow = 90.0, warningHigh = 160.0,
            criticalLow = 85.0, criticalHigh = 180.0
        ),
        "nibp_map" to VitalSignThreshold(
            warningLow = 70.0, warningHigh = 110.0,
            criticalLow = 65.0, criticalHigh = 120.0
        ),
        "pulse_pressure" to VitalSignThreshold(
            warningLow = 40.0, warningHigh = 60.0,
            criticalLow = 35.0, criticalHigh = 70.0
        )
    )

    // CATASTROPHIC_TBI scenario thresholds
    val CATASTROPHIC_TBI = mapOf(
        "hr" to VitalSignThreshold(
            warningLow = 50.0, warningHigh = 100.0,
            criticalLow = 40.0, criticalHigh = 120.0
        ),
        "spo2" to VitalSignThreshold(
            warningLow = 93.0, warningHigh = 100.0,
            criticalLow = 90.0, criticalHigh = 100.0
        ),
        "nibp_sys" to VitalSignThreshold(
            warningLow = 100.0, warningHigh = 160.0,
            criticalLow = 90.0, criticalHigh = 180.0
        ),
        "nibp_map" to VitalSignThreshold(
            warningLow = 65.0, warningHigh = 105.0,
            criticalLow = 60.0, criticalHigh = 110.0
        ),
        "temp" to VitalSignThreshold(
            warningLow = 96.0, warningHigh = 99.0,
            criticalLow = 95.0, criticalHigh = 100.0
        ),
        "etco2" to VitalSignThreshold(
            warningLow = 35.0, warningHigh = 45.0,
            criticalLow = 30.0, criticalHigh = 50.0
        ),
        "icp" to VitalSignThreshold(
            warningLow = 0.0, warningHigh = 20.0,
            criticalLow = 0.0, criticalHigh = 25.0
        ),
        "cpp" to VitalSignThreshold(
            warningLow = 60.0, warningHigh = 70.0,
            criticalLow = 55.0, criticalHigh = 80.0
        )
    )

    // AORTIC_DISSECTION scenario thresholds
    val AORTIC_DISSECTION = mapOf(
        "hr" to VitalSignThreshold(
            warningLow = 60.0, warningHigh = 80.0,
            criticalLow = 55.0, criticalHigh = 90.0
        ),
        "spo2" to VitalSignThreshold(
            warningLow = 94.0, warningHigh = 100.0,
            criticalLow = 90.0, criticalHigh = 100.0
        ),
        "nibp_sys" to VitalSignThreshold(
            warningLow = 100.0, warningHigh = 120.0,
            criticalLow = 90.0, criticalHigh = 130.0
        ),
        "temp" to VitalSignThreshold(
            warningLow = 96.0, warningHigh = 99.5,
            criticalLow = 95.0, criticalHigh = 100.5
        )
    )

    // BURN scenario thresholds
    val BURN = mapOf(
        "hr" to VitalSignThreshold(
            warningLow = 60.0, warningHigh = 140.0,
            criticalLow = 50.0, criticalHigh = 160.0
        ),
        "spo2" to VitalSignThreshold(
            warningLow = 92.0, warningHigh = 100.0,
            criticalLow = 88.0, criticalHigh = 100.0
        ),
        "nibp_map" to VitalSignThreshold(
            warningLow = 55.0, warningHigh = 100.0,
            criticalLow = 50.0, criticalHigh = 110.0
        ),
        "temp" to VitalSignThreshold(
            warningLow = 98.6, warningHigh = 100.0,
            criticalLow = 98.0, criticalHigh = 101.0
        ),
        "cvp" to VitalSignThreshold(
            warningLow = 6.0, warningHigh = 8.0,
            criticalLow = 4.0, criticalHigh = 10.0
        )
    )

    // ATRIAL_FIBRILLATION scenario thresholds
    val ATRIAL_FIBRILLATION = mapOf(
        "hr" to VitalSignThreshold(
            warningLow = 60.0, warningHigh = 110.0,
            criticalLow = 50.0, criticalHigh = 130.0
        ),
        "spo2" to VitalSignThreshold(
            warningLow = 93.0, warningHigh = 100.0,
            criticalLow = 90.0, criticalHigh = 100.0
        ),
        "nibp_sys" to VitalSignThreshold(
            warningLow = 90.0, warningHigh = 140.0,
            criticalLow = 85.0, criticalHigh = 160.0
        )
    )

    // PENETRATING_THORACOABDOMINAL_INJURY scenario thresholds (DCR)
    val PENETRATING_THORACOABDOMINAL_INJURY = mapOf(
        "hr" to VitalSignThreshold(
            warningLow = 60.0, warningHigh = 100.0,
            criticalLow = 50.0, criticalHigh = 120.0
        ),
        "spo2" to VitalSignThreshold(
            warningLow = 92.0, warningHigh = 100.0,
            criticalLow = 88.0, criticalHigh = 100.0
        ),
        "nibp_sys" to VitalSignThreshold(
            warningLow = 100.0, warningHigh = 140.0,
            criticalLow = 90.0, criticalHigh = 160.0
        ),
        "nibp_map" to VitalSignThreshold(
            warningLow = 60.0, warningHigh = 100.0,
            criticalLow = 55.0, criticalHigh = 110.0
        ),
        "temp" to VitalSignThreshold(
            warningLow = 95.0, warningHigh = 99.0,
            criticalLow = 94.0, criticalHigh = 100.0
        ),
        "etco2" to VitalSignThreshold(
            warningLow = 35.0, warningHigh = 45.0,
            criticalLow = 30.0, criticalHigh = 50.0
        ),
        "shock_index" to VitalSignThreshold(
            warningLow = 0.9, warningHigh = 1.2,
            criticalLow = 0.8, criticalHigh = 1.4
        )
    )

    // FEVER_SEIZURE_INTUBATION scenario thresholds (for Training Sim 5 - Chuck)
    val FEVER_SEIZURE_INTUBATION = mapOf(
        "hr" to VitalSignThreshold(
            warningLow = 60.0, warningHigh = 120.0,
            criticalLow = 50.0, criticalHigh = 140.0
        ),
        "spo2" to VitalSignThreshold(
            warningLow = 90.0, warningHigh = 100.0,
            criticalLow = 85.0, criticalHigh = 100.0
        ),
        "nibp_sys" to VitalSignThreshold(
            warningLow = 90.0, warningHigh = 140.0,
            criticalLow = 85.0, criticalHigh = 160.0
        ),
        "nibp_map" to VitalSignThreshold(
            warningLow = 65.0, warningHigh = 100.0,
            criticalLow = 60.0, criticalHigh = 110.0
        ),
        "temp" to VitalSignThreshold(
            warningLow = 96.8, warningHigh = 102.0,
            criticalLow = 96.0, criticalHigh = 103.0
        ),
        "etco2" to VitalSignThreshold(
            warningLow = 35.0, warningHigh = 45.0,
            criticalLow = 30.0, criticalHigh = 50.0
        ),
        "resp_rate" to VitalSignThreshold(
            warningLow = 12.0, warningHigh = 30.0,
            criticalLow = 10.0, criticalHigh = 40.0
        )
    )

    // For backward compatibility, maintain the NORMAL scenario
    val NORMAL = mapOf(
        "hr" to VitalSignThreshold(
            warningLow = 60.0, warningHigh = 100.0,
            criticalLow = 50.0, criticalHigh = 120.0
        ),
        "spo2" to VitalSignThreshold(
            warningLow = 92.0, warningHigh = 100.0,
            criticalLow = 88.0, criticalHigh = 100.0
        ),
        "nibp_sys" to VitalSignThreshold(
            warningLow = 90.0, warningHigh = 140.0,
            criticalLow = 80.0, criticalHigh = 160.0
        ),
        "nibp_dia" to VitalSignThreshold(
            warningLow = 60.0, warningHigh = 90.0,
            criticalLow = 50.0, criticalHigh = 100.0
        ),
        "nibp_map" to VitalSignThreshold(
            warningLow = 65.0, warningHigh = 105.0,
            criticalLow = 60.0, criticalHigh = 120.0
        ),
        "temp" to VitalSignThreshold(
            warningLow = 96.8, warningHigh = 99.5,
            criticalLow = 96.0, criticalHigh = 100.5
        ),
        "resp_rate" to VitalSignThreshold(
            warningLow = 12.0, warningHigh = 20.0,
            criticalLow = 10.0, criticalHigh = 24.0
        ),
        "etco2" to VitalSignThreshold(
            warningLow = 35.0, warningHigh = 45.0,
            criticalLow = 30.0, criticalHigh = 50.0
        )
    )

    /**
     * Helper function to get thresholds by scenario name
     * @param scenarioName The name of the scenario
     * @return Map of thresholds for each vital sign
     */
    fun getThresholdsByScenario(scenarioName: String): Map<String, VitalSignThreshold> {
        return when (scenarioName) {
            "Normal" -> NORMAL
            "TBI" -> TBI
            "Pneumonia" -> PNEUMONIA
            "Unmonitored Severe TBI" -> UNMONITORED_SEVERE_TBI
            "Polytrauma (DCR)" -> POLYTRAUMA_DCR
            "Sepsis" -> SEPSIS
            "ACS/STEMI" -> ACS_STEMI
            "Catastrophic TBI" -> CATASTROPHIC_TBI
            "Aortic Dissection" -> AORTIC_DISSECTION
            "Burn" -> BURN
            "Atrial Fibrillation" -> ATRIAL_FIBRILLATION
            "Penetrating Thoracoabdominal Injury (DCR)" -> PENETRATING_THORACOABDOMINAL_INJURY
            "Fever/Seizure/Intubation" -> FEVER_SEIZURE_INTUBATION
            "DCR" -> POLYTRAUMA_DCR // Alias for Damage Control Resuscitation
            else -> NORMAL // Default to normal thresholds
        }
    }

    /**
     * Get a list of all available scenarios
     */
    fun getAllScenarios(): List<String> {
        return listOf(
            "Normal",
            "TBI",
            "Pneumonia",
            "Unmonitored Severe TBI",
            "Polytrauma (DCR)",
            "Sepsis",
            "ACS/STEMI",
            "Catastrophic TBI",
            "Aortic Dissection",
            "Burn",
            "Atrial Fibrillation",
            "Penetrating Thoracoabdominal Injury (DCR)",
            "Fever/Seizure/Intubation",
            "DCR"
        )
    }

    /**
     * Returns the scenarios used in each simulation
     */
    fun getSimulationScenarios(simName: String): Map<String, String> {
        return when (simName) {
            "Sim1" -> mapOf(
                "Dave" to "TBI",
                "Chuck" to "Pneumonia"
            )
            "Sim2" -> mapOf(
                "Freddy" to "Unmonitored Severe TBI",
                "Oscar" to "Polytrauma (DCR)"
            )
            "Sim3" -> mapOf(
                "Dave" to "Sepsis",
                "Chuck" to "ACS/STEMI"
            )
            "Sim4" -> mapOf(
                "Dave" to "Catastrophic TBI",
                "Chuck" to "Aortic Dissection"
            )
            "Sim5" -> mapOf(
                "Freddy" to "Burn",
                "Oscar" to "Atrial Fibrillation",
                "Matt" to "Penetrating Thoracoabdominal Injury (DCR)"
            )
            "TrainingSim3" -> mapOf(
                "Oscar" to "DCR",
                "Freddy" to "DCR"
            )
            "TrainingSim5" -> mapOf(
                "Dave" to "DCR",
                "Chuck" to "Fever/Seizure/Intubation"
            )
            else -> mapOf("Unknown" to "Normal")
        }
    }

    /**
     * Converts thresholds from ClinicalPracticeGuidelines format to ScenarioThresholds format
     * @param thresholds Map of vital sign name to pair of min and max values
     * @param scenarioName The name of the scenario for logging/context
     * @return Map of vital sign name to VitalSignThreshold
     */
    fun convertToNewFormat(
        thresholds: Map<String, Pair<Double?, Double?>>,
        scenarioName: String
    ): Map<String, VitalSignThreshold> {
        val result = mutableMapOf<String, VitalSignThreshold>()

        // Log the conversion process for debugging
        Log.d("ScenarioThresholds", "Converting thresholds for scenario: $scenarioName")
        Log.d("ScenarioThresholds", "Input thresholds: ${thresholds.keys}")

        // Create a mapping from ClinicalPracticeGuidelines keys to evaluateVitalSignThresholds keys
        val keyMapping = mapOf(
            "NIBP_SYS" to "nibp_sys",
            "NIBP_DIA" to "nibp_dia",
            "NIBP_MAP" to "nibp_map",
            "Hr" to "hr",
            "SpO2" to "spo2",
            "EtCO2" to "etco2",
            "TempF" to "temp",
            "RespRate" to "resp_rate",
            "IBP_ICP" to "icp",
            "IBP_CPP" to "cpp",
            "IBP_CVP" to "cvp"
        )

        thresholds.forEach { (originalVitalSignName, thresholdPair) ->
            val (min, max) = thresholdPair

            // Normalize the key to match the keys used in evaluateVitalSignThresholds
            val normalizedKey = keyMapping[originalVitalSignName] ?: originalVitalSignName.lowercase()

            // Create appropriate thresholds based on min/max values
            // If min or max is null, use sensible defaults to avoid null issues
            val warningLow = min ?: when(normalizedKey) {
                "hr" -> 60.0
                "spo2" -> 92.0
                "nibp_sys" -> 90.0
                "nibp_dia" -> 60.0
                "nibp_map" -> 65.0
                "temp" -> 96.8
                "etco2" -> 35.0
                "resp_rate" -> 12.0
                else -> 0.0
            }

            val warningHigh = max ?: when(normalizedKey) {
                "hr" -> 100.0
                "spo2" -> 100.0
                "nibp_sys" -> 140.0
                "nibp_dia" -> 90.0
                "nibp_map" -> 105.0
                "temp" -> 99.5
                "etco2" -> 45.0
                "resp_rate" -> 20.0
                else -> 100.0
            }

            // Create critical thresholds slightly wider than warning thresholds
            // Critical margins are approximated reasonably
            val criticalLow = min?.let { it * 0.9 } ?: warningLow * 0.9
            val criticalHigh = max?.let { it * 1.1 } ?: warningHigh * 1.1

            // Add to result map using the normalized key
            result[normalizedKey] = VitalSignThreshold(
                warningLow = warningLow,
                warningHigh = warningHigh,
                criticalLow = criticalLow,
                criticalHigh = criticalHigh
            )

            // Log the conversion for this vital sign
            Log.d("ScenarioThresholds", "Converted $originalVitalSignName to $normalizedKey: warning($warningLow-$warningHigh), critical($criticalLow-$criticalHigh)")
        }

        return result
    }
}