package com.example.myapplication.data.source

import com.example.myapplication.data.db.VitalSignDao
import com.example.myapplication.data.db.VitalSignEntity
import com.example.myapplication.data.model.ClinicalEvent
import com.example.myapplication.data.model.VitalSign
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.first
import java.io.File
import java.util.Date

/**
 * Database-backed implementation of VitalSignsDataSource
 * Provides persistent storage and retrieval of vital signs data
 */
class DatabaseVitalSignsDataSource(private val vitalSignDao: VitalSignDao) : VitalSignsDataSource {
    
    override suspend fun loadDataFromDirectory(directory: File): List<VitalSign> {
        // Not applicable for database source - return empty list
        return emptyList()
    }
    
    override suspend fun loadDataFromFile(file: File): List<VitalSign> {
        // Not applicable for database source - return empty list
        return emptyList()
    }
    
    override fun getRealtimeVitalSigns(): Flow<VitalSign> {
        throw NotImplementedError("Real-time monitoring not implemented for database source")
    }
    
    override suspend fun startMonitoring(
        ipAddress: String,
        port: Int,
        authRequired: Boolean,
        username: String,
        password: String
    ): Boolean {
        // Not applicable for database source
        return false
    }
    
    override suspend fun stopMonitoring() {
        // Not applicable for database source
    }
    
    override fun generateSimulatedVitalSign(previousVitalSign: VitalSign?): VitalSign {
        throw NotImplementedError("Simulation not implemented for database source")
    }
    
    override suspend fun detectClinicalEvents(vitalSigns: List<VitalSign>, scenario: String): List<ClinicalEvent> {
        // This could be implemented later with logic from the repository
        return emptyList()
    }
    
    /**
     * Save vital signs to the database
     */
    suspend fun saveVitalSigns(vitalSigns: List<VitalSign>) {
        val entities = vitalSigns.map { VitalSignEntity.fromVitalSign(it) }
        vitalSignDao.insertAll(entities)
    }
    
    /**
     * Save a single vital sign to the database
     */
    suspend fun saveVitalSign(vitalSign: VitalSign): Long {
        val entity = VitalSignEntity.fromVitalSign(vitalSign)
        return vitalSignDao.insert(entity)
    }
    
    /**
     * Delete all vital signs from the database
     */
    suspend fun clearAll() {
        vitalSignDao.deleteAll()
    }
    
    /**
     * Get all vital signs from the database
     */
    fun getAllVitalSigns(): Flow<List<VitalSign>> {
        return vitalSignDao.getAllVitalSigns().map { entities ->
            entities.map { it.toVitalSign() }
        }
    }
    
    override suspend fun getVitalSignsByDateRange(startDate: Date, endDate: Date): List<VitalSign> {
        // Using first() to get the first emission from the flow (suspending function)
        return vitalSignDao.getVitalSignsByDateRange(startDate, endDate)
            .map { entities -> entities.map { it.toVitalSign() } }
            .first()
    }
    
    override suspend fun getVitalSignsByScenarioAndMannequin(scenario: String, mannequin: String): List<VitalSign> {
        // Using first() to get the first emission from the flow (suspending function)
        return vitalSignDao.getVitalSignsByMannequinAndScenario(mannequin, scenario)
            .map { entities -> entities.map { it.toVitalSign() } }
            .first()
    }
    
    /**
     * Get vital signs for a specific simulation
     */
    fun getVitalSignsBySimulation(simulation: String): Flow<List<VitalSign>> {
        return vitalSignDao.getVitalSignsBySimulation(simulation).map { entities ->
            entities.map { it.toVitalSign() }
        }
    }
    
    /**
     * Get count of stored vital signs
     */
    suspend fun getCount(): Int {
        return vitalSignDao.getCount()
    }
    
    /**
     * Get all distinct mannequins in the database
     */
    suspend fun getDistinctMannequins(): List<String> {
        return vitalSignDao.getDistinctMannequins()
    }
    
    /**
     * Get all distinct scenarios in the database
     */
    suspend fun getDistinctScenarios(): List<String> {
        return vitalSignDao.getDistinctScenarios()
    }
} 