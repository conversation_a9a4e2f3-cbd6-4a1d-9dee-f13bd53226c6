package com.example.myapplication.data.model

import com.example.myapplication.data.Constants
import kotlin.collections.HashMap

/**
 * Configuration for mannequins and simulations
 * This class provides utility methods for working with mannequin and simulation data,
 * but references the canonical definitions from Constants.kt to avoid redundancy
 */
object MannequinConfig {
    // Default IP addresses for each mannequin's Zoll monitor
    val DEFAULT_MANNEQUIN_IPS = mapOf(
        "<PERSON>" to "*************",
        "<PERSON>" to "*************",
        "<PERSON>" to "*************",
        "<PERSON>" to "*************", 
        "<PERSON>" to "*************"
    )
    
    /**
     * Information about a mannequin
     */
    data class MannequinInfo(
        val name: String,
        val defaultIpAddress: String,
        val scenario: String
    )

    data class TimeInfo(
        val hours: Int,
        val minutes: Int
    )

    // Convert string-based time schedules to TimeInfo objects
    private val SIM_SCHEDULES = Constants.SIM_SCHEDULES.mapValues { (_, timeRanges) ->
        timeRanges.map { (start, end) ->
            val startParts = start.split(":").map { it.toInt() }
            val endParts = end.split(":").map { it.toInt() }
            Pair(
                TimeInfo(startParts[0], startParts[1]), 
                TimeInfo(endParts[0], endParts[1])
            )
        }
    }
    
    /**
     * Get all available simulation names
     */
    fun getAvailableSimulations(): List<String> {
        return Constants.SIM_MANNEQUINS.keys.toList().sorted()
    }
    
    /**
     * Get mannequins for a specific scenario/simulation
     */
    fun getMannequinsForSimulation(simulation: String): List<String> {
        return Constants.SIM_MANNEQUINS[simulation] ?: listOf("Dave") // Default to Dave if not found
    }
    
    /**
     * Get the scenario for a specific mannequin in a simulation
     */
    fun getScenarioForMannequin(simulation: String, mannequin: String): String {
        return Constants.SIM_MANNEQUIN_SCENARIO[simulation]?.get(mannequin) ?: "TBI" // Default to TBI
    }
    
    /**
     * Get mannequin configurations for a specific simulation
     * @param simulation The simulation name
     * @return List of mannequin info objects
     */
    fun getMannequinConfigsForSimulation(simulation: String): List<MannequinInfo> {
        return when (simulation) {
            "Sim1" -> listOf(
                MannequinInfo("Dave", "*************", "TBI"),
                MannequinInfo("Chuck", "*************", "Pneumonia")
            )
            "Sim2" -> listOf(
                MannequinInfo("Freddy", "*************", "Unmonitored Severe TBI"),
                MannequinInfo("Oscar", "*************", "Polytrauma (DCR)")
            )
            "Sim3" -> listOf(
                MannequinInfo("Dave", "*************", "Sepsis"),
                MannequinInfo("Chuck", "*************", "ACS/STEMI")
            )
            "Sim4" -> listOf(
                MannequinInfo("Dave", "*************", "Catastrophic TBI"),
                MannequinInfo("Chuck", "*************", "Aortic Dissection")
            )
            "Sim5" -> listOf(
                MannequinInfo("Freddy", "*************", "Burn"),
                MannequinInfo("Oscar", "*************", "Atrial Fibrillation"),
                MannequinInfo("Matt", "*************", "Penetrating Thoracoabdominal Injury (DCR)")
            )
            "TrainingSim3" -> listOf(
                MannequinInfo("Oscar", "*************", "DCR"),
                MannequinInfo("Freddy", "*************", "DCR")
            )
            "TrainingSim5" -> listOf(
                MannequinInfo("Dave", "*************", "DCR"),
                MannequinInfo("Chuck", "*************", "Fever/Seizure/Intubation")
            )
            else -> emptyList()
        }
    }

    /**
     * Get the simulation schedule (time blocks) for a specific simulation
     */
    fun getSimulationSchedule(simulation: String): List<Pair<TimeInfo, TimeInfo>>? {
        return SIM_SCHEDULES[simulation]
    }

    /**
     * Get information for a specific mannequin
     * @param mannequinName The name of the mannequin
     * @return MannequinInfo object or null if not found
     */
    fun getMannequinInfo(mannequinName: String): MannequinInfo? {
        return when (mannequinName) {
            "Dave" -> MannequinInfo("Dave", "*************", "TBI") // Can also be DCR in TrainingSim5
            "Chuck" -> MannequinInfo("Chuck", "*************", "Pneumonia") // Can also be Fever/Seizure/Intubation in TrainingSim5
            "Freddy" -> MannequinInfo("Freddy", "*************", "Unmonitored Severe TBI") // Can also be DCR in TrainingSim3
            "Oscar" -> MannequinInfo("Oscar", "*************", "Polytrauma (DCR)") // Can also be DCR in TrainingSim3
            "Matt" -> MannequinInfo("Matt", "*************", "Penetrating Thoracoabdominal Injury (DCR)")
            else -> null
        }
    }
} 