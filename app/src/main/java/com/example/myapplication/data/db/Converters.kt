package com.example.myapplication.data.db

import androidx.room.TypeConverter
import java.util.Date

/**
 * Type converters for Room database to handle non-primitive types
 */
class Converters {
    @TypeConverter
    fun fromTimestamp(value: Long?): Date? {
        return value?.let { Date(it) }
    }
    
    @TypeConverter
    fun dateToTimestamp(date: Date?): Long? {
        return date?.time
    }
} 