package com.example.myapplication.data.model

import androidx.compose.ui.graphics.Color

/**
 * Enum for vital sign status
 */
enum class VitalSignStatus {
    NORMAL,
    WARNING_HIGH,
    WARNING_LOW,
    CRITICAL_HIGH,
    CRITICAL_LOW,
    UNKNOWN;
    
    /**
     * Get color for this status
     */
    fun getColor(): Color {
        return when (this) {
            NORMAL -> Color.Green
            WARNING_HIGH, WARNING_LOW -> Color.Yellow
            CRITICAL_HIGH, CRITICAL_LOW -> Color.Red
            UNKNOWN -> Color.Gray
        }
    }
    
    /**
     * Get label for this status
     */
    fun getLabel(): String {
        return when (this) {
            NORMAL -> "Normal"
            WARNING_HIGH -> "High"
            WARNING_LOW -> "Low"
            CRITICAL_HIGH -> "Critical High"
            CRITICAL_LOW -> "Critical Low"
            UNKNOWN -> "Unknown"
        }
    }
} 