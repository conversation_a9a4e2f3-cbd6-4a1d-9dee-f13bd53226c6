package com.example.myapplication.data.util

import com.example.myapplication.data.model.MannequinConfig
import com.example.myapplication.data.model.VitalSign
import com.example.myapplication.data.SimulationDurations
import com.example.myapplication.data.Constants
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.concurrent.TimeUnit
import android.util.Log // Add Log import if not already present
import java.time.Instant
import java.time.ZoneId
import java.time.temporal.ChronoUnit

/**
 * Utility for detecting the precise start and end times of simulations
 * by analyzing both schedule-based expectations and actual monitor data.
 */
object SimulationTimeDetector {
    
    // Setup buffer time in minutes
    private const val SETUP_BUFFER_MINUTES = 10
    
    // Thresholds for detecting simulation boundaries           
    private const val MIN_SUSTAINED_VALID_DATA_SECONDS = 60 // 1 minute of consistent data to confirm start
    private const val MIN_SUSTAINED_INACTIVE_MINUTES = 3 // 3 minutes of inactive data to confirm end
    
    /**
     * Combined method to identify which simulation is being run and its timeframe
     * Uses schedule-based detection first, then falls back to data pattern analysis
     * 
     * @param vitalSigns List of vital signs
     * @param date The date of the simulation
     * @param annotationEvents Optional list of annotation events 
     * @return Pair of (simulation name, timeframe pair) or null if detection failed
     */
    fun identifySimulationAndTimeframe(
        vitalSigns: List<VitalSign>,
        date: Date,
        annotationEvents: List<Map<String, Any>>? = null
    ): Pair<String, Pair<Date, Date>>? {
        Log.e("SimTimeDetector_Debug", "Entering identifySimulationAndTimeframe") // ENTRY LOG
        if (vitalSigns.isEmpty()) {
            Log.w("SimTimeDetector_Debug", "Exiting identifySimulationAndTimeframe - vitalSigns list is empty") // EXIT LOG
            return null
        }
        
        // Sort vital signs by time
        val sortedVitals = vitalSigns.sortedBy { it.timeObj }
        
        // Try to identify from schedule first
        val simFromSchedule = identifySimulationFromSchedule(date)
        Log.i("SimTimeDetector_Debug", "IdentifyFromSchedule result: ${simFromSchedule ?: "null"}") // SCHEDULE LOG 1
        
        if (simFromSchedule != null) {
            // We have a schedule-based identification, verify with timeframe
            Log.i("SimTimeDetector_Debug", "Attempting DetectSimulationTimeframe for schedule result: $simFromSchedule") // SCHEDULE LOG 2
            val timeframe = detectSimulationTimeframe(sortedVitals, simFromSchedule, date, annotationEvents)
            Log.i("SimTimeDetector_Debug", "DetectSimulationTimeframe (schedule) result: ${timeframe?.let { formatTime(it.first) + " - " + formatTime(it.second) } ?: "null"}") // SCHEDULE LOG 3
            
            if (timeframe != null) {
                // *** REMOVED DURATION CHECK ***
                // The duration check is invalid when only partial data from a multi-mannequin sim is loaded.
                // We now trust the schedule-based Sim identification and the timeframe detected from available data.
                Log.i("SimTimeDetector_Debug", "Schedule-based detection SUCCEEDED for $simFromSchedule with timeframe ${formatTime(timeframe.first)} - ${formatTime(timeframe.second)}. Skipping duration check for potentially partial data.") // SUCCESS LOG (Duration check removed)
                return Pair(simFromSchedule, timeframe)
                // *** END REMOVAL ***
            }
        }
        
        // Schedule-based identification failed or produced an invalid timeframe
        Log.w("SimTimeDetector_Debug", "Schedule-based detection failed or timeframe invalid. Falling back to data pattern analysis.") // DATA LOG 1
        // Fall back to data pattern analysis
        val simFromData = identifySimulationFromData(sortedVitals, annotationEvents)
        Log.i("SimTimeDetector_Debug", "IdentifySimulationFromData result: ${simFromData ?: "null"}") // DATA LOG 2
        
        if (simFromData != null) {
            // We identified the simulation from data patterns
            // Now get the timeframe
            Log.i("SimTimeDetector_Debug", "Attempting DetectTimeframeFromDataOnly for data result: $simFromData") // DATA LOG 3
            val timeframe = detectTimeframeFromDataOnly(sortedVitals, annotationEvents)
            Log.i("SimTimeDetector_Debug", "DetectTimeframeFromDataOnly result: ${timeframe?.let { formatTime(it.first) + " - " + formatTime(it.second) } ?: "null"}") // DATA LOG 4
            
            if (timeframe != null) {
                Log.i("SimTimeDetector_Debug", "Data-based detection SUCCEEDED for $simFromData") // SUCCESS LOG
                return Pair(simFromData, timeframe)
            }
        }
        
        Log.e("SimTimeDetector_Debug", "Exiting identifySimulationAndTimeframe - BOTH schedule and data detection failed.") // FAILURE LOG
        return null
    }
    
    /**
     * Identifies which simulation should be running based on date and time
     * using the simulation schedule
     * 
     * @param date The date and time to check
     * @return The simulation name (Sim1, Sim2, etc.) or null if no match found
     */
    fun identifySimulationFromSchedule(date: Date): String? {
        Log.i("SimDetectSchedule_Debug", "Entering identifySimulationFromSchedule with date: $date") // ENTRY LOG
        // Extract course info from date
        val calendar = Calendar.getInstance().apply { time = date }
        val month = calendar.get(Calendar.MONTH) + 1 // Calendar months are 0-based
        val day = calendar.get(Calendar.DAY_OF_MONTH)
        val dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK)
        
        // --- START REORDER --- 
        Log.i("SimDetectSchedule_Debug", "STEP 1: Checking course dates FIRST.") // COURSE DATE CHECK
        // Find which course this date belongs to
        val dateStr = String.format("%02d/%02d", month, day)
        val courseInfo = Constants.COURSE_DATE_RANGES.find { (startDate, endDate, _) ->
            isDateInRange(dateStr, startDate, endDate)
        }
        
        if (courseInfo != null) {
            Log.i("SimDetectSchedule_Debug", "Found matching course: ${courseInfo.third} for date $dateStr") // COURSE FOUND LOG
            // Find the day offset within the course
            val (startDate, _, courseLabel) = courseInfo
            val courseStartDate = parseCourseDate(startDate)
            
            if (courseStartDate != null) {
                val courseStartCalendar = Calendar.getInstance().apply { 
                    time = courseStartDate
                    set(Calendar.YEAR, calendar.get(Calendar.YEAR))
                    // If the current date month is earlier than the course start month,
                    // it's likely in the next year (December -> January transition)
                    if (month < getMonthFromString(startDate) && month <= 3) {
                        add(Calendar.YEAR, 1)
                    }
                }
                
                // Calculate days since course start using LocalDate for robustness
                val courseStartDateOnly = Instant.ofEpochMilli(courseStartCalendar.timeInMillis)
                    .atZone(ZoneId.systemDefault()) // Use system timezone or specify one like ZoneId.of("America/New_York")
                    .toLocalDate()
                val currentDateOnly = Instant.ofEpochMilli(calendar.timeInMillis)
                    .atZone(ZoneId.systemDefault()) // Use system timezone or specify one
                    .toLocalDate()

                val dayOffset = ChronoUnit.DAYS.between(courseStartDateOnly, currentDateOnly).toInt()
                
                // Map day offset to simulation
                val simFromOffset = Constants.DAY_OFFSET_TO_SIM[dayOffset]
                if (simFromOffset != null) {
                    Log.i("SimDetectSchedule_Debug", "SUCCESS: Identified $simFromOffset based on course date offset ($dayOffset days)") // COURSE SUCCESS LOG
                    return simFromOffset
                } else {
                     Log.w("SimDetectSchedule_Debug", "Course found, but day offset $dayOffset did not map to a sim.") // COURSE OFFSET FAIL
                }
            } else {
                 Log.w("SimDetectSchedule_Debug", "Course found, but failed to parse start date: $startDate") // COURSE PARSE FAIL
            }
        } else {
             Log.i("SimDetectSchedule_Debug", "No matching course found for date: $dateStr.") // COURSE FAIL LOG
        }

        // --- Fallback to Training Sim Check --- 
        Log.i("SimDetectSchedule_Debug", "STEP 2: Checking Training Sim schedule as fallback.") // TRAINING CHECK
        val hourOfDay = calendar.get(Calendar.HOUR_OF_DAY)
        val minute = calendar.get(Calendar.MINUTE)
        val timeInMinutes = hourOfDay * 60 + minute
        
        Log.i("SimDetectSchedule_Debug", "Time Check: dayOfWeek=$dayOfWeek, hourOfDay=$hourOfDay, minute=$minute, timeInMinutes=$timeInMinutes") // TIME CHECK LOG

        // Check if time falls within training simulation hours (typically 9:00-16:00)
        val isTrainingHours = timeInMinutes in 540..960 // 9:00 (540 mins) to 16:00 (960 mins)
        Log.i("SimDetectSchedule_Debug", "Is within general training hours (9-16): $isTrainingHours") // TRAINING HOURS LOG
        
        if (isTrainingHours) {
            if (dayOfWeek == Calendar.WEDNESDAY) {
                Log.i("SimDetectSchedule_Debug", "Day is Wednesday. Checking TrainingSim3 blocks.") // TRAINING SIM 3 CHECK
                // Check if time falls within one of the TrainingSim3 blocks
                for (timeBlock in Constants.SIM_SCHEDULES["TrainingSim3"] ?: emptyList()) {
                    val startTime = timeBlock.first.split(":")
                    val endTime = timeBlock.second.split(":")
                    
                    if (startTime.size == 2 && endTime.size == 2) {
                        val startMinutes = startTime[0].toInt() * 60 + startTime[1].toInt()
                        val endMinutes = endTime[0].toInt() * 60 + endTime[1].toInt()
                        
                        if (timeInMinutes in startMinutes..endMinutes) {
                            Log.i("SimDetectSchedule_Debug", "MATCHED TrainingSim3 block: ${timeBlock.first}-${timeBlock.second} ($startMinutes-$endMinutes mins)") // TRAINING MATCH LOG
                            return "TrainingSim3"
                        }
                    }
                }
            } else if (dayOfWeek == Calendar.FRIDAY) {
                Log.i("SimDetectSchedule_Debug", "Day is Friday. Checking TrainingSim5 blocks.") // TRAINING SIM 5 CHECK
                // Check if time falls within one of the TrainingSim5 blocks
                for (timeBlock in Constants.SIM_SCHEDULES["TrainingSim5"] ?: emptyList()) {
                    val startTime = timeBlock.first.split(":")
                    val endTime = timeBlock.second.split(":")
                    
                    if (startTime.size == 2 && endTime.size == 2) {
                        val startMinutes = startTime[0].toInt() * 60 + startTime[1].toInt()
                        val endMinutes = endTime[0].toInt() * 60 + endTime[1].toInt()
                        
                        if (timeInMinutes in startMinutes..endMinutes) {
                            Log.i("SimDetectSchedule_Debug", "MATCHED TrainingSim5 block: ${timeBlock.first}-${timeBlock.second} ($startMinutes-$endMinutes mins)") // TRAINING MATCH LOG
                            return "TrainingSim5"
                        }
                    }
                }
            }
        }
        
        // --- Final Fallback to General Time Schedule Check --- 
        Log.w("SimDetectSchedule_Debug", "STEP 3: No course or training sim match. Checking general time schedules.") // GENERAL CHECK FALLBACK LOG
        // Fallback check if course lookup fails - Check regular Sim schedules based on time
        return checkGeneralSimSchedules(timeInMinutes)
    }
    
    /**
     * Helper function to check general simulation schedules based on time of day.
     */
    private fun checkGeneralSimSchedules(timeInMinutes: Int): String? {
        Log.i("SimDetectSchedule_Debug", "Entering checkGeneralSimSchedules for timeInMinutes: $timeInMinutes") // GENERAL CHECK LOG
        // Now check if we can determine the simulation from the time of day
        // Try each simulation's schedule to see if the current time falls within it
        for ((simName, schedule) in Constants.SIM_SCHEDULES) {
            // Skip training sims as we already checked them
            if (simName.startsWith("Training")) continue
            
            for (timeBlock in schedule) {
                val startTime = timeBlock.first.split(":")
                val endTime = timeBlock.second.split(":")
                
                if (startTime.size == 2 && endTime.size == 2) {
                    val startMinutes = startTime[0].toInt() * 60 + startTime[1].toInt()
                    val endMinutes = endTime[0].toInt() * 60 + endTime[1].toInt()
                    
                    if (timeInMinutes in startMinutes..endMinutes) {
                        Log.i("SimDetectSchedule_Debug", "MATCHED general schedule: $simName block ${timeBlock.first}-${timeBlock.second} ($startMinutes-$endMinutes mins)") // GENERAL MATCH LOG
                        return simName
                    }
                }
            }
        }
        Log.w("SimDetectSchedule_Debug", "Exiting checkGeneralSimSchedules - No match found.") // GENERAL FAIL LOG
        return null
    }
    
    /**
     * Helper function to check if a date is within a range
     */
    private fun isDateInRange(dateStr: String, startDate: String, endDate: String): Boolean {
        val month = dateStr.split("/")[0].toInt()
        val day = dateStr.split("/")[1].toInt()
        
        val startMonth = startDate.split("/")[0].toInt()
        val startDay = startDate.split("/")[1].toInt()
        
        val endMonth = endDate.split("/")[0].toInt()
        val endDay = endDate.split("/")[1].toInt()
        
        // Handle the case where the range spans the end of the year
        return if (startMonth > endMonth) {
            // Range crosses year boundary (e.g., Dec-Jan)
            (month > startMonth || (month == startMonth && day >= startDay)) || 
            (month < endMonth || (month == endMonth && day <= endDay))
        } else if (startMonth == endMonth) {
            // Same month
            month == startMonth && day >= startDay && day <= endDay
        } else {
            // Normal case
            (month > startMonth || (month == startMonth && day >= startDay)) && 
            (month < endMonth || (month == endMonth && day <= endDay))
        }
    }
    
    /**
     * Helper function to parse course date string (MM/DD) into a Date
     */
    private fun parseCourseDate(dateStr: String): Date? {
        val parts = dateStr.split("/")
        if (parts.size != 2) return null
        
        val month = parts[0].toInt() - 1 // Calendar months are 0-based
        val day = parts[1].toInt()
        
        // Default to current year
        val calendar = Calendar.getInstance()
        calendar.set(Calendar.MONTH, month)
        calendar.set(Calendar.DAY_OF_MONTH, day)
        
        return calendar.time
    }
    
    /**
     * Helper function to extract month from MM/DD string
     */
    private fun getMonthFromString(dateStr: String): Int {
        val parts = dateStr.split("/")
        return if (parts.size == 2) parts[0].toInt() else 1
    }
    
    /**
     * Detects the exact simulation timeframe from a list of vital signs
     * 
     * @param vitalSigns List of vital signs sorted by time
     * @param simName Simulation name (Sim1, Sim2, etc.)
     * @param date The date of the simulation (for schedule lookup)
     * @param annotationEvents Optional list of annotation events to help detect power-off moments
     * @return Pair of start and end times, or null if detection failed
     */
    fun detectSimulationTimeframe(
        vitalSigns: List<VitalSign>,
        simName: String,
        date: Date,
        annotationEvents: List<Map<String, Any>>? = null
    ): Pair<Date, Date>? {
        Log.i("SimTimeframe_Debug", "Entering detectSimulationTimeframe for Sim: $simName, Date: $date") // ENTRY LOG
        if (vitalSigns.isEmpty() || !SimulationDurations.SIM_DURATIONS.containsKey(simName)) {
            Log.w("SimTimeframe_Debug", "Exiting - vitalSigns empty or simName '$simName' not in durations map.") // EARLY EXIT LOG
            return null
        }
        
        // Get scheduled window
        val scheduledWindow = getScheduledSimulationWindow(simName, date)
        Log.i("SimTimeframe_Debug", "Scheduled window for $simName: ${scheduledWindow?.let { formatTime(it.first) + " - " + formatTime(it.second) } ?: "null"}") // SCHEDULE WINDOW LOG
        if (scheduledWindow == null) {
            // If we can't determine scheduled window, use broader timeframe based on the data
            Log.w("SimTimeframe_Debug", "Scheduled window is null. Falling back to detectTimeframeFromDataOnly.") // DATA ONLY FALLBACK LOG
            return detectTimeframeFromDataOnly(vitalSigns, annotationEvents)
        }
        
        val (scheduledStart, scheduledEnd) = scheduledWindow
        
        // Filter vital signs to around the scheduled window (add buffer before and after)
        val startBuffer = Calendar.getInstance().apply { 
            time = scheduledStart
            add(Calendar.MINUTE, -5) // 5 minutes before scheduled start
        }.time
        
        val endBuffer = Calendar.getInstance().apply { 
            time = scheduledEnd
            add(Calendar.MINUTE, 15) // 15 minutes after scheduled end
        }.time
        
        val relevantVitals = vitalSigns.filter { 
            it.timeObj in startBuffer..endBuffer 
        }
        Log.i("SimTimeframe_Debug", "Found ${relevantVitals.size} relevant vital signs within buffer window.") // RELEVANT VITALS LOG
        
        if (relevantVitals.isEmpty()) {
            // Fall back to scheduled times if no relevant data
            Log.w("SimTimeframe_Debug", "No relevant vitals in buffer window. Returning scheduled window.") // NO RELEVANT DATA LOG
            return scheduledWindow
        }
        
        // Detect actual start time (with annotation events)
        Log.i("SimTimeframe_Debug", "Calling detectStartTime with scheduledStart: ${formatTime(scheduledStart)}") // DETECT START CALL LOG
        val actualStart = detectStartTime(relevantVitals, scheduledStart, annotationEvents)
        Log.i("SimTimeframe_Debug", "detectStartTime result: ${formatTime(actualStart)}") // DETECT START RESULT LOG
        
        // Detect actual end time (with annotation events)
        Log.i("SimTimeframe_Debug", "Calling detectEndTime with actualStart: ${formatTime(actualStart)}, scheduledEnd: ${formatTime(scheduledEnd)}") // DETECT END CALL LOG
        val actualEnd = detectEndTime(relevantVitals, actualStart, scheduledEnd, annotationEvents)
        Log.i("SimTimeframe_Debug", "detectEndTime result: ${formatTime(actualEnd)}") // DETECT END RESULT LOG
        
        // Check if actualEnd is before actualStart
        if (actualEnd.before(actualStart)) {
            Log.e("SimTimeframe_Debug", "ERROR: Detected end time (${formatTime(actualEnd)}) is BEFORE detected start time (${formatTime(actualStart)}). Returning null.") // TIME ORDER ERROR LOG
            return null
        }
        
        Log.i("SimTimeframe_Debug", "Exiting detectSimulationTimeframe successfully with: ${formatTime(actualStart)} - ${formatTime(actualEnd)}") // SUCCESS LOG
        return Pair(actualStart, actualEnd)
    }
    
    /**
     * Identifies which simulation is being run based on mannequin combinations and vital sign patterns
     * when the date doesn't fall within a scheduled window.
     *
     * @param vitalSigns List of vital signs sorted by time
     * @param annotationEvents List of annotation events (optional)
     * @return The identified simulation name (Sim1, Sim2, etc.) or null if identification failed
     */
    fun identifySimulationFromData(
        vitalSigns: List<VitalSign>,
        annotationEvents: List<Map<String, Any>>? = null
    ): String? {
        if (vitalSigns.isEmpty()) {
            return null
        }
        
        // Sort vital signs by time
        val sortedVitals = vitalSigns.sortedBy { it.timeObj }
        
        // Get the timeframe of the simulation
        val simulationTimeframe = detectTimeframeFromDataOnly(sortedVitals, annotationEvents) ?: return null
        val (startTime, endTime) = simulationTimeframe
        
        // Filter to vital signs within the detected timeframe
        val relevantVitals = sortedVitals.filter { 
            it.timeObj in startTime..endTime 
        }
        
        // Check simulation duration
        val durationMinutes = TimeUnit.MILLISECONDS.toMinutes(endTime.time - startTime.time)
        
        // Step 1: Check mannequin combinations (unique device serials)
        val uniqueSerials = relevantVitals
            .mapNotNull { it.deviceSerial }
            .toSet()
            
        val mannequins = uniqueSerials
            .mapNotNull { Constants.MANNEQUIN_MAP[it] }
            .toSet()
            
        // Filter based on mannequin combinations
        if (mannequins.size == 3 && 
            mannequins.containsAll(setOf("Freddy", "Oscar", "Matt"))) {
            // If duration matches Sim5 expectations
            if (durationMinutes in 38..42) {
                return "Sim5"
            }
        } else if (mannequins.size == 2 && 
            mannequins.containsAll(setOf("Freddy", "Oscar")) && 
            !mannequins.contains("Dave") && 
            !mannequins.contains("Chuck") && 
            !mannequins.contains("Matt")) {
            
            // Check if it's TrainingSim3 (two DCR patients - Oscar and Freddy)
            // Look for DCR-specific patterns for both mannequins
            if (isDCRSimulation(relevantVitals) && durationMinutes in 43..47) {
                return "TrainingSim3"
            }
            
            // Otherwise, assume it's regular Sim2
            if (durationMinutes in 33..37) {
                return "Sim2"
            }
        } else if (mannequins.size == 2 && 
            mannequins.containsAll(setOf("Dave", "Chuck")) && 
            !mannequins.contains("Freddy") && 
            !mannequins.contains("Oscar") && 
            !mannequins.contains("Matt")) {
            
            // Check for TrainingSim5 (Dave DCR and Chuck fever/seizure/intubation)
            if (hasDaveDCRAndChuckFeverPattern(relevantVitals) && durationMinutes in 43..47) {
                return "TrainingSim5"
            }
            
            // Need to identify among Sim1, Sim3, or Sim4 using vital sign patterns
            return identifyDaveChuckSimulation(relevantVitals, durationMinutes)
        }
        
        // Step 3: If we have only one serial or still ambiguous, use vital sign patterns
        return identifySimulationFromVitalPatterns(relevantVitals, durationMinutes, annotationEvents)
    }
    
    /**
     * Checks if a simulation shows patterns consistent with DCR scenarios
     * for both mannequins (used for Training Sim 3)
     */
    private fun isDCRSimulation(vitalSigns: List<VitalSign>): Boolean {
        if (vitalSigns.isEmpty()) return false
        
        // Group vital signs by mannequin
        val mannequinVitals = vitalSigns
            .filter { it.deviceSerial != null }
            .groupBy { Constants.MANNEQUIN_MAP[it.deviceSerial] }
        
        // We need data for both Oscar and Freddy
        if (!mannequinVitals.containsKey("Oscar") || !mannequinVitals.containsKey("Freddy")) {
            return false
        }
        
        // Check for DCR indicators in both mannequins:
        // 1. Temperature < 96°F at some point (hypothermia)
        // 2. Early hypotension (MAP < 60)
        val mannequinsWithDCRIndicators = mutableSetOf<String>()
        
        for ((mannequin, vitals) in mannequinVitals) {
            if (mannequin == null) continue
            
            val hasHypothermia = vitals.any { 
                it.temp1 != null && it.temp1!! < 96.0 && it.temp1Status == "valid" 
            }
            
            val hasEarlyHypotension = vitals.take(vitals.size / 3).any {
                it.nibpMap != null && it.nibpMap!! < 60 && it.nibpMapStatus == "valid"
            }
            
            if (hasHypothermia || hasEarlyHypotension) {
                mannequinsWithDCRIndicators.add(mannequin)
            }
        }
        
        // Return true if both Oscar and Freddy show DCR indicators
        return mannequinsWithDCRIndicators.containsAll(setOf("Oscar", "Freddy"))
    }
    
    /**
     * Checks if a simulation shows patterns consistent with 
     * Dave having DCR and Chuck having fever/seizure/intubation (Training Sim 5)
     */
    private fun hasDaveDCRAndChuckFeverPattern(vitalSigns: List<VitalSign>): Boolean {
        if (vitalSigns.isEmpty()) return false
        
        // Group vital signs by mannequin
        val mannequinVitals = vitalSigns
            .filter { it.deviceSerial != null }
            .groupBy { Constants.MANNEQUIN_MAP[it.deviceSerial] }
        
        // We need data for both Dave and Chuck
        if (!mannequinVitals.containsKey("Dave") || !mannequinVitals.containsKey("Chuck")) {
            return false
        }
        
        // Check Dave for DCR indicators:
        // 1. Temperature < 96°F at some point (hypothermia)
        // 2. Early hypotension (MAP < 60)
        val daveVitals = mannequinVitals["Dave"] ?: return false
        val daveDCRIndicators = daveVitals.any { 
            (it.temp1 != null && it.temp1!! < 96.0 && it.temp1Status == "valid") ||
            (it.nibpMap != null && it.nibpMap!! < 60 && it.nibpMapStatus == "valid")
        }
        
        // Check Chuck for fever/seizure indicators:
        // 1. Temperature > 101°F at some point (fever)
        // 2. Periods of tachycardia (HR > 110)
        // 3. Irregular respiratory patterns
        val chuckVitals = mannequinVitals["Chuck"] ?: return false
        val chuckHasFever = chuckVitals.any {
            it.temp1 != null && it.temp1!! > 101.0 && it.temp1Status == "valid"
        }
        
        val chuckHasTachycardia = chuckVitals.any {
            it.hr != null && it.hr!! > 110 && it.hrStatus == "valid"
        }
        
        // Irregular respiratory patterns - look for significant variation
        val respiratoryRates = chuckVitals
            .filter { it.respRate != null && it.respRateStatus == "valid" }
            .map { it.respRate!! }
        
        val hasRespiratoryVariability = if (respiratoryRates.size >= 5) {
            val minRate = respiratoryRates.minOrNull() ?: 0.0
            val maxRate = respiratoryRates.maxOrNull() ?: 0.0
            maxRate - minRate >= 8.0 // Significant variation in respiratory rate
        } else {
            false
        }
        
        // Return true if both mannequins show their specific pattern indicators
        return daveDCRIndicators && (chuckHasFever || chuckHasTachycardia || hasRespiratoryVariability)
    }
    
    /**
     * Identifies which simulation among Sim1, Sim3, and Sim4 is being run
     * based on vital sign patterns for Dave and Chuck mannequins
     */
    private fun identifyDaveChuckSimulation(
        vitalSigns: List<VitalSign>,
        durationMinutes: Long
    ): String {
        // Check for TrainingSim5 (Dave DCR and Chuck fever/seizure/intubation)
        if (hasDaveDCRAndChuckFeverPattern(vitalSigns)) {
            // More flexible duration check for training simulations (30-60 minutes)
            if (durationMinutes in 30..60) {
                return "TrainingSim5"
            }
        }
        
        // Look for HR flat-line followed by ROSC pattern (Sim3 - Chuck's V-tach and shock)
        if (hasHRFlatLineROSCPattern(vitalSigns)) {
            // Confirm with duration check
            return if (durationMinutes in 48..52) "Sim3" else "Sim1"
        }
        
        // Look for long hypertension plateau + spike/drop pattern (Sim4)
        if (hasHypertensionPatternForSim4(vitalSigns)) {
            // Confirm with duration check
            return if (durationMinutes in 53..57) "Sim4" else "Sim1"
        }
        
        // Check for late hypotension (MAP below 55 mmHg in last 10 minutes) typical of Sim1
        if (hasLateHypotensionForSim1(vitalSigns)) {
            // Confirm with duration check
            return if (durationMinutes in 43..47) "Sim1" else "Sim3"
        }
        
        // Default to Sim1 if no specific patterns detected
        return if (durationMinutes in 43..47) {
            "Sim1"
        } else if (durationMinutes in 48..52) {
            "Sim3"
        } else if (durationMinutes in 53..57) {
            "Sim4"
        } else {
            "Sim1" // Default fallback
        }
    }
    
    /**
     * Identifies simulation from vital sign patterns when mannequin combinations
     * are insufficient to determine the simulation
     */
    private fun identifySimulationFromVitalPatterns(
        vitalSigns: List<VitalSign>,
        durationMinutes: Long,
        annotationEvents: List<Map<String, Any>>? = null
    ): String? {
        // Check for training simulations first
        
        // Check for Training Sim 3 patterns (two DCR patients)
        if (isDCRSimulation(vitalSigns)) {
            // More flexible duration check for training simulations (30-60 minutes)
            if (durationMinutes in 30..60) {
            return "TrainingSim3"
            }
        }
        
        // Check for Training Sim 5 patterns (Dave DCR and Chuck fever/seizure)
        if (hasDaveDCRAndChuckFeverPattern(vitalSigns)) {
            // More flexible duration check for training simulations (30-60 minutes)
            if (durationMinutes in 30..60) {
            return "TrainingSim5"
            }
        }
        
        // Check for empty-room lead-in (first ~7 minutes unmonitored) - Sim2
        if (hasEmptyRoomLeadIn(vitalSigns)) {
            return if (durationMinutes in 33..37) "Sim2" else "Sim1"
        }
        
        // Check for whole-run tachycardia + SpO2 probe events - Sim5
        val hasRunLongTachycardia = hasWholeRunTachycardia(vitalSigns)
        val hasMultipleProbeEvents = hasMultipleSpO2ProbeEvents(annotationEvents)
        
        if (hasRunLongTachycardia && (hasMultipleProbeEvents || durationMinutes in 38..42)) {
            return "Sim5"
        }
        
        // Check for HR flat-line + ROSC pattern - Sim3
        if (hasHRFlatLineROSCPattern(vitalSigns)) {
            return if (durationMinutes in 48..52) "Sim3" else "Sim1"
        }
        
        // Check for long hypertension plateau + spike/drop pattern - Sim4
        if (hasHypertensionPatternForSim4(vitalSigns)) {
            return if (durationMinutes in 53..57) "Sim4" else "Sim1"
        }
        
        // Check for early hypotension that improves then relapses - Sim2
        if (hasEarlyHypotensionRelapse(vitalSigns)) {
            return if (durationMinutes in 33..37) "Sim2" else "Sim1"
        }
        
        // Default to Sim1 if no other patterns match
        return if (durationMinutes in 43..47) {
            "Sim1"
        } else if (durationMinutes in 33..37) {
            "Sim2"
        } else if (durationMinutes in 48..52) {
            "Sim3"
        } else if (durationMinutes in 53..57) {
            "Sim4"
        } else if (durationMinutes in 38..42) {
            "Sim5"
        } else {
            "Sim1" // Default fallback
        }
    }
    
    /**
     * Detects whether the first ~7 minutes have every channel marked "unmonitored"
     * followed by valid data (matches Sim2 - prep before mannequins rolled in)
     */
    private fun hasEmptyRoomLeadIn(vitalSigns: List<VitalSign>): Boolean {
        if (vitalSigns.isEmpty()) return false
        
        val startTime = vitalSigns.first().timeObj
        val sevenMinuteMark = Calendar.getInstance().apply { 
            time = startTime
            add(Calendar.MINUTE, 7)
        }.time
        
        // Get vital signs in first 7 minutes
        val earlyVitals = vitalSigns.filter { 
            it.timeObj <= sevenMinuteMark
        }
        
        if (earlyVitals.isEmpty()) return false
        
        // Calculate percentage of early readings that have all channels unmonitored
        val allUnmonitoredCount = earlyVitals.count { vital ->
            (vital.hrStatus == "unmonitored" || vital.hrStatus == null) &&
            (vital.spO2Status == "unmonitored" || vital.spO2Status == null) &&
            (vital.nibpSysStatus == "unmonitored" || vital.nibpSysStatus == null) &&
            (vital.nibpMapStatus == "unmonitored" || vital.nibpMapStatus == null) &&
            (vital.respRateStatus == "unmonitored" || vital.respRateStatus == null) &&
            (vital.etCO2Status == "unmonitored" || vital.etCO2Status == null)
        }
        
        // Check if at least 70% of early readings have all channels unmonitored
        val unmonitoredRatio = allUnmonitoredCount.toDouble() / earlyVitals.size
        
        // Check if data becomes valid after the 7-minute mark
        val laterVitals = vitalSigns.filter { 
            it.timeObj > sevenMinuteMark
        }
        
        val hasValidDataLater = laterVitals.take(10).any { vital ->
            vital.hrStatus == "valid" || vital.spO2Status == "valid" || 
            vital.nibpSysStatus == "valid" || vital.nibpMapStatus == "valid" || 
            vital.respRateStatus == "valid" || vital.etCO2Status == "valid"
        }
        
        return unmonitoredRatio >= 0.7 && hasValidDataLater
    }
    
    /**
     * Detects whether there is a HR flat-line (0 bpm or missing) for a few samples,
     * then ROSC above 60 bpm (matches Sim3 - Chuck's V-tach and shock)
     */
    private fun hasHRFlatLineROSCPattern(vitalSigns: List<VitalSign>): Boolean {
        if (vitalSigns.size < 10) return false
        
        // Skip the first few and last few readings
        val middleVitals = vitalSigns.drop(vitalSigns.size / 5).dropLast(vitalSigns.size / 5)
        
        // Look for a sequence where HR becomes 0 or null and then returns
        for (i in 0 until middleVitals.size - 5) {
            val current = middleVitals[i]
            
            // If we have a valid HR
            if (current.hr != null && current.hr!! > 40) {
                // Check if next readings show flatline
                val hasFlatline = (i + 1 until i + 4).any { idx ->
                    if (idx < middleVitals.size) {
                        val next = middleVitals[idx]
                        next.hr == null || next.hr == 0.0 || next.hrStatus != "valid"
                    } else false
                }
                
                // Check if reading after flatline shows ROSC
                val hasROSC = (i + 3 until i + 8).any { idx ->
                    if (idx < middleVitals.size) {
                        val afterFlatline = middleVitals[idx]
                        afterFlatline.hr != null && afterFlatline.hr!! >= 60 && afterFlatline.hrStatus == "valid"
                    } else false
                }
                
                if (hasFlatline && hasROSC) return true
            }
        }
        
        return false
    }
    
    /**
     * Detects whether MAP is above 110 mmHg for ten minutes or more,
     * followed by a single spike or drop of at least 25 mmHg (typical Sim4)
     */
    private fun hasHypertensionPatternForSim4(vitalSigns: List<VitalSign>): Boolean {
        if (vitalSigns.size < 20) return false
        
        val tenMinutesInMillis = 10 * 60 * 1000
        var hypertensionStart: Date? = null
        var hypertensionEnd: Date? = null
        
        // Look for 10+ minutes of MAP > 110
        for (i in vitalSigns.indices) {
            val vital = vitalSigns[i]
            
            if (vital.nibpMap != null && vital.nibpMap!! > 110 && vital.nibpMapStatus == "valid") {
                if (hypertensionStart == null) {
                    hypertensionStart = vital.timeObj
                }
                hypertensionEnd = vital.timeObj
            } else if (hypertensionStart != null) {
                // Check if we've had 10+ minutes of hypertension
                if (hypertensionEnd!!.time - hypertensionStart!!.time >= tenMinutesInMillis) {
                    // Now look for a spike or drop of 25+ mmHg
                    val indexAfterHypertension = vitalSigns.indexOfFirst { 
                        it.timeObj > hypertensionEnd!! 
                    }
                    
                    if (indexAfterHypertension >= 0) {
                        // Check next 10 readings for significant change
                        for (j in indexAfterHypertension until minOf(indexAfterHypertension + 10, vitalSigns.size)) {
                            val afterVital = vitalSigns[j]
                            if (afterVital.nibpMap != null && afterVital.nibpMapStatus == "valid") {
                                // Calculate the change from the last hypertensive reading
                                val lastHypertensive = vitalSigns[i - 1]
                                val mapChange = Math.abs(afterVital.nibpMap!! - lastHypertensive.nibpMap!!)
                                
                                if (mapChange >= 25) {
                                    return true
                                }
                            }
                        }
                    }
                }
                
                // Reset hypertension tracking
                hypertensionStart = null
                hypertensionEnd = null
            }
        }
        
        return false
    }
    
    /**
     * Detects whether there is late hypotension (MAP below 55 mmHg)
     * in the last 10 minutes of a run, with HR rising (typical of Sim1 - Dave's CPP drop)
     */
    private fun hasLateHypotensionForSim1(vitalSigns: List<VitalSign>): Boolean {
        if (vitalSigns.isEmpty()) return false
        
        val endTime = vitalSigns.last().timeObj
        val tenMinutesBeforeEnd = Calendar.getInstance().apply { 
            time = endTime
            add(Calendar.MINUTE, -10)
        }.time
        
        // Get vital signs in last 10 minutes
        val lateVitals = vitalSigns.filter { 
            it.timeObj >= tenMinutesBeforeEnd
        }
        
        if (lateVitals.isEmpty()) return false
        
        // Check for MAP below 55 mmHg with HR rising
        var hasLowMAP = false
        var initialHR: Double? = null
        var finalHR: Double? = null
        
        for (vital in lateVitals) {
            if (vital.nibpMap != null && vital.nibpMap!! < 55 && vital.nibpMapStatus == "valid") {
                hasLowMAP = true
            }
            
            if (vital.hr != null && vital.hrStatus == "valid") {
                if (initialHR == null) {
                    initialHR = vital.hr
                }
                finalHR = vital.hr
            }
        }
        
        // Check if HR increased during the period
        val hrIncreased = initialHR != null && finalHR != null && finalHR > initialHR
        
        return hasLowMAP && hrIncreased
    }
    
    /**
     * Detects whether there is whole-run tachycardia
     * (HR consistently elevated throughout the simulation, typical of Sim5 burn management)
     */
    private fun hasWholeRunTachycardia(vitalSigns: List<VitalSign>): Boolean {
        if (vitalSigns.isEmpty()) return false
        
        // Count readings with HR > 100
        val validHRReadings = vitalSigns.filter { 
            it.hr != null && it.hrStatus == "valid" 
        }
        
        if (validHRReadings.size < 10) return false
        
        val tachycardiaCount = validHRReadings.count { 
            it.hr!! > 100 
        }
        
        // Return true if at least 60% of valid HR readings show tachycardia
        return tachycardiaCount.toDouble() / validHRReadings.size >= 0.6
    }
    
    /**
     * Detects whether there are multiple SpO2 probe connection/disconnection events
     * (typical of Sims 2 and 5 with heavy line work)
     */
    private fun hasMultipleSpO2ProbeEvents(annotationEvents: List<Map<String, Any>>?): Boolean {
        if (annotationEvents == null || annotationEvents.isEmpty()) return false
        
        val probeEvents = annotationEvents.count { event ->
            val eventName = event["@EvtName"] as? String
            eventName?.contains("SpO2 Probe") == true || 
            eventName?.contains("Probe Disconnected") == true
        }
        
        // Return true if we have at least 3 probe events
        return probeEvents >= 3
    }
    
    /**
     * Detects whether there is early hypotension (MAP below 60 mmHg within the first 5 minutes)
     * that improves, then relapses once (common in Sim2)
     */
    private fun hasEarlyHypotensionRelapse(vitalSigns: List<VitalSign>): Boolean {
        if (vitalSigns.isEmpty()) return false
        
        val startTime = vitalSigns.first().timeObj
        val fiveMinuteMark = Calendar.getInstance().apply { 
            time = startTime
            add(Calendar.MINUTE, 5)
        }.time
        
        // Get vital signs in first 5 minutes
        val earlyVitals = vitalSigns.filter { 
            it.timeObj <= fiveMinuteMark
        }
        
        if (earlyVitals.isEmpty()) return false
        
        // Check for early MAP below 60 mmHg
        val hasEarlyHypotension = earlyVitals.any { vital ->
            vital.nibpMap != null && vital.nibpMap!! < 60 && vital.nibpMapStatus == "valid"
        }
        
        if (!hasEarlyHypotension) return false
        
        // Now check for improvement followed by relapse
        var improvedAfterEarly = false
        var relapsedAfterImprovement = false
        
        // Skip early vitals and check pattern
        val laterVitals = vitalSigns.filter { 
            it.timeObj > fiveMinuteMark
        }
        
        for (vital in laterVitals) {
            if (!improvedAfterEarly && vital.nibpMap != null && vital.nibpMap!! >= 60 && vital.nibpMapStatus == "valid") {
                improvedAfterEarly = true
            } else if (improvedAfterEarly && vital.nibpMap != null && vital.nibpMap!! < 60 && vital.nibpMapStatus == "valid") {
                relapsedAfterImprovement = true
                break
            }
        }
        
        return hasEarlyHypotension && improvedAfterEarly && relapsedAfterImprovement
    }
    
    /**
     * Calculates the scheduled simulation window based on simulation schedule and duration
     */
    private fun getScheduledSimulationWindow(simName: String, date: Date): Pair<Date, Date>? {
        // Get the simulation schedule for this sim
        val simSchedule = Constants.SIM_SCHEDULES[simName] ?: return null
        
        // Find the block that contains this date
        val calendar = Calendar.getInstance().apply { time = date }
        val hourOfDay = calendar.get(Calendar.HOUR_OF_DAY)
        val minute = calendar.get(Calendar.MINUTE)
        
        // Convert hour and minute to minutes since midnight for easier comparison
        val timeInMinutes = hourOfDay * 60 + minute
        
        // Helper function to convert HH:MM string to minutes
        fun timeStringToMinutes(timeStr: String): Int {
            val parts = timeStr.split(":")
            if (parts.size != 2) return 0
            return parts[0].toInt() * 60 + parts[1].toInt()
        }
        
        // Find the appropriate time block
        val block = simSchedule.firstOrNull { pair ->
            val startMinutes = timeStringToMinutes(pair.first)
            val endMinutes = timeStringToMinutes(pair.second)
            timeInMinutes in startMinutes..endMinutes
        } ?: return null
        
        // Extract start hours and minutes
        val startParts = block.first.split(":")
        val startHour = startParts[0].toInt()
        val startMinute = startParts[1].toInt()
        
        // Calculate scheduled start (block start + setup buffer)
        val scheduledStartCal = Calendar.getInstance().apply {
            time = date
            set(Calendar.HOUR_OF_DAY, startHour)
            set(Calendar.MINUTE, startMinute)
            add(Calendar.MINUTE, SETUP_BUFFER_MINUTES)
        }
        
        // Get simulation duration
        val simDuration = SimulationDurations.SIM_DURATIONS[simName] ?: 45
        
        // Use a more flexible approach for training simulations
        val adjustedDuration = if (simName.startsWith("Training")) {
            // For training sims, we'll use a longer window to allow for more flexibility
            // We'll add 30 minutes to the minimum duration to create a wider detection window
            simDuration + 30
        } else {
            simDuration
        }
        
        // Calculate scheduled end (scheduled start + simulation duration)
        val scheduledEndCal = Calendar.getInstance().apply {
            time = scheduledStartCal.time
            add(Calendar.MINUTE, adjustedDuration)
        }
        
        return Pair(scheduledStartCal.time, scheduledEndCal.time)
    }
    
    /**
     * Detects the actual start time by analyzing vital sign transitions
     * Requires sustained valid readings to confirm start, considers probe annotations.
     */
    private fun detectStartTime(
        vitalSigns: List<VitalSign>, 
        scheduledStart: Date,
        annotationEvents: List<Map<String, Any>>? = null
    ): Date {
        // Sort by time
        val sortedVitals = vitalSigns.sortedBy { it.timeObj }
        
        // Look for SpO₂ probe connected events first (if annotations available)
        var probeConnectedTime: Date? = null
        if (annotationEvents != null) {
            val probeEvents = annotationEvents.filter { event ->
                val eventName = event["@EvtName"] as? String ?: ""
                eventName.contains("SpO2 Probe", ignoreCase = true) && 
                eventName.contains("Connected", ignoreCase = true)
            }
            
            // Find the *earliest* probe connected event
            probeConnectedTime = probeEvents
                .mapNotNull { event ->
                    val eventTimeStr = event["DevDateTime"] as? String ?: ""
                    try {
                        SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.US).parse(eventTimeStr)
                } catch (e: Exception) {
                        null // Ignore parse errors
                    }
                }
                .minByOrNull { it.time } // Get the first one

            if (probeConnectedTime != null) {
                Log.d("SimulationTimeDetector", "Earliest SpO2 Probe Connected event found at: ${formatTime(probeConnectedTime)}")
            }
        }

        // We will consider probeConnectedTime later after finding a potential start based on vitals.
        val relevantVitals = sortedVitals
        
        // *** NEW LOGIC: Find the first timestamp where 3 major vitals are simultaneously valid ***
        var startCandidateFromVitals: Date? = null
            for (vital in relevantVitals) {
            // Check validity of all major vitals for this timestamp
            val hrIsValid = vital.hr != null && vital.hrStatus == "valid" && isValidHeartRate(vital.hr)
            val nibpMapIsValid = vital.nibpMap != null && vital.nibpMapStatus == "valid" && isValidBloodPressure(vital.nibpMap)
            val spo2IsValid = vital.spO2 != null && vital.spO2Status == "valid" && isValidSpO2(vital.spO2)
            val majorsValidCount = listOf(hrIsValid, spo2IsValid, nibpMapIsValid).count { it }

            // Log detailed status for debugging
            val timeStr = formatTime(vital.timeObj)
            Log.v("SimulationTimeDetector".take(23), "Time: $timeStr | Valid Majors: $majorsValidCount | HR: ${vital.hr ?: "-"} (${vital.hrStatus ?: "-"}, ${isValidHeartRate(vital.hr)})	| SpO2: ${vital.spO2 ?: "-"} (${vital.spO2Status ?: "-"}, ${spo2IsValid})	| NIBP_MAP: ${vital.nibpMap ?: "-"} (${vital.nibpMapStatus ?: "-"}, ${nibpMapIsValid}) ")

            // *** NEW: Select candidate if ANY major vital is valid ***
            if (hrIsValid || nibpMapIsValid || spo2IsValid) {
                startCandidateFromVitals = vital.timeObj
                Log.d("SimulationTimeDetector", "Found potential start candidate based on FIRST valid major vital (HR or SpO2 or MAP) at: ${formatTime(startCandidateFromVitals)}")
                break // Found the first occurrence
            }
        }
        
        // If no timestamp found with ANY valid major vital, fall back to scheduled start
        if (startCandidateFromVitals == null) {
            Log.d("SimulationTimeDetector", "No timestamp found with any valid major vital (HR, SpO2, MAP). Falling back to scheduled start: ${formatTime(scheduledStart)}")
                return scheduledStart
            }
            
        // *** VALIDATION: Check stability starting from the found candidate time ***
        // New logic: Check if AT LEAST ONE major vital remains valid consecutively.
        val maxAllowedDiffMs = 90 * 1000L
                val subsequentReadings = relevantVitals.filter { it.timeObj >= startCandidateFromVitals }

        // Check 1: Consecutive Stability (at least 4 readings with >= 1 valid major vital)
                    var consecutiveValidCount = 0
                    var maxConsecutiveValid = 0
        val requiredConsecutive = 4
        var foundConsecutiveStability = false

        if (subsequentReadings.isNotEmpty()) {
            for (i in 0 until minOf(10, subsequentReadings.size)) { // Check first 10 readings
                        val reading = subsequentReadings[i]
                // *** NEW: Check if ANY major vital is valid ***
                val hrIsValid = reading.hr != null && reading.hrStatus == "valid" && isValidHeartRate(reading.hr)
                val nibpMapIsValid = reading.nibpMap != null && reading.nibpMapStatus == "valid" && isValidBloodPressure(reading.nibpMap)
                val spo2IsValid = reading.spO2 != null && reading.spO2Status == "valid" && isValidSpO2(reading.spO2)
                val majorsValid = hrIsValid || nibpMapIsValid || spo2IsValid
                
                if (majorsValid) {
                            consecutiveValidCount++
                        } else {
                    consecutiveValidCount = 0 // Reset counter if no major vital is valid
                        }
                maxConsecutiveValid = maxOf(maxConsecutiveValid, consecutiveValidCount)
                    
                if (maxConsecutiveValid >= requiredConsecutive) {
                    foundConsecutiveStability = true
                    Log.d("SimulationTimeDetector", "Found $requiredConsecutive consecutive readings with >=1 valid major vital starting from candidate.")
                    break 
                }
            }
        }

        // Check 2: Stabilization Checkpoint at 60 seconds (at least ONE major vital must be valid)
        var isStableAt60sCheckpoint = false
        val stabilizationTimeMs = 60 * 1000L
                        val stabilizationPoint = subsequentReadings.firstOrNull { 
            it.timeObj.time >= (startCandidateFromVitals.time + stabilizationTimeMs)
                        }
                        
                        if (stabilizationPoint != null) {
             // *** NEW: Check if ANY major vital is valid ***
            val hrIsValidAt60s = stabilizationPoint.hr != null && stabilizationPoint.hrStatus == "valid" && isValidHeartRate(stabilizationPoint.hr)
            val mapIsValidAt60s = stabilizationPoint.nibpMap != null && stabilizationPoint.nibpMapStatus == "valid" && isValidBloodPressure(stabilizationPoint.nibpMap)
            val spo2IsValidAt60s = stabilizationPoint.spO2 != null && stabilizationPoint.spO2Status == "valid" && isValidSpO2(stabilizationPoint.spO2)
            isStableAt60sCheckpoint = hrIsValidAt60s || mapIsValidAt60s || spo2IsValidAt60s
            Log.d("SimulationTimeDetector", "Stabilization check at 60s (>=1 Major Valid): ${if(isStableAt60sCheckpoint) "Stable" else "Not Stable"}")
                        } else {
            Log.d("SimulationTimeDetector", "Stabilization check at 60s: No data point found.")
            isStableAt60sCheckpoint = false // Cannot confirm stability without data
        }

        // Final Decision: If consecutive stability AND 60s checkpoint are met
        if (foundConsecutiveStability && isStableAt60sCheckpoint) {
            val confirmedStartTime = startCandidateFromVitals
            Log.d("SimulationTimeDetector", "Stability confirmed (Consecutive Readings with >=1 Major Valid + 60s Checkpoint).")

            // Now, consider the probe connection time if available
                             if (probeConnectedTime != null) {
                val timeDiffMs = confirmedStartTime.time - probeConnectedTime.time
                
                                 if (timeDiffMs >= 0 && timeDiffMs <= maxAllowedDiffMs) {
                    // Vital start is within 90s AFTER probe connect: Use vital start
                    Log.d("SimulationTimeDetector", "Stable start (${formatTime(confirmedStartTime)}) is within ${maxAllowedDiffMs/1000}s after probe connect (${formatTime(probeConnectedTime)}). Using vital start.")
                    return confirmedStartTime
                } else if (timeDiffMs < 0 && timeDiffMs >= -maxAllowedDiffMs) {
                     // Vital start is within 90s BEFORE probe connect: Use probe connect time
                     Log.d("SimulationTimeDetector", "Stable start (${formatTime(confirmedStartTime)}) is within ${maxAllowedDiffMs/1000}s before probe connect (${formatTime(probeConnectedTime)}). Using probe connect time.")
                     return probeConnectedTime
                } else if (timeDiffMs < -maxAllowedDiffMs) {
                    // Vital start is significantly BEFORE probe connect: Indicates potential issue, use vital start but log warning.
                    Log.w("SimulationTimeDetector", "Warning: Stable start (${formatTime(confirmedStartTime)}) detected significantly before probe connect (${formatTime(probeConnectedTime)}). Using vital start, but check data.")
                     return confirmedStartTime
                                 } else {
                    // Vital start is significantly AFTER probe connect: Use vital start (normal scenario)
                    Log.d("SimulationTimeDetector", "Stable start (${formatTime(confirmedStartTime)}) is significantly after probe connect (${formatTime(probeConnectedTime)}). Using vital start.")
                    return confirmedStartTime
                                 }
                             } else {
                // No probe time, just use the confirmed vital start time
                Log.d("SimulationTimeDetector", "Stability confirmed, no probe annotation found. Using vital start: ${formatTime(confirmedStartTime)}")
                return confirmedStartTime
             }
        }

        // If stability checks fail, fall back to scheduled start
        Log.d("SimulationTimeDetector", "Stability checks failed (Consecutive >=1 Major: $foundConsecutiveStability, 60s Checkpoint >=1 Major: $isStableAt60sCheckpoint). Falling back to scheduled start: ${formatTime(scheduledStart)}")
        return scheduledStart
    }
    
    /**
     * Detects the actual end time by looking for sustained periods without valid data
     * or specific power-off annotation events
     */
    private fun detectEndTime(
        vitalSigns: List<VitalSign>, 
        actualStart: Date, 
        scheduledEnd: Date,
        annotationEvents: List<Map<String, Any>>? = null
    ): Date {
        Log.i("SimDetectEnd_Debug", "Entering detectEndTime. actualStart=${formatTime(actualStart)}, scheduledEnd=${formatTime(scheduledEnd)}") // ENTRY LOG
        // Sort by time
        val sortedVitals = vitalSigns
            .filter { it.timeObj >= actualStart } // Filter data >= actualStart
            .sortedBy { it.timeObj }
        
        Log.i("SimDetectEnd_Debug", "Found ${sortedVitals.size} vitals >= actualStart for analysis.") // COUNT LOG
        
        if (sortedVitals.isEmpty()) {
            Log.w("SimDetectEnd_Debug", "No vitals found >= actualStart. Returning scheduledEnd: ${formatTime(scheduledEnd)}") // EMPTY LOG
            return scheduledEnd
        }
        
        // Check for power-off or shutdown annotation events (Keep this logic as is)
        if (annotationEvents != null) {
            // Get events after the actual start time
            val relevantEvents = annotationEvents
                .filter { event ->
                    val eventTime = event["DevDateTime"] as? String ?: ""
                    if (eventTime.isNotEmpty()) {
                        try {
                            val date = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.US).parse(eventTime)
                            date != null && date >= actualStart
                        } catch (e: Exception) {
                            false
                        }
                    } else {
                        false
                    }
                }
                .sortedBy { event ->
                    val eventTime = event["DevDateTime"] as? String ?: ""
                    try {
                        SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.US).parse(eventTime)
                    } catch (e: Exception) {
                        Date(0) // Earliest possible date as fallback
                    }
                }
            
            // Look for power off events
            for (event in relevantEvents) {
                val eventName = event["@EvtName"] as? String ?: ""
                val eventTime = event["DevDateTime"] as? String ?: ""
                
                // Check for power off or system shutdown events
                if (eventName.contains("Power Off", ignoreCase = true) || 
                    eventName.contains("System Off", ignoreCase = true) ||
                    eventName.contains("Shutdown", ignoreCase = true) ||
                    eventName.contains("Last Power", ignoreCase = true)) {
                    
                    try {
                        val date = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.US).parse(eventTime)
                        if (date != null) {
                            // Make sure the event is after start and not too far past the scheduled end
                            if (date > actualStart && 
                                date.time - scheduledEnd.time < TimeUnit.MINUTES.toMillis(15)) {
                                return date
                            }
                        }
                    } catch (e: Exception) {
                        // Parsing failed, continue with other detection methods
                    }
                }
            }
        }
        
        // If power off event was found and returned, the rest of the function is skipped.
        Log.i("SimDetectEnd_Debug", "Power off annotation check complete. Proceeding to vital sign analysis.") // ANNOTATION CHECK LOG
        
        // Check for a sustained period where required major vital signs are unmonitored or invalid
        var lastValidDataTime = actualStart // Initialize with actualStart
        Log.i("SimDetectEnd_Debug", "Initial lastValidDataTime set to actualStart: ${formatTime(lastValidDataTime)}") // INIT LOG
        
        for (i in sortedVitals.indices) {
            val vital = sortedVitals[i]
            val currentTime = vital.timeObj
            
            // Check if this vital sign has at least 2 valid major vitals (with physiological bounds check)
            val hrValid = vital.hr != null && vital.hrStatus == "valid" && isValidHeartRate(vital.hr)
            val spo2Valid = vital.spO2 != null && vital.spO2Status == "valid" && isValidSpO2(vital.spO2)
            val nibpMapValid = vital.nibpMap != null && vital.nibpMapStatus == "valid" && isValidBloodPressure(vital.nibpMap)
            
            val validMajorVitalsCount = listOf(hrValid, spo2Valid, nibpMapValid).count { it }
            // *** RELAXING CONDITION: Change back from >= 2 to >= 1 ***
            val hasSufficientValidVitals = validMajorVitalsCount >= 1 // Relaxed requirement

            // DETAILED LOG PER VITAL
            Log.v("SimDetectEnd_Debug", "[${formatTime(currentTime)}] Vitals Valid (>=1): $hasSufficientValidVitals (Count: $validMajorVitalsCount) | HR:$hrValid SpO2:$spo2Valid MAP:$nibpMapValid | Current lastValidDataTime: ${formatTime(lastValidDataTime)}") // Log message updated slightly

            if (hasSufficientValidVitals) {
                // This vital sign has enough valid data, update lastValidDataTime
                if (currentTime.after(lastValidDataTime)) { // Ensure we only move forward
                    lastValidDataTime = currentTime
                    Log.d("SimDetectEnd_Debug", "  -> Updated lastValidDataTime to: ${formatTime(lastValidDataTime)}") // UPDATE LOG
                }
            } else if (i > 0) {
                // This vital doesn't have sufficient valid data. Check for sustained inactivity.
                val timeWithoutValidData = currentTime.time - lastValidDataTime.time
                val minutesWithoutValidData = TimeUnit.MILLISECONDS.toMinutes(timeWithoutValidData)
                
                Log.d("SimDetectEnd_Debug", "  -> Insufficient vitals. Time since last valid: ${minutesWithoutValidData} min (Threshold: $MIN_SUSTAINED_INACTIVE_MINUTES min)") // INACTIVITY CHECK LOG
                
                if (minutesWithoutValidData >= MIN_SUSTAINED_INACTIVE_MINUTES) {
                    // We found the end - it's when we last had valid data
                    Log.i("SimDetectEnd_Debug", "SUSTAINED INACTIVITY DETECTED. Returning lastValidDataTime: ${formatTime(lastValidDataTime)}") // INACTIVITY RETURN LOG
                    return lastValidDataTime
                }
            }
        }
        
        // If we reach here, sustained inactivity was not detected before the end of the list.
        // Use the determined lastValidDataTime as the end.
        Log.i("SimDetectEnd_Debug", "Reached end of loop without sustained inactivity. Returning final lastValidDataTime: ${formatTime(lastValidDataTime)}") // FINAL RETURN LOG
        return lastValidDataTime
    }
    
    /**
     * Fallback method that detects timeframe purely from data patterns
     * without relying on scheduled windows
     */
    private fun detectTimeframeFromDataOnly(
        vitalSigns: List<VitalSign>,
        annotationEvents: List<Map<String, Any>>? = null
    ): Pair<Date, Date>? {
        if (vitalSigns.isEmpty()) {
            return null
        }
        
        // Look for SpO₂ probe connected events first (if annotations available)
        var probeConnectedTime: Date? = null
        if (annotationEvents != null) {
            val probeEvents = annotationEvents.filter { event ->
                val eventName = event["@EvtName"] as? String ?: ""
                eventName.contains("SpO2 Probe", ignoreCase = true) && 
                eventName.contains("Connected", ignoreCase = true)
            }
            
            if (probeEvents.isNotEmpty()) {
                val eventTimeStr = probeEvents.first()["DevDateTime"] as? String ?: ""
                try {
                    probeConnectedTime = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.US).parse(eventTimeStr)
                } catch (e: Exception) {
                    // Parsing failed, continue without probe time
                }
            }
        }
        
        // Sort by time and filter by probe connection time if available
        val sortedVitals = vitalSigns.sortedBy { it.timeObj }
        val relevantVitals = if (probeConnectedTime != null) {
            sortedVitals.filter { it.timeObj >= probeConnectedTime!! }
        } else {
            sortedVitals
        }
        
        // Identify periods of activity (>= 3 consecutive readings with 2+ valid major vitals)
        val activityPeriods = mutableListOf<Pair<Date, Date>>()
        var currentStartTime: Date? = null
        var lastValidTime: Date? = null
        var consecutiveValidCount = 0
        
        for (i in relevantVitals.indices) {
            val vital = relevantVitals[i]
            
            // Check if this vital sign has valid major vitals (with physiological bounds check)
            // *** MODIFICATION START: Require >= 2 valid major vitals for activity period detection ***
            val validMajorVitalsCount = listOf(
                Triple(vital.hr, vital.hrStatus, isValidHeartRate(vital.hr)),
                Triple(vital.spO2, vital.spO2Status, isValidSpO2(vital.spO2)),
                Triple(vital.nibpMap, vital.nibpMapStatus, isValidBloodPressure(vital.nibpMap))
            ).count { (value, status, isPhysiologicallyValid) ->
                value != null && status == "valid" && isPhysiologicallyValid
            }
            
            // Require at least 2 major vitals to be valid (CHANGED FROM >= 1)
            val hasValidMajorVitals = validMajorVitalsCount >= 2
            // *** MODIFICATION END ***
            
            if (hasValidMajorVitals) {
                consecutiveValidCount++
                lastValidTime = vital.timeObj
                
                // Only start recording activity after 3 consecutive readings with 2+ valid major vitals
                if (currentStartTime == null && consecutiveValidCount >= 3) {
                    // Look back to find the actual start time (when valid data first appeared)
                    val lookbackIndex = maxOf(0, i - 2)
                    currentStartTime = relevantVitals[lookbackIndex].timeObj
                }
                
                // If this is the last element, end an activity period
                if (i == relevantVitals.size - 1 && currentStartTime != null) {
                        activityPeriods.add(Pair(currentStartTime, lastValidTime))
                    }
                } else {
                consecutiveValidCount = 0
                
                // If we were in an activity period, check if it should end
                if (currentStartTime != null && lastValidTime != null) {
                    // Check for a gap in valid data
                    val gapTime = vital.timeObj.time - lastValidTime.time
                    
                    if (TimeUnit.MILLISECONDS.toMinutes(gapTime) >= MIN_SUSTAINED_INACTIVE_MINUTES) {
                        // End of an activity period
                        activityPeriods.add(Pair(currentStartTime, lastValidTime))
                        currentStartTime = null
                    }
                }
            }
            
            // Check for a gap to the next reading
            if (i < relevantVitals.size - 1) {
                val nextVital = relevantVitals[i + 1]
                val gapTime = nextVital.timeObj.time - vital.timeObj.time
                
                if (TimeUnit.MILLISECONDS.toMinutes(gapTime) >= MIN_SUSTAINED_INACTIVE_MINUTES) {
                    if (currentStartTime != null && lastValidTime != null) {
                    // End of an activity period
                    activityPeriods.add(Pair(currentStartTime, lastValidTime))
                    currentStartTime = null
                        consecutiveValidCount = 0
                    }
                }
            }
        }
        
        // Check for power-off events if we have annotation events
        if (annotationEvents != null && !activityPeriods.isEmpty()) {
            for (period in activityPeriods) {
                // Check if there are any power-off events that would end this period
                val (startDate, endDate) = period
                
                // Find relevant power-off events
                val powerOffEvents = annotationEvents.filter { event ->
                    val eventName = event["@EvtName"] as? String ?: ""
                    val eventTimeStr = event["DevDateTime"] as? String ?: ""
                    
                    // Check if it's a power-off type of event
                    val isPowerOffEvent = eventName.contains("Power Off", ignoreCase = true) || 
                                         eventName.contains("System Off", ignoreCase = true) ||
                                         eventName.contains("Shutdown", ignoreCase = true) ||
                                         eventName.contains("Last Power", ignoreCase = true)
                    
                    if (isPowerOffEvent && eventTimeStr.isNotEmpty()) {
                        try {
                            val eventDate = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.US).parse(eventTimeStr)
                            // Check if event is after start and within a reasonable time after the current end
                            eventDate != null && 
                            eventDate > startDate && 
                            eventDate.time - endDate.time < TimeUnit.MINUTES.toMillis(15)
                        } catch (e: Exception) {
                            false
                        }
                    } else {
                        false
                    }
                }
                
                // If we found power-off events, adjust the end time
                if (powerOffEvents.isNotEmpty()) {
                    val eventTimeStr = powerOffEvents.first()["DevDateTime"] as? String ?: ""
                    try {
                        val eventDate = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.US).parse(eventTimeStr)
                        if (eventDate != null) {
                            // Return the period with adjusted end time
                            return Pair(startDate, eventDate)
                        }
                    } catch (e: Exception) {
                        // If parsing fails, keep original end date
                    }
                }
            }
        }
        
        if (activityPeriods.isEmpty()) {
            return null
        }
        
        // Enhanced selection logic for multiple activity periods
        if (activityPeriods.size > 1) {
            // Sort periods by duration (descending)
            val sortedByDuration = activityPeriods.sortedByDescending { (start, end) -> 
            end.time - start.time
        }
            
            // Get the longest period
            val longestPeriod = sortedByDuration.first()
            val longestDuration = longestPeriod.second.time - longestPeriod.first.time
            
            // Check if there are other periods with similar duration (within 60 seconds/1 minute)
            val similarPeriods = sortedByDuration.filter { (start, end) ->
                val duration = end.time - start.time
                val differenceMs = Math.abs(duration - longestDuration)
                differenceMs <= TimeUnit.MINUTES.toMillis(1) // Within 1 minute of the longest
            }
            
            // If we have multiple similar-length periods, prefer the earliest one
            if (similarPeriods.size > 1) {
                return similarPeriods.minByOrNull { it.first.time }
            }
            
            // Otherwise return the longest period
            return longestPeriod
        }
        
        // If only one period, return it
        return activityPeriods.first()
    }
    
    /**
     * Validates if a heart rate value is within physiologically plausible bounds
     */
    private fun isValidHeartRate(hr: Double?): Boolean {
        return hr != null && hr >= 30.0 && hr <= 250.0
    }
    
    /**
     * Validates if an SpO₂ value is within physiologically plausible bounds
     */
    private fun isValidSpO2(spO2: Double?): Boolean {
        return spO2 != null && spO2 >= 50.0 && spO2 <= 100.0
    }
    
    /**
     * Validates if a blood pressure value is within physiologically plausible bounds
     */
    private fun isValidBloodPressure(bp: Double?): Boolean {
        return bp != null && bp >= 30.0 && bp <= 250.0
    }
    
    /**
     * Validates if a respiratory rate value is within physiologically plausible bounds
     */
    private fun isValidRespRate(respRate: Double?): Boolean {
        return respRate != null && respRate >= 5.0 && respRate <= 60.0
    }
    
    /**
     * Validates if an EtCO₂ value is within physiologically plausible bounds
     */
    private fun isValidEtCO2(etCO2: Double?): Boolean {
        return etCO2 != null && etCO2 >= 10.0 && etCO2 <= 100.0
    }
    
    /**
     * Validates if a temperature value is within physiologically plausible bounds
     */
    private fun isValidTemperature(temp: Double?): Boolean {
        return temp != null && temp >= 90.0 && temp <= 110.0
    }
    
    /**
     * Formats a date for debugging
     */
    fun formatTime(date: Date): String {
        return SimpleDateFormat("HH:mm:ss", Locale.US).format(date)
    }
    
    /**
     * Checks if a given timestamp is within the detected simulation timeframe
     */
    fun isInSimulationTimeframe(timestamp: Date, startTime: Date, endTime: Date): Boolean {
        return timestamp in startTime..endTime
    }
    
    /**
     * Test utility method to generate sample vital signs for testing the detection algorithm
     * with different edge conditions
     *
     * @param baseTime The base time to start the sequence
     * @param scenario The test scenario to generate
     * @param mannequinName The mannequin name to use
     * @return List of generated vital signs for testing
     */
    fun generateTestData(
        baseTime: Date,
        scenario: TestScenario,
        mannequinName: String = "Dave"
    ): Pair<List<VitalSign>, List<Map<String, Any>>> {
        val vitalSigns = mutableListOf<VitalSign>()
        val annotationEvents = mutableListOf<Map<String, Any>>()
        
        val calendar = Calendar.getInstance().apply { time = baseTime }
        
        // Generate data based on scenario
        when (scenario) {
            TestScenario.NORMAL_SIMULATION -> {
                // 2 minutes of unmonitored data
                for (i in 0 until 12) { // 2 minutes at 10-second intervals
                    vitalSigns.add(createUnmonitoredVitalSign(calendar.time, mannequinName))
                    calendar.add(Calendar.SECOND, 10)
                }
                
                // 45 minutes of valid data (typical sim duration)
                for (i in 0 until 270) { // 45 minutes at 10-second intervals
                    vitalSigns.add(createValidVitalSign(calendar.time, mannequinName))
                    calendar.add(Calendar.SECOND, 10)
                }
                
                // 2 minutes of unmonitored data
                for (i in 0 until 12) { // 2 minutes at 10-second intervals
                    vitalSigns.add(createUnmonitoredVitalSign(calendar.time, mannequinName))
                    calendar.add(Calendar.SECOND, 10)
                }
            }
            
            TestScenario.EARLY_JUNK_DATA -> {
                // 10 minutes of inconsistent data with occasional valid readings
                for (i in 0 until 60) { // 10 minutes at 10-second intervals
                    val isValid = (i % 7 == 0) // Occasional valid reading
                    if (isValid) {
                        vitalSigns.add(createValidVitalSign(calendar.time, mannequinName))
                    } else {
                        vitalSigns.add(createUnmonitoredVitalSign(calendar.time, mannequinName))
                    }
                    calendar.add(Calendar.SECOND, 10)
                }
                
                // 45 minutes of valid data (typical sim duration)
                for (i in 0 until 270) { // 45 minutes at 10-second intervals
                    vitalSigns.add(createValidVitalSign(calendar.time, mannequinName))
                    calendar.add(Calendar.SECOND, 10)
                }
                
                // 2 minutes of unmonitored data
                for (i in 0 until 12) { // 2 minutes at 10-second intervals
                    vitalSigns.add(createUnmonitoredVitalSign(calendar.time, mannequinName))
                    calendar.add(Calendar.SECOND, 10)
                }
            }
            
            TestScenario.MID_SIMULATION_GAP -> {
                // 2 minutes of unmonitored data
                for (i in 0 until 12) { // 2 minutes at 10-second intervals
                    vitalSigns.add(createUnmonitoredVitalSign(calendar.time, mannequinName))
                    calendar.add(Calendar.SECOND, 10)
                }
                
                // 20 minutes of valid data
                for (i in 0 until 120) { // 20 minutes at 10-second intervals
                    vitalSigns.add(createValidVitalSign(calendar.time, mannequinName))
                    calendar.add(Calendar.SECOND, 10)
                }
                
                // 2.5 minute gap (just under the 3-minute threshold)
                calendar.add(Calendar.MINUTE, 2)
                calendar.add(Calendar.SECOND, 30)
                
                // 25 minutes more of valid data
                for (i in 0 until 150) { // 25 minutes at 10-second intervals
                    vitalSigns.add(createValidVitalSign(calendar.time, mannequinName))
                    calendar.add(Calendar.SECOND, 10)
                }
                
                // 2 minutes of unmonitored data
                for (i in 0 until 12) { // 2 minutes at 10-second intervals
                    vitalSigns.add(createUnmonitoredVitalSign(calendar.time, mannequinName))
                    calendar.add(Calendar.SECOND, 10)
                }
            }
            
            TestScenario.SUDDEN_POWER_OFF -> {
                // 2 minutes of unmonitored data
                for (i in 0 until 12) { // 2 minutes at 10-second intervals
                    vitalSigns.add(createUnmonitoredVitalSign(calendar.time, mannequinName))
                    calendar.add(Calendar.SECOND, 10)
                }
                
                // 45 minutes of valid data (typical sim duration)
                for (i in 0 until 270) { // 45 minutes at 10-second intervals
                    vitalSigns.add(createValidVitalSign(calendar.time, mannequinName))
                    calendar.add(Calendar.SECOND, 10)
                }
                
                // Add power off annotation event
                val powerOffTime = calendar.time
                annotationEvents.add(
                    mapOf(
                        "@EvtName" to "System Power Off",
                        "DevDateTime" to SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.US).format(powerOffTime),
                        "DevEvtCode" to 24,
                        "XidCode" to "00000018"
                    )
                )
            }
            
            TestScenario.MULTIPLE_SIMILAR_PERIODS -> {
                // First activity period - 45 minutes
                for (i in 0 until 12) { // 2 minutes of unmonitored
                    vitalSigns.add(createUnmonitoredVitalSign(calendar.time, mannequinName))
                    calendar.add(Calendar.SECOND, 10)
                }
                
                val firstStart = calendar.time
                
                for (i in 0 until 270) { // 45 minutes of valid data
                    vitalSigns.add(createValidVitalSign(calendar.time, mannequinName))
                    calendar.add(Calendar.SECOND, 10)
                }
                
                for (i in 0 until 24) { // 4 minutes of unmonitored (clear gap)
                    vitalSigns.add(createUnmonitoredVitalSign(calendar.time, mannequinName))
                    calendar.add(Calendar.SECOND, 10)
                }
                
                // Second activity period - 44 minutes (slightly shorter)
                val secondStart = calendar.time
                
                for (i in 0 until 264) { // 44 minutes of valid data
                    vitalSigns.add(createValidVitalSign(calendar.time, mannequinName))
                    calendar.add(Calendar.SECOND, 10)
                }
                
                for (i in 0 until 12) { // 2 minutes of unmonitored
                    vitalSigns.add(createUnmonitoredVitalSign(calendar.time, mannequinName))
                    calendar.add(Calendar.SECOND, 10)
                }
            }
        }
        
        return Pair(vitalSigns, annotationEvents)
    }
    
    /**
     * Create a valid vital sign for testing
     */
    private fun createValidVitalSign(time: Date, mannequinName: String): VitalSign {
        return VitalSign(
            timeObj = time,
            timeStr = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.US).format(time),
            deviceSerial = "TEST-DEVICE",
            sourceFile = "test_file.json",
            
            hr = 80.0,
            hrStatus = "valid",
            
            spO2 = 99.0,
            spO2Status = "valid",
            
            nibpSys = 120.0,
            nibpSysStatus = "valid",
            
            nibpDia = 80.0,
            nibpDiaStatus = "valid",
            
            nibpMap = 93.0,
            nibpMapStatus = "valid",
            
            temp1 = 98.6,
            temp1Status = "valid",
            
            respRate = 16.0,
            respRateStatus = "valid",
            
            etCO2 = 40.0,
            etCO2Status = "valid",
            
            overrideMannequin = mannequinName,
            scenario = "TestScenario"
        )
    }
    
    /**
     * Create an unmonitored vital sign for testing
     */
    private fun createUnmonitoredVitalSign(time: Date, mannequinName: String): VitalSign {
        return VitalSign(
            timeObj = time,
            timeStr = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.US).format(time),
            deviceSerial = "TEST-DEVICE",
            sourceFile = "test_file.json",
            
            hr = null,
            hrStatus = "unmonitored",
            
            spO2 = null,
            spO2Status = "unmonitored",
            
            nibpSys = null,
            nibpSysStatus = "unmonitored",
            
            nibpDia = null,
            nibpDiaStatus = "unmonitored",
            
            nibpMap = null,
            nibpMapStatus = "unmonitored",
            
            temp1 = null,
            temp1Status = "unmonitored",
            
            respRate = null,
            respRateStatus = "unmonitored",
            
            etCO2 = null,
            etCO2Status = "unmonitored",
            
            overrideMannequin = mannequinName,
            scenario = "TestScenario"
        )
    }
    
    /**
     * Test scenarios for simulation time detection
     */
    enum class TestScenario {
        NORMAL_SIMULATION,         // Standard simulation with clean start/end
        EARLY_JUNK_DATA,           // Lots of inconsistent data before simulation starts
        MID_SIMULATION_GAP,        // Gap in the middle of simulation (< 3 minutes)
        SUDDEN_POWER_OFF,          // Simulation ends with sudden power off event
        MULTIPLE_SIMILAR_PERIODS   // Multiple activity periods with similar lengths
    }
} 