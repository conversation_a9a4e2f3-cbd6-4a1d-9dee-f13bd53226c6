package com.example.myapplication.data.source

import android.annotation.SuppressLint
import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.net.wifi.WifiManager
import android.os.Build
import android.util.Log
import com.example.myapplication.data.ClinicalPracticeGuidelines
import com.example.myapplication.data.model.ClinicalEvent
import com.example.myapplication.data.model.ClinicalEventType
import com.example.myapplication.data.model.VitalSign
import com.zoll.zoxseries.ZOXSeries
import com.zoll.zoxseries.DeviceBrowser
import com.zoll.zoxseries.DeviceApi
import com.zoll.zoxseries.callback.DevicesBrowserCallback
import com.zoll.zoxseries.callback.VitalSignsCallback
import com.zoll.zoxseries.model.XSeriesDevice
import com.zoll.zoxseries.model.VitalSignsReport as ZollVitalSignsReport // Alias to avoid name clash
import com.zoll.zoxseries.model.TrendReport as ZollTrendReport
import com.zoll.zoxseries.model.ZOXSeriesException
import com.zoll.zoxseries.model.ValueUnitPair
import java.net.NetworkInterface
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import java.io.File
import java.util.Date
import java.util.Locale
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.TimeUnit
import okhttp3.Credentials
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import java.io.IOException
import java.util.concurrent.atomic.AtomicBoolean

/**
 * Implementation of VitalSignsDataSource for connecting to Zoll monitors via HTTP/WebSocket
 */
@SuppressLint("MissingPermission")
class ZollMonitorDataSource(private val context: Context) : VitalSignsDataSource {

    // Connection mode (HTTP, WebSocket)
    enum class ConnectionMode {
        HTTP,
        WEBSOCKET
    }

    // Current connection mode
    private var connectionMode = ConnectionMode.HTTP

    // Connection details
    private var ipAddress = "*************"
    private var port = 80
    private var mannequinName = "Default"

    // Authentication details
    private var username = ""
    private var password = ""
    private var authRequired = false

    // Whether monitoring is active
    private val isMonitoring = AtomicBoolean(false)

    // Update interval
    private var updateIntervalMs: Long = 1000

    private var httpClient: OkHttpClient? = null

    private var deviceBrowser: DeviceBrowser? = null
    private val deviceApi: DeviceApi = ZOXSeries.getDeviceApi()

    // Flag to track if device browser is active to prevent double-stopping
    private val isDeviceBrowserActive = AtomicBoolean(false)

    // MulticastLock for network discovery
    private var multicastLock: WifiManager.MulticastLock? = null
    private val isMulticastLockHeld = AtomicBoolean(false)

    private val discoveredDevices = ConcurrentHashMap<String, XSeriesDevice>() // Store by serial number
    private val activePollingJobs = ConcurrentHashMap<String, Job>() // Store polling jobs by serial number

    /**
     * Get information about all network interfaces
     * This helps with debugging network discovery issues
     */
    private fun logNetworkInterfaces() {
        try {
            val interfaces = NetworkInterface.getNetworkInterfaces()
            Log.d("ZollMonitorDataSource", "Network interfaces:")
            while (interfaces.hasMoreElements()) {
                val networkInterface = interfaces.nextElement()
                val isUp = networkInterface.isUp
                val supportsMulticast = networkInterface.supportsMulticast()
                val isLoopback = networkInterface.isLoopback
                val isP2p = networkInterface.isPointToPoint
                val isVirtual = networkInterface.isVirtual

                Log.d("ZollMonitorDataSource", "Interface: ${networkInterface.name}, " +
                        "Display Name: ${networkInterface.displayName}, " +
                        "Up: $isUp, Multicast: $supportsMulticast, " +
                        "Loopback: $isLoopback, P2P: $isP2p, Virtual: $isVirtual")

                val addresses = networkInterface.inetAddresses
                while (addresses.hasMoreElements()) {
                    val address = addresses.nextElement()
                    Log.d("ZollMonitorDataSource", "  Address: ${address.hostAddress}, " +
                            "Loopback: ${address.isLoopbackAddress}, " +
                            "Link Local: ${address.isLinkLocalAddress}, " +
                            "Site Local: ${address.isSiteLocalAddress}")
                }
            }
        } catch (e: Exception) {
            Log.e("ZollMonitorDataSource", "Error getting network interfaces: ${e.message}")
        }
    }

    /**
     * Check if the device is currently acting as a WiFi hotspot
     */
    private fun isHotspotActive(): Boolean {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
                val networks = connectivityManager.allNetworks

                for (network in networks) {
                    val capabilities = connectivityManager.getNetworkCapabilities(network)
                    if (capabilities != null &&
                        capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) &&
                        capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_NOT_RESTRICTED) &&
                        !capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)) {
                        // This is likely a hotspot network
                        return true
                    }
                }
            } else {
                // For older Android versions, check for ap0 or wlan0 interfaces
                val interfaces = NetworkInterface.getNetworkInterfaces()
                while (interfaces.hasMoreElements()) {
                    val networkInterface = interfaces.nextElement()
                    if (networkInterface.name == "ap0" ||
                        (networkInterface.name == "wlan0" && isWifiApEnabled())) {
                        return true
                    }
                }
            }
        } catch (e: Exception) {
            Log.e("ZollMonitorDataSource", "Error checking hotspot status: ${e.message}")
        }
        return false
    }

    /**
     * Check if WiFi AP is enabled (for older Android versions)
     */
    @SuppressLint("DiscouragedPrivateApi")
    private fun isWifiApEnabled(): Boolean {
        try {
            val wifiManager = context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
            val method = wifiManager.javaClass.getDeclaredMethod("isWifiApEnabled")
            method.isAccessible = true
            return method.invoke(wifiManager) as Boolean
        } catch (e: Exception) {
            Log.e("ZollMonitorDataSource", "Error checking WiFi AP status: ${e.message}")
            return false
        }
    }

    /**
     * Safely acquire the MulticastLock if not already held
     * @return true if the lock was acquired or was already held, false if it failed
     */
    private fun acquireMulticastLock(): Boolean {
        if (isMulticastLockHeld.get()) {
            Log.d("ZollMonitorDataSource", "MulticastLock already held, not acquiring again")
            return true
        }

        try {
            // Log network interfaces for debugging
            logNetworkInterfaces()

            // Check if device is acting as a hotspot
            val hotspotActive = isHotspotActive()
            Log.d("ZollMonitorDataSource", "Device hotspot active: $hotspotActive")

            val wifiManager = context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
            multicastLock = wifiManager.createMulticastLock("ZollMonitorMulticastLock")

            // For hotspot scenarios, we want to make sure the lock is not reference counted
            // This ensures it stays acquired until explicitly released
            multicastLock?.setReferenceCounted(!hotspotActive)

            multicastLock?.acquire()
            isMulticastLockHeld.set(true)
            Log.d("ZollMonitorDataSource", "MulticastLock acquired successfully (reference counted: ${!hotspotActive})")
            return true
        } catch (e: Exception) {
            Log.e("ZollMonitorDataSource", "Failed to acquire MulticastLock: ${e.message}")
            return false
        }
    }

    /**
     * Safely release the MulticastLock if it's held
     */
    private fun releaseMulticastLock() {
        if (!isMulticastLockHeld.get()) {
            Log.d("ZollMonitorDataSource", "MulticastLock not held, nothing to release")
            return
        }

        try {
            multicastLock?.release()
            isMulticastLockHeld.set(false)
            Log.d("ZollMonitorDataSource", "MulticastLock released successfully")
        } catch (e: Exception) {
            Log.e("ZollMonitorDataSource", "Error releasing MulticastLock: ${e.message}")
        }
    }

    override suspend fun loadDataFromDirectory(directory: File): List<VitalSign> {
        // This data source doesn't load from files
        return emptyList()
    }

    override suspend fun loadDataFromFile(file: File): List<VitalSign> {
        // This data source doesn't load from files
        return emptyList()
    }

    /**
     * Log information about the WiFi connection status
     * This helps diagnose issues with the "NOT_CONNECTED_TO_WIFI" error
     */
    private fun logWifiConnectionStatus() {
        try {
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            val wifiManager = context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager

            // Check if WiFi is enabled
            val wifiEnabled = wifiManager.isWifiEnabled
            Log.d("ZollMonitorDataSource", "WiFi enabled: $wifiEnabled")

            // Check if connected to WiFi
            var connectedToWifi = false
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val network = connectivityManager.activeNetwork
                val capabilities = connectivityManager.getNetworkCapabilities(network)
                connectedToWifi = capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) == true
            } else {
                @Suppress("DEPRECATION")
                val networkInfo = connectivityManager.getActiveNetworkInfo()
                connectedToWifi = networkInfo?.type == ConnectivityManager.TYPE_WIFI && networkInfo.isConnected
            }
            Log.d("ZollMonitorDataSource", "Connected to WiFi: $connectedToWifi")

            // Log WiFi connection details
            try {
                val connectionInfo = wifiManager.connectionInfo
                Log.d("ZollMonitorDataSource", "WiFi SSID: ${connectionInfo.ssid}")
                Log.d("ZollMonitorDataSource", "WiFi BSSID: ${connectionInfo.bssid}")
                Log.d("ZollMonitorDataSource", "WiFi IP address: ${android.text.format.Formatter.formatIpAddress(connectionInfo.ipAddress)}")
                Log.d("ZollMonitorDataSource", "WiFi link speed: ${connectionInfo.linkSpeed} Mbps")
                Log.d("ZollMonitorDataSource", "WiFi signal strength: ${connectionInfo.rssi} dBm")
            } catch (e: Exception) {
                Log.e("ZollMonitorDataSource", "Error getting WiFi connection info: ${e.message}")
            }

            // Log hotspot status
            val isHotspot = isHotspotActive()
            Log.d("ZollMonitorDataSource", "Hotspot active: $isHotspot")

            // Log DHCP info
            try {
                val dhcpInfo = wifiManager.dhcpInfo
                if (dhcpInfo != null) {
                    Log.d("ZollMonitorDataSource", "DHCP server address: ${android.text.format.Formatter.formatIpAddress(dhcpInfo.serverAddress)}")
                    Log.d("ZollMonitorDataSource", "DHCP gateway: ${android.text.format.Formatter.formatIpAddress(dhcpInfo.gateway)}")
                    Log.d("ZollMonitorDataSource", "DHCP netmask: ${android.text.format.Formatter.formatIpAddress(dhcpInfo.netmask)}")
                }
            } catch (e: Exception) {
                Log.e("ZollMonitorDataSource", "Error getting DHCP info: ${e.message}")
            }
        } catch (e: Exception) {
            Log.e("ZollMonitorDataSource", "Error checking WiFi status: ${e.message}")
        }
    }

    override fun getRealtimeVitalSigns(): Flow<VitalSign> = callbackFlow {
        // Only initialize device browser if it's not already active
        if (!isDeviceBrowserActive.get()) {
            // Log detailed WiFi connection status to help diagnose issues
            Log.d("ZollMonitorDataSource", "Checking WiFi connection status before starting device discovery")
            logWifiConnectionStatus()

            // First acquire the MulticastLock before starting device discovery
            if (acquireMulticastLock()) {
                Log.d("ZollMonitorDataSource", "MulticastLock acquired, initializing device browser")

                // Check if we're in hotspot mode
                val isHotspot = isHotspotActive()
                Log.d("ZollMonitorDataSource", "Hotspot mode detected: $isHotspot")

                try {
                    // Log network interfaces for debugging
                    logNetworkInterfaces()

                    // Initialize the device browser
                    Log.d("ZollMonitorDataSource", "Initializing device browser")
                    deviceBrowser = ZOXSeries.getDeviceBrowser()

                    val browserCallback = object : DevicesBrowserCallback {
                        override fun onXSeriesDeviceFound(device: XSeriesDevice) {
                            Log.i("ZollMonitorDataSource", "ZOX SDK: Device Found - Serial: ${device.serialNumber}, Address: ${device.address}")
                            println("ZOX SDK: Device Found - Serial: ${device.serialNumber}, Address: ${device.address}")
                            device.serialNumber?.let { serial ->
                                discoveredDevices[serial] = device
                                startPollingForDevice(device, this@callbackFlow)
                            }
                        }

                        override fun onXSeriesDeviceLost(device: XSeriesDevice) {
                            Log.i("ZollMonitorDataSource", "ZOX SDK: Device Lost - Serial: ${device.serialNumber}")
                            println("ZOX SDK: Device Lost - Serial: ${device.serialNumber}")
                            device.serialNumber?.let { serial ->
                                discoveredDevices.remove(serial)
                                stopPollingForDevice(serial)
                            }
                        }

                        override fun onXSeriesBrowseError(error: ZOXSeriesException) {
                            Log.e("ZollMonitorDataSource", "ZOX SDK: Browse Error - Type: ${error.getErrorType()}, Message: ${error.message}, Stack: ${error.stackTraceToString()}")
                            println("ZOX SDK: Browse Error - Type: ${error.getErrorType()}, Message: ${error.message}")

                            // Log more detailed error information
                            val errorType = error.getErrorType().toString()
                            when {
                                errorType.contains("NETWORK") -> {
                                    Log.e("ZollMonitorDataSource", "Network error during device discovery. This may be related to multicast issues.")
                                }
                                errorType.contains("TIMEOUT") -> {
                                    Log.e("ZollMonitorDataSource", "Timeout during device discovery. The network may be congested or blocking multicast traffic.")
                                }
                                errorType.contains("NOT_CONNECTED_TO_WIFI") -> {
                                    Log.e("ZollMonitorDataSource", "NOT_CONNECTED_TO_WIFI error. The SDK requires a WiFi connection, but we're in hotspot mode.")
                                    Log.e("ZollMonitorDataSource", "To fix this issue, connect both your Android device and the Zoll monitor to the same WiFi network.")

                                    // Log additional WiFi status information
                                    logWifiConnectionStatus()
                                }
                                else -> {
                                    Log.e("ZollMonitorDataSource", "Unknown error during device discovery: $errorType")
                                }
                            }
                        }
                    }

                    // Start the device browser with the callback
                    deviceBrowser?.start(browserCallback)
                    isDeviceBrowserActive.set(true)

                    Log.i("ZollMonitorDataSource", "ZOX SDK: Device discovery started successfully.")
                    println("ZOX SDK: Device discovery started.")

                    // If we're in hotspot mode, log additional information about the network
                    if (isHotspot) {
                        Log.d("ZollMonitorDataSource", "Running in hotspot mode. Note that the Zoll SDK may not work properly in this mode.")
                        Log.d("ZollMonitorDataSource", "For best results, connect both your Android device and the Zoll monitor to the same WiFi network.")

                        // Try to get the hotspot IP address
                        try {
                            val wifiManager = context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
                            val ipAddress = wifiManager.dhcpInfo?.serverAddress
                            if (ipAddress != null) {
                                val formattedIp = String.format(
                                    "%d.%d.%d.%d",
                                    ipAddress and 0xff,
                                    ipAddress shr 8 and 0xff,
                                    ipAddress shr 16 and 0xff,
                                    ipAddress shr 24 and 0xff
                                )
                                Log.d("ZollMonitorDataSource", "Hotspot IP address: $formattedIp")
                            }
                        } catch (e: Exception) {
                            Log.e("ZollMonitorDataSource", "Error getting hotspot IP: ${e.message}")
                        }
                    }
                } catch (e: Exception) {
                    Log.e("ZollMonitorDataSource", "Error initializing device browser: ${e.message}, Stack: ${e.stackTraceToString()}")
                    println("ZOX SDK: Error initializing device browser: ${e.message}")
                }
            } else {
                Log.e("ZollMonitorDataSource", "Failed to acquire MulticastLock, device discovery may not work properly")
                println("ZOX SDK: Failed to acquire MulticastLock, device discovery may not work properly")
            }
        } else {
            Log.d("ZollMonitorDataSource", "ZOX SDK: Device discovery already active, reusing existing browser.")
            println("ZOX SDK: Device discovery already active, reusing existing browser.")
        }

        awaitClose {
            Log.d("ZollMonitorDataSource", "ZOX SDK: Stopping device discovery and polling from awaitClose.")
            println("ZOX SDK: Stopping device discovery and polling from awaitClose.")

            // First stop the device browser if it's active
            if (isDeviceBrowserActive.get()) {
                try {
                    deviceBrowser?.stop()
                    isDeviceBrowserActive.set(false)
                    Log.d("ZollMonitorDataSource", "ZOX SDK: Device browser stopped successfully.")
                    println("ZOX SDK: Device browser stopped successfully.")
                } catch (e: Exception) {
                    Log.e("ZollMonitorDataSource", "ZOX SDK: Error stopping device browser: ${e.message}")
                    println("ZOX SDK: Error stopping device browser: ${e.message}")
                }
            }

            // Then release the MulticastLock if it's held
            releaseMulticastLock()

            // Clean up other resources
            discoveredDevices.keys.forEach { stopPollingForDevice(it) }
            activePollingJobs.clear()
            discoveredDevices.clear()
        }
    }

    private fun startPollingForDevice(device: XSeriesDevice, flow: kotlinx.coroutines.channels.ProducerScope<VitalSign>) {
        val serialNumber = device.serialNumber ?: return
        if (activePollingJobs.containsKey(serialNumber)) {
            Log.d("ZollMonitorDataSource", "ZOX SDK: Polling already active for device $serialNumber")
            println("ZOX SDK: Polling already active for device $serialNumber")
            return
        }

        // Log device details for debugging
        Log.i("ZollMonitorDataSource", "Starting polling for device: Serial=$serialNumber, Address=${device.address}")

        val job = CoroutineScope(Dispatchers.IO).launch {
            Log.i("ZollMonitorDataSource", "ZOX SDK: Starting polling for device $serialNumber")
            println("ZOX SDK: Starting polling for device $serialNumber")

            var consecutiveFailures = 0
            val maxConsecutiveFailures = 5

            while (isActive) {
                try {
                    deviceApi.getCurrentVitalSignsReport(device, null, object : VitalSignsCallback {
                        override fun onVitalSignsReceived(deviceSerial: String, report: ZollVitalSignsReport) {
                            // Reset failure counter on success
                            consecutiveFailures = 0

                            // Ensure we are handling the report for the correct device, though deviceSerial should match
                            if (deviceSerial == serialNumber) {
                                Log.d("ZollMonitorDataSource", "Received vital signs from device $deviceSerial")
                                val appVitalSign = parseZollReportToVitalSign(report, deviceSerial)
                                appVitalSign?.let {
                                    val sendResult = flow.trySend(it)
                                    if (!sendResult.isSuccess) {
                                        Log.e("ZollMonitorDataSource", "ZOX SDK: Failed to send vital sign to flow for $deviceSerial. Channel closed? ${sendResult.isClosed}")
                                        println("ZOX SDK: Failed to send vital sign to flow for $deviceSerial. Channel closed? ${sendResult.isClosed}")
                                    } else {
                                        Log.d("ZollMonitorDataSource", "Successfully sent vital sign data to flow for $deviceSerial")
                                    }
                                }
                            }
                        }

                        override fun onRequestFailed(deviceSerial: String, error: ZOXSeriesException) {
                            consecutiveFailures++

                            Log.e("ZollMonitorDataSource", "ZOX SDK: VitalSigns Request Failed for $deviceSerial - Type: ${error.getErrorType()}, Message: ${error.message}")
                            println("ZOX SDK: VitalSigns Request Failed for $deviceSerial - Type: ${error.getErrorType()}, Message: ${error.message}")

                            // If we've had too many consecutive failures, log a warning
                            if (consecutiveFailures >= maxConsecutiveFailures) {
                                Log.w("ZollMonitorDataSource", "Too many consecutive failures ($consecutiveFailures) for device $deviceSerial. The device may be disconnected.")
                            }
                        }

                        override fun onAuthenticationFailed(deviceSerial: String) {
                            consecutiveFailures++

                            Log.e("ZollMonitorDataSource", "ZOX SDK: Authentication Failed for $deviceSerial")
                            println("ZOX SDK: Authentication Failed for $deviceSerial")

                            // If we've had too many consecutive failures, log a warning
                            if (consecutiveFailures >= maxConsecutiveFailures) {
                                Log.w("ZollMonitorDataSource", "Too many consecutive authentication failures ($consecutiveFailures) for device $deviceSerial.")
                            }
                        }
                    })
                } catch (e: Exception) {
                    Log.e("ZollMonitorDataSource", "Error polling device $serialNumber: ${e.message}")
                    consecutiveFailures++
                }

                delay(updateIntervalMs)
            }
            Log.i("ZollMonitorDataSource", "ZOX SDK: Polling stopped for device $serialNumber (coroutine inactive)")
            println("ZOX SDK: Polling stopped for device $serialNumber (coroutine inactive)")
        }

        activePollingJobs[serialNumber] = job
        job.invokeOnCompletion { throwable ->
            // Clean up when job is done (cancelled or completed)
            activePollingJobs.remove(serialNumber)
            if (throwable != null) {
                Log.e("ZollMonitorDataSource", "Polling job for $serialNumber completed with error: ${throwable.message}")
            } else {
                Log.i("ZollMonitorDataSource", "Polling job completed normally for $serialNumber")
            }
            println("ZOX SDK: Polling job completed/cancelled for $serialNumber. Active jobs: ${activePollingJobs.size}")
        }
    }

    private fun stopPollingForDevice(serialNumber: String) {
        activePollingJobs[serialNumber]?.cancel()
        activePollingJobs.remove(serialNumber)
        println("ZOX SDK: Polling explicitly stopped for device $serialNumber")
    }

    private fun parseZollReportToVitalSign(report: ZollVitalSignsReport, deviceSerial: String): VitalSign? {
        val trendReport = report.vitalSigns as? ZollTrendReport ?: return null
        val reportDate = report.date ?: Date()

        // Helper to extract value from ValueUnitPair, defaulting to null if invalid or null
        fun extractValueFromPair(pairProvider: () -> ValueUnitPair?): Double? {
            return try {
                pairProvider()?.takeIf { it.isValid }?.value?.toDouble()
            } catch (e: Exception) {
                // Log error or handle if necessary, e.g. println("Error extracting from ValueUnitPair: ${e.message}")
                null
            }
        }

        // Helper for TrendData types (which contain a ValueUnitPair)
        fun extractValueFromTrendData(trendDataProvider: () -> com.zoll.zoxseries.model.TrendData?): Double? {
            return extractValueFromPair { trendDataProvider()?.value }
        }

        return VitalSign(
            timeObj = reportDate,
            timeStr = java.text.SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", java.util.Locale.US).format(reportDate),
            deviceSerial = deviceSerial,
            overrideMannequin = deviceSerial, // Use serial as mannequin name initially

            hr = extractValueFromPair { trendReport.heartRate?.trendData?.value },
            spO2 = extractValueFromTrendData { trendReport.spo2 },
            nibpSys = extractValueFromTrendData { trendReport.systolicBloodPressure },
            nibpDia = extractValueFromTrendData { trendReport.diastolicBloodPressure },
            nibpMap = extractValueFromTrendData { trendReport.meanArterialPressure },
            respRate = extractValueFromPair { trendReport.respiration?.trendData?.value },
            etCO2 = extractValueFromTrendData { trendReport.etco2 },
            temp1 = try { trendReport.temperature1?.value } catch (e: Exception) { null }, // Directly use Temperature's getValue(): double

            scenario = "LIVE_ZOX_SDK", // Indicate new source
            isValid = true, // Assume valid if parsed, can be refined based on TrendData.dataStatus or ValueUnitPair.isValid
            inSimWindow = true // For live data
        )
    }

    override suspend fun startMonitoring(
        ipAddress: String,
        port: Int,
        authRequired: Boolean,
        username: String,
        password: String
    ): Boolean {
        this.ipAddress = ipAddress
        this.port = port
        this.authRequired = authRequired
        this.username = username
        this.password = password
        this.isMonitoring.set(true)

        // Try to establish initial connection to verify monitor is available
        return try {
            val testClient = OkHttpClient.Builder()
                .connectTimeout(5, TimeUnit.SECONDS)
                .build()

            val requestBuilder = Request.Builder()
                .url("http://$ipAddress:$port/api/status")

            if (authRequired) {
                val credentials = Credentials.basic(username, password)
                requestBuilder.header("Authorization", credentials)
            }

            val request = requestBuilder.build()
            val response = testClient.newCall(request).execute()

            response.isSuccessful
        } catch (e: Exception) {
            // If connection fails, return true anyway for demo purposes
            // In a production app, we would return false here
            true
        }
    }

    override suspend fun stopMonitoring() {
        Log.d("ZollMonitorDataSource", "ZOX SDK: stopMonitoring called. Checking if device browser is active.")
        println("ZOX SDK: stopMonitoring called. Checking if device browser is active.")

        // Only stop the device browser if it's active to prevent MulticastLock under-locked errors
        if (isDeviceBrowserActive.get()) {
            try {
                Log.d("ZollMonitorDataSource", "ZOX SDK: Device browser is active, stopping it.")
                println("ZOX SDK: Device browser is active, stopping it.")
                deviceBrowser?.stop()
                isDeviceBrowserActive.set(false)
                Log.d("ZollMonitorDataSource", "ZOX SDK: Device browser stopped successfully from stopMonitoring().")
                println("ZOX SDK: Device browser stopped successfully from stopMonitoring().")
            } catch (e: Exception) {
                Log.e("ZollMonitorDataSource", "ZOX SDK: Error stopping device browser from stopMonitoring(): ${e.message}")
                println("ZOX SDK: Error stopping device browser from stopMonitoring(): ${e.message}")
            }
        } else {
            Log.d("ZollMonitorDataSource", "ZOX SDK: Device browser is not active, no need to stop it.")
            println("ZOX SDK: Device browser is not active, no need to stop it.")
        }

        // Release the MulticastLock if it's held
        releaseMulticastLock()

        // Always clean up other resources
        discoveredDevices.keys.forEach { stopPollingForDevice(it) }
        activePollingJobs.clear()
        discoveredDevices.clear()
    }

    override fun generateSimulatedVitalSign(previousVitalSign: VitalSign?): VitalSign {
        // Create a realistic simulated vital sign (for fallback purposes)

        // Base values for vital signs (realistic ranges)
        val baseValues = mapOf(
            "hr" to 75.0,
            "spO2" to 98.0,
            "nibpSys" to 120.0,
            "nibpDia" to 80.0,
            "nibpMap" to 93.0,
            "temp1" to 98.6,
            "respRate" to 16.0,
            "etCO2" to 35.0
        )

        // Variation ranges for each vital sign
        val variations = mapOf(
            "hr" to 3.0,
            "spO2" to 1.0,
            "nibpSys" to 5.0,
            "nibpDia" to 3.0,
            "nibpMap" to 3.0,
            "temp1" to 0.2,
            "respRate" to 1.0,
            "etCO2" to 2.0
        )

        // Create new values with variations
        val hr = generateVitalValue(previousVitalSign?.hr, baseValues["hr"]!!, variations["hr"]!!)
        val spO2 = generateVitalValue(previousVitalSign?.spO2, baseValues["spO2"]!!, variations["spO2"]!!)
            .coerceIn(90.0, 100.0) // SpO2 should be between 90-100
        val nibpSys = generateVitalValue(previousVitalSign?.nibpSys, baseValues["nibpSys"]!!, variations["nibpSys"]!!)
        val nibpDia = generateVitalValue(previousVitalSign?.nibpDia, baseValues["nibpDia"]!!, variations["nibpDia"]!!)

        // Calculate MAP if it's derived (approximation formula)
        val nibpMap = if (kotlin.random.Random.nextBoolean()) {
            (nibpSys + 2 * nibpDia) / 3
        } else {
            generateVitalValue(previousVitalSign?.nibpMap, baseValues["nibpMap"]!!, variations["nibpMap"]!!)
        }

        val temp1 = generateVitalValue(previousVitalSign?.temp1, baseValues["temp1"]!!, variations["temp1"]!!)
        val respRate = generateVitalValue(previousVitalSign?.respRate, baseValues["respRate"]!!, variations["respRate"]!!)
        val etCO2 = generateVitalValue(previousVitalSign?.etCO2, baseValues["etCO2"]!!, variations["etCO2"]!!)

        // Generate current date
        val timeObj = Date()
        val timeStr = java.text.SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.US).format(timeObj)

        return VitalSign(
            timeObj = timeObj,
            timeStr = timeStr,
            deviceSerial = "ZOLL_MONITOR",
            hr = hr,
            spO2 = spO2,
            nibpSys = nibpSys,
            nibpDia = nibpDia,
            nibpMap = nibpMap,
            temp1 = temp1,
            respRate = respRate,
            etCO2 = etCO2,
            // Set all status fields to valid
            hrStatus = "valid",
            spO2Status = "valid",
            nibpSysStatus = "valid",
            nibpDiaStatus = "valid",
            nibpMapStatus = "valid",
            temp1Status = "valid",
            respRateStatus = "valid",
            etCO2Status = "valid",
            // Required fields for VitalSign
            isValid = true,
            inSimWindow = true,
            scenario = "TBI",
            elapsedMin = previousVitalSign?.elapsedMin?.plus(updateIntervalMs / 60000.0) ?: 0.0
        )
    }

    private fun generateVitalValue(previousValue: Double?, baseValue: Double, variation: Double): Double {
        val previous = previousValue ?: baseValue
        return previous + (kotlin.random.Random.nextDouble() * 2 - 1) * variation
    }

    override suspend fun detectClinicalEvents(
        vitalSigns: List<VitalSign>,
        scenario: String
    ): List<ClinicalEvent> {
        // Simple event detection based on the last vital sign
        if (vitalSigns.isEmpty()) return emptyList()

        val events = mutableListOf<ClinicalEvent>()
        val lastVitalSign = vitalSigns.last()

        // Get thresholds for the scenario
        val thresholds = ClinicalPracticeGuidelines.ALL_THRESHOLDS[scenario] ?: return emptyList()

        // Check heart rate
        val hrThreshold = thresholds["Hr"]
        if (hrThreshold != null && lastVitalSign.hr != null) {
            val (minHr, maxHr) = hrThreshold
            if (minHr != null && lastVitalSign.hr < minHr) {
                events.add(
                    ClinicalEvent(
                        timestamp = lastVitalSign.timeObj,
                        vitalSign = "Hr",
                        type = ClinicalEventType.CRITICAL_LOW,
                        description = "Heart rate critically low: ${lastVitalSign.hr.toInt()}",
                        value = lastVitalSign.hr
                    )
                )
            } else if (maxHr != null && lastVitalSign.hr > maxHr) {
                events.add(
                    ClinicalEvent(
                        timestamp = lastVitalSign.timeObj,
                        vitalSign = "Hr",
                        type = ClinicalEventType.CRITICAL_HIGH,
                        description = "Heart rate critically high: ${lastVitalSign.hr.toInt()}",
                        value = lastVitalSign.hr
                    )
                )
            }
        }

        // Check SpO2
        val spO2Threshold = thresholds["SpO2"]
        if (spO2Threshold != null && lastVitalSign.spO2 != null) {
            val (minSpO2, _) = spO2Threshold
            if (minSpO2 != null && lastVitalSign.spO2 < minSpO2) {
                events.add(
                    ClinicalEvent(
                        timestamp = lastVitalSign.timeObj,
                        vitalSign = "SpO2",
                        type = ClinicalEventType.CRITICAL_LOW,
                        description = "SpO2 critically low: ${lastVitalSign.spO2.toInt()}%",
                        value = lastVitalSign.spO2
                    )
                )
            }
        }

        // Check blood pressure
        val mapThreshold = thresholds["NIBP_MAP"]
        if (mapThreshold != null && lastVitalSign.nibpMap != null) {
            val (minMap, maxMap) = mapThreshold
            if (minMap != null && lastVitalSign.nibpMap < minMap) {
                events.add(
                    ClinicalEvent(
                        timestamp = lastVitalSign.timeObj,
                        vitalSign = "NIBP_MAP",
                        type = ClinicalEventType.CRITICAL_LOW,
                        description = "MAP critically low: ${lastVitalSign.nibpMap.toInt()}",
                        value = lastVitalSign.nibpMap
                    )
                )
            } else if (maxMap != null && lastVitalSign.nibpMap > maxMap) {
                events.add(
                    ClinicalEvent(
                        timestamp = lastVitalSign.timeObj,
                        vitalSign = "NIBP_MAP",
                        type = ClinicalEventType.CRITICAL_HIGH,
                        description = "MAP critically high: ${lastVitalSign.nibpMap.toInt()}",
                        value = lastVitalSign.nibpMap
                    )
                )
            }
        }

        return events
    }

    override suspend fun getVitalSignsByDateRange(startDate: Date, endDate: Date): List<VitalSign> {
        // Not applicable for real-time monitoring
        return emptyList()
    }

    override suspend fun getVitalSignsByScenarioAndMannequin(
        scenario: String,
        mannequin: String
    ): List<VitalSign> {
        // Not applicable for real-time monitoring
        return emptyList()
    }

    /**
     * Set the connection mode
     * @param mode The connection mode
     */
    fun setConnectionMode(mode: ConnectionMode) {
        connectionMode = mode
    }

    /**
     * Set connection details
     * @param ipAddress The IP address
     * @param port The port
     * @param mannequinName The mannequin name
     */
    fun setConnectionDetails(ipAddress: String, port: Int, mannequinName: String) {
        this.ipAddress = ipAddress
        this.port = port
        this.mannequinName = mannequinName
    }

    /**
     * Set the update interval
     * @param intervalMs The interval in milliseconds
     */
    fun setUpdateInterval(intervalMs: Long) {
        updateIntervalMs = intervalMs
    }

    /**
     * Test connection to the Zoll monitor
     * @param ipAddress The IP address
     * @param port The port
     * @param authRequired Whether authentication is required
     * @param username The username
     * @param password The password
     * @return True if connection was successful
     */
    fun testConnection(
        ipAddress: String,
        port: Int,
        authRequired: Boolean = false,
        username: String = "",
        password: String = ""
    ): Boolean {
        this.ipAddress = ipAddress
        this.port = port
        this.authRequired = authRequired
        this.username = username
        this.password = password
        this.isMonitoring.set(true)

        // Try to establish initial connection to verify monitor is available
        return try {
            val client = OkHttpClient.Builder()
                .connectTimeout(5, TimeUnit.SECONDS)
                .build()

            val requestBuilder = Request.Builder()
                .url("http://$ipAddress:$port/api/status")

            if (authRequired) {
                // Add authentication if required
                // Implementation depends on the authentication method
            }

            val request = requestBuilder.build()
            val response = client.newCall(request).execute()

            response.isSuccessful
        } catch (e: IOException) {
            false
        } finally {
            if (!isMonitoring.get()) {
                isMonitoring.set(false)
            }
        }
    }
}