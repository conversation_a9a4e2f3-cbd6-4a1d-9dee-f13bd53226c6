package com.example.myapplication.data.db

import android.content.Context
import android.util.Log
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import com.example.myapplication.util.CrashLogger

/**
 * Room database implementation for vital signs
 */
@Database(entities = [VitalSignEntity::class], version = 1, exportSchema = false)
@TypeConverters(Converters::class)
abstract class VitalSignsDatabase : RoomDatabase() {
    /**
     * Get the DAO for vital sign operations
     */
    abstract fun vitalSignDao(): VitalSignDao
    
    companion object {
        @Volatile
        private var INSTANCE: VitalSignsDatabase? = null
        private const val TAG = "VitalSignsDatabase"
        
        /**
         * Get database instance, creating it if necessary
         * @throws Exception if database creation fails
         */
        fun getDatabase(context: Context): VitalSignsDatabase {
            // if the INSTANCE is not null, then return it,
            // if it is, then create the database
            return INSTANCE ?: synchronized(this) {
                try {
                    val instance = Room.databaseBuilder(
                        context.applicationContext,
                        VitalSignsDatabase::class.java,
                        "vital_signs_database"
                    )
                        .fallbackToDestructiveMigration() // For simplicity during development
                        .build()
                    INSTANCE = instance
                    instance
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to create database instance", e)
                    CrashLogger.logError(TAG, "Failed to create database instance", e)
                    throw e
                }
            }
        }
    }
} 