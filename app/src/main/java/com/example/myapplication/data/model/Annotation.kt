package com.example.myapplication.data.model

import java.util.Date
import java.util.UUID

/**
 * Represents an annotation or note added by an instructor during monitoring
 */
data class Annotation(
    val id: String = UUID.randomUUID().toString(),
    val timestamp: Date = Date(),
    val text: String,
    val vitalSign: String? = null,
    val mannequin: String? = null,
    val type: AnnotationType = AnnotationType.NOTE,
    val relatedNonComplianceEventId: String? = null
)

/**
 * Types of annotations that can be added
 */
enum class AnnotationType {
    NOTE,           // General note
    EVENT,          // Significant event
    INTERVENTION,   // Clinical intervention
    CRITICAL_ALERT  // Critical situation alert
}

/**
 * Represents an active non-compliance event that is currently ongoing
 */
data class ActiveNonComplianceEvent(
    val id: String = UUID.randomUUID().toString(),
    val startTime: Date,
    val vitalSign: String,
    val vitalSignKey: String, // The key used in vitalSignStatuses map (e.g., "hr", "spo2")
    val currentValue: Double,
    val minThreshold: Double?,
    val maxThreshold: Double?,
    val values: MutableList<Double> = mutableListOf(),
    val mannequin: String? = null,
    val scenario: String? = null,
    val status: VitalSignStatus
) {
    // Duration in seconds (for real-time display)
    fun getDurationSeconds(currentTime: Date): Long {
        return (currentTime.time - startTime.time) / 1000
    }
    
    // Convert to a NonComplianceEvent when the event ends
    fun toNonComplianceEvent(endTime: Date): NonComplianceEvent {
        return NonComplianceEvent(
            startTime = startTime,
            endTime = endTime,
            vitalSign = vitalSign,
            minThreshold = minThreshold,
            maxThreshold = maxThreshold,
            values = values.toList(),
            mannequin = mannequin,
            scenario = scenario
        )
    }
    
    // Get threshold as a formatted string
    fun getThresholdString(): String {
        return when {
            minThreshold != null && maxThreshold != null -> "$minThreshold - $maxThreshold"
            minThreshold != null -> "≥ $minThreshold"
            maxThreshold != null -> "≤ $maxThreshold"
            else -> "Unknown"
        }
    }
}
