package com.example.myapplication.data.util

import com.example.myapplication.data.model.MannequinConfig
import com.example.myapplication.data.model.VitalSign
import com.example.myapplication.data.SimulationDurations
import com.example.myapplication.data.Constants
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.concurrent.TimeUnit

/**
 * Utility for detecting the precise start and end times of simulations
 * by analyzing both schedule-based expectations and actual monitor data.
 */
object SimulationTimeDetector {
    
    // Setup buffer time in minutes
    private const val SETUP_BUFFER_MINUTES = 10
    
    // Thresholds for detecting simulation boundaries           
    private const val MIN_SUSTAINED_VALID_DATA_SECONDS = 60 // 1 minute of consistent data to confirm start
    private const val MIN_SUSTAINED_INACTIVE_MINUTES = 3 // 3 minutes of inactive data to confirm end
    
    /**
     * Combined method to identify which simulation is being run and its timeframe
     * Uses schedule-based detection first, then falls back to data pattern analysis
     * 
     * @param vitalSigns List of vital signs
     * @param date The date of the simulation
     * @param annotationEvents Optional list of annotation events 
     * @return Pair of (simulation name, timeframe pair) or null if detection failed
     */
    fun identifySimulationAndTimeframe(
        vitalSigns: List<VitalSign>,
        date: Date,
        annotationEvents: List<Map<String, Any>>? = null
    ): Pair<String, Pair<Date, Date>>? {
        if (vitalSigns.isEmpty()) {
            return null
        }
        
        // Sort vital signs by time
        val sortedVitals = vitalSigns.sortedBy { it.timeObj }
        
        // Try to identify from schedule first
        val simFromSchedule = identifySimulationFromSchedule(date)
        
        if (simFromSchedule != null) {
            // We have a schedule-based identification, verify with timeframe
            val timeframe = detectSimulationTimeframe(sortedVitals, simFromSchedule, date, annotationEvents)
            
            if (timeframe != null) {
                // Double-check the timeframe against duration expectations
                val durationMinutes = TimeUnit.MILLISECONDS.toMinutes(timeframe.second.time - timeframe.first.time)
                val expectedMinDuration = when(simFromSchedule) {
                    "Sim1" -> 43 // 45 min - 2 min buffer
                    "Sim2" -> 33 // 35 min - 2 min buffer
                    "Sim3" -> 48 // 50 min - 2 min buffer
                    "Sim4" -> 53 // 55 min - 2 min buffer
                    "Sim5" -> 38 // 40 min - 2 min buffer
                    // Training simulations have more flexible duration ranges
                    "TrainingSim3" -> 30 // 30 min minimum - can be shorter than regular Sim3
                    "TrainingSim5" -> 30 // 30 min minimum - can be shorter than regular Sim5
                    else -> 30
                }
                val expectedMaxDuration = when(simFromSchedule) {
                    "Sim1" -> 47 // 45 min + 2 min buffer
                    "Sim2" -> 37 // 35 min + 2 min buffer
                    "Sim3" -> 52 // 50 min + 2 min buffer
                    "Sim4" -> 57 // 55 min + 2 min buffer
                    "Sim5" -> 42 // 40 min + 2 min buffer
                    // Training simulations have more flexible duration ranges
                    "TrainingSim3" -> 60 // Allow up to 60 minutes for training simulations
                    "TrainingSim5" -> 60 // Allow up to 60 minutes for training simulations
                    else -> 60
                }
                
                if (durationMinutes in expectedMinDuration..expectedMaxDuration) {
                    // Timeframe matches expected duration, return result
                    return Pair(simFromSchedule, timeframe)
                }
                
                // New code: Check if there's continuous valid data after the expected end time
                if (durationMinutes > expectedMaxDuration) {
                    val (startTime, endTime) = timeframe
                    
                    // Calculate expected end time based on start + max duration
                    val expectedEndTime = Calendar.getInstance().apply {
                        time = startTime
                        add(Calendar.MINUTE, expectedMaxDuration)
                    }.time
                    
                    // Check if we have valid data for at least 2 minutes after expected end
                    val validDataAfterExpectedEnd = sortedVitals
                        .filter { it.timeObj > expectedEndTime && it.timeObj < endTime }
                        .groupBy { 
                            // Group by minute to check for continuous data
                            val cal = Calendar.getInstance().apply { time = it.timeObj }
                            cal.get(Calendar.MINUTE)
                        }
                        .any { (_, vitalsInMinute) ->
                            // Check for valid major vitals in this minute
                            vitalsInMinute.any { vital ->
                                val hasValidHR = vital.hr != null && vital.hrStatus == "valid"
                                val hasValidSpO2 = vital.spO2 != null && vital.spO2Status == "valid"
                                
                                // If either HR or SpO2 is valid, consider this minute valid
                                hasValidHR || hasValidSpO2
                            }
                        }
                    
                    if (validDataAfterExpectedEnd) {
                        // If we have valid data continuing after expected end, accept the extended timeframe
                        return Pair(simFromSchedule, timeframe)
                    }
                }
            }
        }
        
        // Schedule-based identification failed or produced an invalid timeframe
        // Fall back to data pattern analysis
        val simFromData = identifySimulationFromData(sortedVitals, annotationEvents)
        
        if (simFromData != null) {
            // We identified the simulation from data patterns
            // Now get the timeframe
            val timeframe = detectTimeframeFromDataOnly(sortedVitals, annotationEvents)
            
            if (timeframe != null) {
                return Pair(simFromData, timeframe)
            }
        }
        
        return null
    }
    
    /**
     * Identifies which simulation should be running based on date and time
     * using the simulation schedule
     * 
     * @param date The date and time to check
     * @return The simulation name (Sim1, Sim2, etc.) or null if no match found
     */
    fun identifySimulationFromSchedule(date: Date): String? {
        // Extract course info from date
        val calendar = Calendar.getInstance().apply { time = date }
        val month = calendar.get(Calendar.MONTH) + 1 // Calendar months are 0-based
        val day = calendar.get(Calendar.DAY_OF_MONTH)
        val dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK)
        
        // Check for training simulations based on day of week
        // Training Sim 3 is on Wednesday (Calendar.WEDNESDAY = 4)
        // Training Sim 5 is on Friday (Calendar.FRIDAY = 6)
        val hourOfDay = calendar.get(Calendar.HOUR_OF_DAY)
        val minute = calendar.get(Calendar.MINUTE)
        val timeInMinutes = hourOfDay * 60 + minute
        
        // Check if time falls within training simulation hours (typically 9:00-16:00)
        val isTrainingHours = timeInMinutes in 540..960 // 9:00 (540 mins) to 16:00 (960 mins)
        
        if (isTrainingHours) {
            if (dayOfWeek == Calendar.WEDNESDAY) {
                // Check if time falls within one of the TrainingSim3 blocks
                for (timeBlock in Constants.SIM_SCHEDULES["TrainingSim3"] ?: emptyList()) {
                    val startTime = timeBlock.first.split(":")
                    val endTime = timeBlock.second.split(":")
                    
                    if (startTime.size == 2 && endTime.size == 2) {
                        val startMinutes = startTime[0].toInt() * 60 + startTime[1].toInt()
                        val endMinutes = endTime[0].toInt() * 60 + endTime[1].toInt()
                        
                        if (timeInMinutes in startMinutes..endMinutes) {
                            return "TrainingSim3"
                        }
                    }
                }
            } else if (dayOfWeek == Calendar.FRIDAY) {
                // Check if time falls within one of the TrainingSim5 blocks
                for (timeBlock in Constants.SIM_SCHEDULES["TrainingSim5"] ?: emptyList()) {
                    val startTime = timeBlock.first.split(":")
                    val endTime = timeBlock.second.split(":")
                    
                    if (startTime.size == 2 && endTime.size == 2) {
                        val startMinutes = startTime[0].toInt() * 60 + startTime[1].toInt()
                        val endMinutes = endTime[0].toInt() * 60 + endTime[1].toInt()
                        
                        if (timeInMinutes in startMinutes..endMinutes) {
                            return "TrainingSim5"
                        }
                    }
                }
            }
        }
        
        // Find which course this date belongs to
        val dateStr = String.format("%02d/%02d", month, day)
        val courseInfo = Constants.COURSE_DATE_RANGES.find { (startDate, endDate, _) ->
            isDateInRange(dateStr, startDate, endDate)
        }
        
        if (courseInfo == null) {
            return null
        }
        
        // Find the day offset within the course
        val (startDate, _, courseLabel) = courseInfo
        val courseStartDate = parseCourseDate(startDate)
        
        if (courseStartDate != null) {
            val courseStartCalendar = Calendar.getInstance().apply { 
                time = courseStartDate
                set(Calendar.YEAR, calendar.get(Calendar.YEAR))
                // If the current date month is earlier than the course start month,
                // it's likely in the next year (December -> January transition)
                if (month < getMonthFromString(startDate) && month <= 3) {
                    add(Calendar.YEAR, 1)
                }
            }
            
            // Calculate days since course start
            val dayOffset = ((calendar.timeInMillis - courseStartCalendar.timeInMillis) / 
                            (24 * 60 * 60 * 1000)).toInt()
            
            // Map day offset to simulation
            return Constants.DAY_OFFSET_TO_SIM[dayOffset]
        }
        
        // Now check if we can determine the simulation from the time of day
        // Try each simulation's schedule to see if the current time falls within it
        for ((simName, schedule) in Constants.SIM_SCHEDULES) {
            // Skip training sims as we already checked them
            if (simName.startsWith("Training")) continue
            
            for (timeBlock in schedule) {
                val startTime = timeBlock.first.split(":")
                val endTime = timeBlock.second.split(":")
                
                if (startTime.size == 2 && endTime.size == 2) {
                    val startMinutes = startTime[0].toInt() * 60 + startTime[1].toInt()
                    val endMinutes = endTime[0].toInt() * 60 + endTime[1].toInt()
                    
                    if (timeInMinutes in startMinutes..endMinutes) {
                        return simName
                    }
                }
            }
        }
        
        return null
    }
    
    /**
     * Helper function to check if a date is within a range
     */
    private fun isDateInRange(dateStr: String, startDate: String, endDate: String): Boolean {
        val month = dateStr.split("/")[0].toInt()
        val day = dateStr.split("/")[1].toInt()
        
        val startMonth = startDate.split("/")[0].toInt()
        val startDay = startDate.split("/")[1].toInt()
        
        val endMonth = endDate.split("/")[0].toInt()
        val endDay = endDate.split("/")[1].toInt()
        
        // Handle the case where the range spans the end of the year
        return if (startMonth > endMonth) {
            // Range crosses year boundary (e.g., Dec-Jan)
            (month > startMonth || (month == startMonth && day >= startDay)) || 
            (month < endMonth || (month == endMonth && day <= endDay))
        } else if (startMonth == endMonth) {
            // Same month
            month == startMonth && day >= startDay && day <= endDay
        } else {
            // Normal case
            (month > startMonth || (month == startMonth && day >= startDay)) && 
            (month < endMonth || (month == endMonth && day <= endDay))
        }
    }
    
    /**
     * Helper function to parse course date string (MM/DD) into a Date
     */
    private fun parseCourseDate(dateStr: String): Date? {
        val parts = dateStr.split("/")
        if (parts.size != 2) return null
        
        val month = parts[0].toInt() - 1 // Calendar months are 0-based
        val day = parts[1].toInt()
        
        // Default to current year
        val calendar = Calendar.getInstance()
        calendar.set(Calendar.MONTH, month)
        calendar.set(Calendar.DAY_OF_MONTH, day)
        
        return calendar.time
    }
    
    /**
     * Helper function to extract month from MM/DD string
     */
    private fun getMonthFromString(dateStr: String): Int {
        val parts = dateStr.split("/")
        return if (parts.size == 2) parts[0].toInt() else 1
    }
    
    /**
     * Detects the exact simulation timeframe from a list of vital signs
     * 
     * @param vitalSigns List of vital signs sorted by time
     * @param simName Simulation name (Sim1, Sim2, etc.)
     * @param date The date of the simulation (for schedule lookup)
     * @param annotationEvents Optional list of annotation events to help detect power-off moments
     * @return Pair of start and end times, or null if detection failed
     */
    fun detectSimulationTimeframe(
        vitalSigns: List<VitalSign>,
        simName: String,
        date: Date,
        annotationEvents: List<Map<String, Any>>? = null
    ): Pair<Date, Date>? {
        if (vitalSigns.isEmpty() || !SimulationDurations.SIM_DURATIONS.containsKey(simName)) {
            return null
        }
        
        // Get scheduled window
        val scheduledWindow = getScheduledSimulationWindow(simName, date)
        if (scheduledWindow == null) {
            // If we can't determine scheduled window, use broader timeframe based on the data
            return detectTimeframeFromDataOnly(vitalSigns, annotationEvents)
        }
        
        val (scheduledStart, scheduledEnd) = scheduledWindow
        
        // Filter vital signs to around the scheduled window (add buffer before and after)
        val startBuffer = Calendar.getInstance().apply { 
            time = scheduledStart
            add(Calendar.MINUTE, -5) // 5 minutes before scheduled start
        }.time
        
        val endBuffer = Calendar.getInstance().apply { 
            time = scheduledEnd
            add(Calendar.MINUTE, 15) // 15 minutes after scheduled end
        }.time
        
        val relevantVitals = vitalSigns.filter { 
            it.timeObj in startBuffer..endBuffer 
        }
        
        if (relevantVitals.isEmpty()) {
            // Fall back to scheduled times if no relevant data
            return scheduledWindow
        }
        
        // Detect actual start time (with annotation events)
        val actualStart = detectStartTime(relevantVitals, scheduledStart, annotationEvents)
        
        // Detect actual end time (with annotation events)
        val actualEnd = detectEndTime(relevantVitals, actualStart, scheduledEnd, annotationEvents)
        
        // GENERAL FIX: Check if there's continuous valid data after the detected end time
        val dataAfterEnd = vitalSigns.filter { 
            it.timeObj > actualEnd && 
            it.timeObj < Calendar.getInstance().apply { 
                time = actualEnd
                add(Calendar.MINUTE, 10) // Look 10 minutes ahead
            }.time
        }
        
        // If we have valid HR or SpO2 readings after the detected end time, extend the simulation
        val hasValidDataAfter = dataAfterEnd.any { vital ->
            (vital.hr != null && vital.hrStatus == "valid") ||
            (vital.spO2 != null && vital.spO2Status == "valid")
        }
        
        if (hasValidDataAfter) {
            // Find the last valid data point with HR or SpO2
            val lastValidData = vitalSigns
                .filter { 
                    it.timeObj > actualEnd && 
                    ((it.hr != null && it.hrStatus == "valid") || 
                     (it.spO2 != null && it.spO2Status == "valid"))
                }
                .maxByOrNull { it.timeObj }
            
            if (lastValidData != null) {
                // Use the last valid data point as the end time
                return Pair(actualStart, lastValidData.timeObj)
            }
        }
        
        return Pair(actualStart, actualEnd)
    }
    
    /**
     * Helper function to check if a date matches a specific time (HH:MM:SS)
     */
    private fun isSpecificTimepoint(date: Date, hours: Int, minutes: Int, seconds: Int): Boolean {
        val cal = Calendar.getInstance().apply { time = date }
        return cal.get(Calendar.HOUR_OF_DAY) == hours && 
               cal.get(Calendar.MINUTE) == minutes && 
               cal.get(Calendar.SECOND) == seconds
    }
    
    /**
     * Identifies which simulation is being run based on mannequin combinations and vital sign patterns
     * when the date doesn't fall within a scheduled window.
     *
     * @param vitalSigns List of vital signs sorted by time
     * @param annotationEvents List of annotation events (optional)
     * @return The identified simulation name (Sim1, Sim2, etc.) or null if identification failed
     */
    fun identifySimulationFromData(
        vitalSigns: List<VitalSign>,
        annotationEvents: List<Map<String, Any>>? = null
    ): String? {
        if (vitalSigns.isEmpty()) {
            return null
        }
        
        // Sort vital signs by time
        val sortedVitals = vitalSigns.sortedBy { it.timeObj }
        
        // Get the timeframe of the simulation
        val simulationTimeframe = detectTimeframeFromDataOnly(sortedVitals, annotationEvents) ?: return null
        val (startTime, endTime) = simulationTimeframe
        
        // Filter to vital signs within the detected timeframe
        val relevantVitals = sortedVitals.filter { 
            it.timeObj in startTime..endTime 
        }
        
        // Check simulation duration
        val durationMinutes = TimeUnit.MILLISECONDS.toMinutes(endTime.time - startTime.time)
        
        // Step 1: Check mannequin combinations (unique device serials)
        val uniqueSerials = relevantVitals
            .mapNotNull { it.deviceSerial }
            .toSet()
            
        val mannequins = uniqueSerials
            .mapNotNull { Constants.MANNEQUIN_MAP[it] }
            .toSet()
            
        // Filter based on mannequin combinations
        if (mannequins.size == 3 && 
            mannequins.containsAll(setOf("Freddy", "Oscar", "Matt"))) {
            // If duration matches Sim5 expectations
            if (durationMinutes in 38..42) {
                return "Sim5"
            }
        } else if (mannequins.size == 2 && 
            mannequins.containsAll(setOf("Freddy", "Oscar")) && 
            !mannequins.contains("Dave") && 
            !mannequins.contains("Chuck") && 
            !mannequins.contains("Matt")) {
            
            // Check if it's TrainingSim3 (two DCR patients - Oscar and Freddy)
            // Look for DCR-specific patterns for both mannequins
            if (isDCRSimulation(relevantVitals) && durationMinutes in 43..47) {
                return "TrainingSim3"
            }
            
            // Otherwise, assume it's regular Sim2
            if (durationMinutes in 33..37) {
                return "Sim2"
            }
        } else if (mannequins.size == 2 && 
            mannequins.containsAll(setOf("Dave", "Chuck")) && 
            !mannequins.contains("Freddy") && 
            !mannequins.contains("Oscar") && 
            !mannequins.contains("Matt")) {
            
            // Check for TrainingSim5 (Dave DCR and Chuck fever/seizure/intubation)
            if (hasDaveDCRAndChuckFeverPattern(relevantVitals) && durationMinutes in 43..47) {
                return "TrainingSim5"
            }
            
            // Need to identify among Sim1, Sim3, or Sim4 using vital sign patterns
            return identifyDaveChuckSimulation(relevantVitals, durationMinutes)
        }
        
        // Step 3: If we have only one serial or still ambiguous, use vital sign patterns
        return identifySimulationFromVitalPatterns(relevantVitals, durationMinutes, annotationEvents)
    }
    
    /**
     * Checks if a simulation shows patterns consistent with DCR scenarios
     * for both mannequins (used for Training Sim 3)
     */
    private fun isDCRSimulation(vitalSigns: List<VitalSign>): Boolean {
        if (vitalSigns.isEmpty()) return false
        
        // Group vital signs by mannequin
        val mannequinVitals = vitalSigns
            .filter { it.deviceSerial != null }
            .groupBy { Constants.MANNEQUIN_MAP[it.deviceSerial] }
        
        // We need data for both Oscar and Freddy
        if (!mannequinVitals.containsKey("Oscar") || !mannequinVitals.containsKey("Freddy")) {
            return false
        }
        
        // Check for DCR indicators in both mannequins:
        // 1. Temperature < 96°F at some point (hypothermia)
        // 2. Early hypotension (MAP < 60)
        val mannequinsWithDCRIndicators = mutableSetOf<String>()
        
        for ((mannequin, vitals) in mannequinVitals) {
            if (mannequin == null) continue
            
            val hasHypothermia = vitals.any { 
                it.temp1 != null && it.temp1!! < 96.0 && it.temp1Status == "valid" 
            }
            
            val hasEarlyHypotension = vitals.take(vitals.size / 3).any {
                it.nibpMap != null && it.nibpMap!! < 60 && it.nibpMapStatus == "valid"
            }
            
            if (hasHypothermia || hasEarlyHypotension) {
                mannequinsWithDCRIndicators.add(mannequin)
            }
        }
        
        // Return true if both Oscar and Freddy show DCR indicators
        return mannequinsWithDCRIndicators.containsAll(setOf("Oscar", "Freddy"))
    }
    
    /**
     * Checks if a simulation shows patterns consistent with 
     * Dave having DCR and Chuck having fever/seizure/intubation (Training Sim 5)
     */
    private fun hasDaveDCRAndChuckFeverPattern(vitalSigns: List<VitalSign>): Boolean {
        if (vitalSigns.isEmpty()) return false
        
        // Group vital signs by mannequin
        val mannequinVitals = vitalSigns
            .filter { it.deviceSerial != null }
            .groupBy { Constants.MANNEQUIN_MAP[it.deviceSerial] }
        
        // We need data for both Dave and Chuck
        if (!mannequinVitals.containsKey("Dave") || !mannequinVitals.containsKey("Chuck")) {
            return false
        }
        
        // Check Dave for DCR indicators:
        // 1. Temperature < 96°F at some point (hypothermia)
        // 2. Early hypotension (MAP < 60)
        val daveVitals = mannequinVitals["Dave"] ?: return false
        val daveDCRIndicators = daveVitals.any { 
            (it.temp1 != null && it.temp1!! < 96.0 && it.temp1Status == "valid") ||
            (it.nibpMap != null && it.nibpMap!! < 60 && it.nibpMapStatus == "valid")
        }
        
        // Check Chuck for fever/seizure indicators:
        // 1. Temperature > 101°F at some point (fever)
        // 2. Periods of tachycardia (HR > 110)
        // 3. Irregular respiratory patterns
        val chuckVitals = mannequinVitals["Chuck"] ?: return false
        val chuckHasFever = chuckVitals.any {
            it.temp1 != null && it.temp1!! > 101.0 && it.temp1Status == "valid"
        }
        
        val chuckHasTachycardia = chuckVitals.any {
            it.hr != null && it.hr!! > 110 && it.hrStatus == "valid"
        }
        
        // Irregular respiratory patterns - look for significant variation
        val respiratoryRates = chuckVitals
            .filter { it.respRate != null && it.respRateStatus == "valid" }
            .map { it.respRate!! }
        
        val hasRespiratoryVariability = if (respiratoryRates.size >= 5) {
            val minRate = respiratoryRates.minOrNull() ?: 0.0
            val maxRate = respiratoryRates.maxOrNull() ?: 0.0
            maxRate - minRate >= 8.0 // Significant variation in respiratory rate
        } else {
            false
        }
        
        // Return true if both mannequins show their specific pattern indicators
        return daveDCRIndicators && (chuckHasFever || chuckHasTachycardia || hasRespiratoryVariability)
    }
    
    /**
     * Identifies which simulation among Sim1, Sim3, and Sim4 is being run
     * based on vital sign patterns for Dave and Chuck mannequins
     */
    private fun identifyDaveChuckSimulation(
        vitalSigns: List<VitalSign>,
        durationMinutes: Long
    ): String {
        // Check for TrainingSim5 (Dave DCR and Chuck fever/seizure/intubation)
        if (hasDaveDCRAndChuckFeverPattern(vitalSigns)) {
            // More flexible duration check for training simulations (30-60 minutes)
            if (durationMinutes in 30..60) {
                return "TrainingSim5"
            }
        }
        
        // Look for HR flat-line followed by ROSC pattern (Sim3 - Chuck's V-tach and shock)
        if (hasHRFlatLineROSCPattern(vitalSigns)) {
            // Confirm with duration check
            return if (durationMinutes in 48..52) "Sim3" else "Sim1"
        }
        
        // Look for long hypertension plateau + spike/drop pattern (Sim4)
        if (hasHypertensionPatternForSim4(vitalSigns)) {
            // Confirm with duration check
            return if (durationMinutes in 53..57) "Sim4" else "Sim1"
        }
        
        // Check for late hypotension (MAP below 55 mmHg in last 10 minutes) typical of Sim1
        if (hasLateHypotensionForSim1(vitalSigns)) {
            // Confirm with duration check
            return if (durationMinutes in 43..47) "Sim1" else "Sim3"
        }
        
        // Default to Sim1 if no specific patterns detected
        return if (durationMinutes in 43..47) {
            "Sim1"
        } else if (durationMinutes in 48..52) {
            "Sim3"
        } else if (durationMinutes in 53..57) {
            "Sim4"
        } else {
            "Sim1" // Default fallback
        }
    }
    
    /**
     * Identifies simulation from vital sign patterns when mannequin combinations
     * are insufficient to determine the simulation
     */
    private fun identifySimulationFromVitalPatterns(
        vitalSigns: List<VitalSign>,
        durationMinutes: Long,
        annotationEvents: List<Map<String, Any>>? = null
    ): String? {
        // Check for training simulations first
        
        // Check for Training Sim 3 patterns (two DCR patients)
        if (isDCRSimulation(vitalSigns)) {
            // More flexible duration check for training simulations (30-60 minutes)
            if (durationMinutes in 30..60) {
            return "TrainingSim3"
            }
        }
        
        // Check for Training Sim 5 patterns (Dave DCR and Chuck fever/seizure)
        if (hasDaveDCRAndChuckFeverPattern(vitalSigns)) {
            // More flexible duration check for training simulations (30-60 minutes)
            if (durationMinutes in 30..60) {
            return "TrainingSim5"
            }
        }
        
        // Check for empty-room lead-in (first ~7 minutes unmonitored) - Sim2
        if (hasEmptyRoomLeadIn(vitalSigns)) {
            return if (durationMinutes in 33..37) "Sim2" else "Sim1"
        }
        
        // Check for whole-run tachycardia + SpO2 probe events - Sim5
        val hasRunLongTachycardia = hasWholeRunTachycardia(vitalSigns)
        val hasMultipleProbeEvents = hasMultipleSpO2ProbeEvents(annotationEvents)
        
        if (hasRunLongTachycardia && (hasMultipleProbeEvents || durationMinutes in 38..42)) {
            return "Sim5"
        }
        
        // Check for HR flat-line + ROSC pattern - Sim3
        if (hasHRFlatLineROSCPattern(vitalSigns)) {
            return if (durationMinutes in 48..52) "Sim3" else "Sim1"
        }
        
        // Check for long hypertension plateau + spike/drop pattern - Sim4
        if (hasHypertensionPatternForSim4(vitalSigns)) {
            return if (durationMinutes in 53..57) "Sim4" else "Sim1"
        }
        
        // Check for early hypotension that improves then relapses - Sim2
        if (hasEarlyHypotensionRelapse(vitalSigns)) {
            return if (durationMinutes in 33..37) "Sim2" else "Sim1"
        }
        
        // Default to Sim1 if no other patterns match
        return if (durationMinutes in 43..47) {
            "Sim1"
        } else if (durationMinutes in 33..37) {
            "Sim2"
        } else if (durationMinutes in 48..52) {
            "Sim3"
        } else if (durationMinutes in 53..57) {
            "Sim4"
        } else if (durationMinutes in 38..42) {
            "Sim5"
        } else {
            "Sim1" // Default fallback
        }
    }
    
    /**
     * Detects whether the first ~7 minutes have every channel marked "unmonitored"
     * followed by valid data (matches Sim2 - prep before mannequins rolled in)
     */
    private fun hasEmptyRoomLeadIn(vitalSigns: List<VitalSign>): Boolean {
        if (vitalSigns.isEmpty()) return false
        
        val startTime = vitalSigns.first().timeObj
        val sevenMinuteMark = Calendar.getInstance().apply { 
            time = startTime
            add(Calendar.MINUTE, 7)
        }.time
        
        // Get vital signs in first 7 minutes
        val earlyVitals = vitalSigns.filter { 
            it.timeObj <= sevenMinuteMark
        }
        
        if (earlyVitals.isEmpty()) return false
        
        // Calculate percentage of early readings that have all channels unmonitored
        val allUnmonitoredCount = earlyVitals.count { vital ->
            (vital.hrStatus == "unmonitored" || vital.hrStatus == null) &&
            (vital.spO2Status == "unmonitored" || vital.spO2Status == null) &&
            (vital.nibpSysStatus == "unmonitored" || vital.nibpSysStatus == null) &&
            (vital.nibpMapStatus == "unmonitored" || vital.nibpMapStatus == null) &&
            (vital.respRateStatus == "unmonitored" || vital.respRateStatus == null) &&
            (vital.etCO2Status == "unmonitored" || vital.etCO2Status == null)
        }
        
        // Check if at least 70% of early readings have all channels unmonitored
        val unmonitoredRatio = allUnmonitoredCount.toDouble() / earlyVitals.size
        
        // Check if data becomes valid after the 7-minute mark
        val laterVitals = vitalSigns.filter { 
            it.timeObj > sevenMinuteMark
        }
        
        val hasValidDataLater = laterVitals.take(10).any { vital ->
            vital.hrStatus == "valid" || vital.spO2Status == "valid" || 
            vital.nibpSysStatus == "valid" || vital.nibpMapStatus == "valid" || 
            vital.respRateStatus == "valid" || vital.etCO2Status == "valid"
        }
        
        return unmonitoredRatio >= 0.7 && hasValidDataLater
    }
    
    /**
     * Detects whether there is a HR flat-line (0 bpm or missing) for a few samples,
     * then ROSC above 60 bpm (matches Sim3 - Chuck's V-tach and shock)
     */
    private fun hasHRFlatLineROSCPattern(vitalSigns: List<VitalSign>): Boolean {
        if (vitalSigns.size < 10) return false
        
        // Skip the first few and last few readings
        val middleVitals = vitalSigns.drop(vitalSigns.size / 5).dropLast(vitalSigns.size / 5)
        
        // Look for a sequence where HR becomes 0 or null and then returns
        for (i in 0 until middleVitals.size - 5) {
            val current = middleVitals[i]
            
            // If we have a valid HR
            if (current.hr != null && current.hr!! > 40) {
                // Check if next readings show flatline
                val hasFlatline = (i + 1 until i + 4).any { idx ->
                    if (idx < middleVitals.size) {
                        val next = middleVitals[idx]
                        next.hr == null || next.hr == 0.0 || next.hrStatus != "valid"
                    } else false
                }
                
                // Check if reading after flatline shows ROSC
                val hasROSC = (i + 3 until i + 8).any { idx ->
                    if (idx < middleVitals.size) {
                        val afterFlatline = middleVitals[idx]
                        afterFlatline.hr != null && afterFlatline.hr!! >= 60 && afterFlatline.hrStatus == "valid"
                    } else false
                }
                
                if (hasFlatline && hasROSC) return true
            }
        }
        
        return false
    }
    
    /**
     * Detects whether MAP is above 110 mmHg for ten minutes or more,
     * followed by a single spike or drop of at least 25 mmHg (typical Sim4)
     */
    private fun hasHypertensionPatternForSim4(vitalSigns: List<VitalSign>): Boolean {
        if (vitalSigns.size < 20) return false
        
        val tenMinutesInMillis = 10 * 60 * 1000
        var hypertensionStart: Date? = null
        var hypertensionEnd: Date? = null
        
        // Look for 10+ minutes of MAP > 110
        for (i in vitalSigns.indices) {
            val vital = vitalSigns[i]
            
            if (vital.nibpMap != null && vital.nibpMap!! > 110 && vital.nibpMapStatus == "valid") {
                if (hypertensionStart == null) {
                    hypertensionStart = vital.timeObj
                }
                hypertensionEnd = vital.timeObj
            } else if (hypertensionStart != null) {
                // Check if we've had 10+ minutes of hypertension
                if (hypertensionEnd!!.time - hypertensionStart!!.time >= tenMinutesInMillis) {
                    // Now look for a spike or drop of 25+ mmHg
                    val indexAfterHypertension = vitalSigns.indexOfFirst { 
                        it.timeObj > hypertensionEnd!! 
                    }
                    
                    if (indexAfterHypertension >= 0) {
                        // Check next 10 readings for significant change
                        for (j in indexAfterHypertension until minOf(indexAfterHypertension + 10, vitalSigns.size)) {
                            val afterVital = vitalSigns[j]
                            if (afterVital.nibpMap != null && afterVital.nibpMapStatus == "valid") {
                                // Calculate the change from the last hypertensive reading
                                val lastHypertensive = vitalSigns[i - 1]
                                val mapChange = Math.abs(afterVital.nibpMap!! - lastHypertensive.nibpMap!!)
                                
                                if (mapChange >= 25) {
                                    return true
                                }
                            }
                        }
                    }
                }
                
                // Reset hypertension tracking
                hypertensionStart = null
                hypertensionEnd = null
            }
        }
        
        return false
    }
    
    /**
     * Detects whether there is late hypotension (MAP below 55 mmHg)
     * in the last 10 minutes of a run, with HR rising (typical of Sim1 - Dave's CPP drop)
     */
    private fun hasLateHypotensionForSim1(vitalSigns: List<VitalSign>): Boolean {
        if (vitalSigns.isEmpty()) return false
        
        val endTime = vitalSigns.last().timeObj
        val tenMinutesBeforeEnd = Calendar.getInstance().apply { 
            time = endTime
            add(Calendar.MINUTE, -10)
        }.time
        
        // Get vital signs in last 10 minutes
        val lateVitals = vitalSigns.filter { 
            it.timeObj >= tenMinutesBeforeEnd
        }
        
        if (lateVitals.isEmpty()) return false
        
        // Check for MAP below 55 mmHg with HR rising
        var hasLowMAP = false
        var initialHR: Double? = null
        var finalHR: Double? = null
        
        for (vital in lateVitals) {
            if (vital.nibpMap != null && vital.nibpMap!! < 55 && vital.nibpMapStatus == "valid") {
                hasLowMAP = true
            }
            
            if (vital.hr != null && vital.hrStatus == "valid") {
                if (initialHR == null) {
                    initialHR = vital.hr
                }
                finalHR = vital.hr
            }
        }
        
        // Check if HR increased during the period
        val hrIncreased = initialHR != null && finalHR != null && finalHR > initialHR
        
        return hasLowMAP && hrIncreased
    }
    
    /**
     * Detects whether there is whole-run tachycardia
     * (HR consistently elevated throughout the simulation, typical of Sim5 burn management)
     */
    private fun hasWholeRunTachycardia(vitalSigns: List<VitalSign>): Boolean {
        if (vitalSigns.isEmpty()) return false
        
        // Count readings with HR > 100
        val validHRReadings = vitalSigns.filter { 
            it.hr != null && it.hrStatus == "valid" 
        }
        
        if (validHRReadings.size < 10) return false
        
        val tachycardiaCount = validHRReadings.count { 
            it.hr!! > 100 
        }
        
        // Return true if at least 60% of valid HR readings show tachycardia
        return tachycardiaCount.toDouble() / validHRReadings.size >= 0.6
    }
    
    /**
     * Detects whether there are multiple SpO2 probe connection/disconnection events
     * (typical of Sims 2 and 5 with heavy line work)
     */
    private fun hasMultipleSpO2ProbeEvents(annotationEvents: List<Map<String, Any>>?): Boolean {
        if (annotationEvents == null || annotationEvents.isEmpty()) return false
        
        val probeEvents = annotationEvents.count { event ->
            val eventName = event["@EvtName"] as? String
            eventName?.contains("SpO2 Probe") == true || 
            eventName?.contains("Probe Disconnected") == true
        }
        
        // Return true if we have at least 3 probe events
        return probeEvents >= 3
    }
    
    /**
     * Detects whether there is early hypotension (MAP below 60 mmHg within the first 5 minutes)
     * that improves, then relapses once (common in Sim2)
     */
    private fun hasEarlyHypotensionRelapse(vitalSigns: List<VitalSign>): Boolean {
        if (vitalSigns.isEmpty()) return false
        
        val startTime = vitalSigns.first().timeObj
        val fiveMinuteMark = Calendar.getInstance().apply { 
            time = startTime
            add(Calendar.MINUTE, 5)
        }.time
        
        // Get vital signs in first 5 minutes
        val earlyVitals = vitalSigns.filter { 
            it.timeObj <= fiveMinuteMark
        }
        
        if (earlyVitals.isEmpty()) return false
        
        // Check for early MAP below 60 mmHg
        val hasEarlyHypotension = earlyVitals.any { vital ->
            vital.nibpMap != null && vital.nibpMap!! < 60 && vital.nibpMapStatus == "valid"
        }
        
        if (!hasEarlyHypotension) return false
        
        // Now check for improvement followed by relapse
        var improvedAfterEarly = false
        var relapsedAfterImprovement = false
        
        // Skip early vitals and check pattern
        val laterVitals = vitalSigns.filter { 
            it.timeObj > fiveMinuteMark
        }
        
        for (vital in laterVitals) {
            if (!improvedAfterEarly && vital.nibpMap != null && vital.nibpMap!! >= 60 && vital.nibpMapStatus == "valid") {
                improvedAfterEarly = true
            } else if (improvedAfterEarly && vital.nibpMap != null && vital.nibpMap!! < 60 && vital.nibpMapStatus == "valid") {
                relapsedAfterImprovement = true
                break
            }
        }
        
        return hasEarlyHypotension && improvedAfterEarly && relapsedAfterImprovement
    }
    
    /**
     * Calculates the scheduled simulation window based on simulation schedule and duration
     */
    private fun getScheduledSimulationWindow(simName: String, date: Date): Pair<Date, Date>? {
        // Get the simulation schedule for this sim
        val simSchedule = Constants.SIM_SCHEDULES[simName] ?: return null
        
        // Find the block that contains this date
        val calendar = Calendar.getInstance().apply { time = date }
        val hourOfDay = calendar.get(Calendar.HOUR_OF_DAY)
        val minute = calendar.get(Calendar.MINUTE)
        
        // Convert hour and minute to minutes since midnight for easier comparison
        val timeInMinutes = hourOfDay * 60 + minute
        
        // Helper function to convert HH:MM string to minutes
        fun timeStringToMinutes(timeStr: String): Int {
            val parts = timeStr.split(":")
            if (parts.size != 2) return 0
            return parts[0].toInt() * 60 + parts[1].toInt()
        }
        
        // Find the appropriate time block
        val block = simSchedule.firstOrNull { pair ->
            val startMinutes = timeStringToMinutes(pair.first)
            val endMinutes = timeStringToMinutes(pair.second)
            timeInMinutes in startMinutes..endMinutes
        } ?: return null
        
        // Extract start hours and minutes
        val startParts = block.first.split(":")
        val startHour = startParts[0].toInt()
        val startMinute = startParts[1].toInt()
        
        // Calculate scheduled start (block start + setup buffer)
        val scheduledStartCal = Calendar.getInstance().apply {
            time = date
            set(Calendar.HOUR_OF_DAY, startHour)
            set(Calendar.MINUTE, startMinute)
            add(Calendar.MINUTE, SETUP_BUFFER_MINUTES)
        }
        
        // Get simulation duration
        val simDuration = SimulationDurations.SIM_DURATIONS[simName] ?: 45
        
        // Use a more flexible approach for training simulations
        val adjustedDuration = if (simName.startsWith("Training")) {
            // For training sims, we'll use a longer window to allow for more flexibility
            // We'll add 30 minutes to the minimum duration to create a wider detection window
            simDuration + 30
        } else {
            simDuration
        }
        
        // Calculate scheduled end (scheduled start + simulation duration)
        val scheduledEndCal = Calendar.getInstance().apply {
            time = scheduledStartCal.time
            add(Calendar.MINUTE, adjustedDuration)
        }
        
        return Pair(scheduledStartCal.time, scheduledEndCal.time)
    }
    
    /**
     * Detects the actual start time by analyzing vital sign transitions
     * Requires at least three consecutive valid readings to confirm start
     */
    private fun detectStartTime(
        vitalSigns: List<VitalSign>, 
        scheduledStart: Date,
        annotationEvents: List<Map<String, Any>>? = null
    ): Date {
        // Sort by time
        val sortedVitals = vitalSigns.sortedBy { it.timeObj }
        
        // Look for SpO₂ probe connected events first (if annotations available)
        var probeConnectedTime: Date? = null
        if (annotationEvents != null) {
            val probeEvents = annotationEvents.filter { event ->
                val eventName = event["@EvtName"] as? String ?: ""
                eventName.contains("SpO2 Probe", ignoreCase = true) && 
                eventName.contains("Connected", ignoreCase = true)
            }
            
            if (probeEvents.isNotEmpty()) {
                val eventTimeStr = probeEvents.first()["DevDateTime"] as? String ?: ""
                try {
                    probeConnectedTime = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.US).parse(eventTimeStr)
                } catch (e: Exception) {
                    // Parsing failed, continue without probe time
                }
            }
        }
        
        // Filter vitals to only include those after probe connection (if we found one)
        val relevantVitals = if (probeConnectedTime != null) {
            sortedVitals.filter { it.timeObj >= probeConnectedTime!! }
        } else {
            sortedVitals
        }
        
        if (relevantVitals.isEmpty()) {
            return scheduledStart
        }
        
        // IMPROVED APPROACH: First check for the first occurrence of actual valid vital signs
        // Find the first reading that has at least one valid major vital sign
        val firstValidReading = relevantVitals.find { vital ->
            (vital.hr != null && vital.hrStatus == "valid" && isValidHeartRate(vital.hr)) || 
            (vital.spO2 != null && vital.spO2Status == "valid" && isValidSpO2(vital.spO2)) ||
            (vital.nibpMap != null && vital.nibpMapStatus == "valid" && isValidBloodPressure(vital.nibpMap))
        }
        
        // If we found a reading with valid vital signs, use that as a starting point
        // Otherwise proceed with the original algorithm
        val searchStartPoint = if (firstValidReading != null) {
            // Get all readings from first valid point onward
            relevantVitals.filter { it.timeObj >= firstValidReading.timeObj }
        } else {
            relevantVitals
        }
        
        // If we have a valid reading, start our search from there
        // This prevents including long periods of invalid data at the beginning
        if (firstValidReading != null) {
            // Check for consistent valid data after this point
            val subsequentValidReadings = searchStartPoint.take(5)
            
            // If we have 3+ readings with valid data, this is a good start point
            val validReadingsCount = subsequentValidReadings.count { vital ->
                (vital.hr != null && vital.hrStatus == "valid" && isValidHeartRate(vital.hr)) || 
                (vital.spO2 != null && vital.spO2Status == "valid" && isValidSpO2(vital.spO2)) ||
                (vital.nibpMap != null && vital.nibpMapStatus == "valid" && isValidBloodPressure(vital.nibpMap))
            }
            
            if (validReadingsCount >= 2) {
                return firstValidReading.timeObj
            }
        }
        
        // Continue with the original algorithm for more sophisticated pattern detection
        // Track when vital signs first become valid
        val validTransitions = mutableListOf<Date>()
        
        // Check for the first transition from unmonitored to valid for each vital sign type
        val vitalTypes = listOf("hr", "spO2", "nibpSys", "nibpDia", "nibpMap", "respRate", "etCO2", "temp1")
        
        // Create a list of "major" vitals that are more important for start detection
        val majorVitalTypes = listOf("hr", "spO2", "nibpSys", "nibpDia", "nibpMap")
        
        // Map to track which vital signs have transitioned to valid
        val validVitalTypes = mutableMapOf<String, Boolean>()
        
        for (type in vitalTypes) {
            var previousStatus: String? = null
            
            for (vital in relevantVitals) {
                // Get value and apply physiological bounds check
                val value = when (type) {
                    "hr" -> if (isValidHeartRate(vital.hr)) vital.hr else null
                    "spO2" -> if (isValidSpO2(vital.spO2)) vital.spO2 else null
                    "nibpSys" -> if (isValidBloodPressure(vital.nibpSys)) vital.nibpSys else null
                    "nibpDia" -> if (isValidBloodPressure(vital.nibpDia)) vital.nibpDia else null
                    "nibpMap" -> if (isValidBloodPressure(vital.nibpMap)) vital.nibpMap else null
                    "respRate" -> if (isValidRespRate(vital.respRate)) vital.respRate else null
                    "etCO2" -> if (isValidEtCO2(vital.etCO2)) vital.etCO2 else null
                    "temp1" -> if (isValidTemperature(vital.temp1)) vital.temp1 else null
                    else -> null
                }
                
                val status = when (type) {
                    "hr" -> vital.hrStatus
                    "spO2" -> vital.spO2Status
                    "nibpSys" -> vital.nibpSysStatus
                    "nibpDia" -> vital.nibpDiaStatus
                    "nibpMap" -> vital.nibpMapStatus
                    "respRate" -> vital.respRateStatus
                    "etCO2" -> vital.etCO2Status
                    "temp1" -> vital.temp1Status
                    else -> null
                }
                
                // If we transitioned from unmonitored/null to valid
                if ((previousStatus == "unmonitored" || previousStatus == null) && status == "valid" && value != null) {
                    validVitalTypes[type] = true
                    validTransitions.add(vital.timeObj)
                    break
                }
                
                previousStatus = status
            }
        }
        
        // If we found valid transitions, look for a sequence of consistent valid readings
        if (validTransitions.isNotEmpty()) {
            // Count how many major vital signs have become valid
            val majorVitalsValidCount = majorVitalTypes.count { validVitalTypes[it] == true }
            
            // If fewer than 2 major vital signs are valid, use firstValidReading if available
            if (majorVitalsValidCount < 2) {
                return firstValidReading?.timeObj ?: scheduledStart
            }
            
            // Sort transitions by time
            val sortedTransitions = validTransitions.sorted()
            
            // Require at least two major vitals (HR, SpO₂, MAP) to be valid during sustained period
            for (startCandidate in sortedTransitions) {
                // Get readings after this candidate start time
                val subsequentReadings = relevantVitals.filter { it.timeObj >= startCandidate }
                
                // Check if we have at least 5 subsequent readings to analyze
                if (subsequentReadings.size >= 5) {
                    // Check for at least 3 consecutive readings with valid major vitals
                    var consecutiveValidCount = 0
                    var maxConsecutiveValid = 0
                    
                    for (i in 0 until minOf(10, subsequentReadings.size)) {
                        val reading = subsequentReadings[i]
                        
                        // Count major vital signs that are both valid and physiologically possible
                        val validMajorVitalsCount = listOf(
                            Triple(reading.hr, reading.hrStatus, isValidHeartRate(reading.hr)),
                            Triple(reading.spO2, reading.spO2Status, isValidSpO2(reading.spO2)),
                            Triple(reading.nibpMap, reading.nibpMapStatus, isValidBloodPressure(reading.nibpMap))
                        ).count { (value, status, isPhysiologicallyValid) ->
                            value != null && status == "valid" && isPhysiologicallyValid
                        }
                        
                        // Require at least 2 major vitals to be valid
                        if (validMajorVitalsCount >= 2) {
                            consecutiveValidCount++
                            maxConsecutiveValid = maxOf(maxConsecutiveValid, consecutiveValidCount)
                        } else {
                            consecutiveValidCount = 0
                        }
                    }
                    
                    // If we found at least 3 consecutive readings with 2+ valid major vitals
                    if (maxConsecutiveValid >= 3) {
                        // Apply stabilization period: Check if vitals remain valid for at least 30 seconds
                        val candidateStart = startCandidate
                        val stabilizationTimeMs = 30 * 1000L // 30 seconds
                        
                        // Find the reading 30 seconds after candidate start
                        val stabilizationPoint = subsequentReadings.firstOrNull { 
                            it.timeObj.time >= (candidateStart.time + stabilizationTimeMs)
                        }
                        
                        if (stabilizationPoint != null) {
                            // Check if we still have 2+ valid major vitals at the stabilization point
                            val validMajorVitalsAtStabilization = listOf(
                                Triple(stabilizationPoint.hr, stabilizationPoint.hrStatus, isValidHeartRate(stabilizationPoint.hr)),
                                Triple(stabilizationPoint.spO2, stabilizationPoint.spO2Status, isValidSpO2(stabilizationPoint.spO2)),
                                Triple(stabilizationPoint.nibpMap, stabilizationPoint.nibpMapStatus, isValidBloodPressure(stabilizationPoint.nibpMap))
                            ).count { (value, status, isPhysiologicallyValid) ->
                                value != null && status == "valid" && isPhysiologicallyValid
                            }
                            
                            if (validMajorVitalsAtStabilization >= 2) {
                                return candidateStart
                            }
                        } else {
                            // If we don't have data 30 seconds later, just use the candidate start
                            return candidateStart
                        }
                    }
                }
            }
            
            // If we couldn't find a sequence with 2+ stable major vitals, use a moving window approach
            // Look for at least 3 valid readings in any 5 consecutive readings
            for (startCandidate in sortedTransitions) {
                val subsequentReadings = relevantVitals.filter { it.timeObj >= startCandidate }
                
                if (subsequentReadings.size >= 5) {
                    // Use a sliding window of 5 readings
                    for (i in 0..subsequentReadings.size - 5) {
                        val window = subsequentReadings.subList(i, i + 5)
                        
                        // Count how many readings in the window have 2+ valid major vitals
                        val readingsWithValidMajorVitals = window.count { reading ->
                            val validMajorVitalsCount = listOf(
                                Triple(reading.hr, reading.hrStatus, isValidHeartRate(reading.hr)),
                                Triple(reading.spO2, reading.spO2Status, isValidSpO2(reading.spO2)),
                                Triple(reading.nibpMap, reading.nibpMapStatus, isValidBloodPressure(reading.nibpMap))
                            ).count { (value, status, isPhysiologicallyValid) ->
                                value != null && status == "valid" && isPhysiologicallyValid
                            }
                            
                            validMajorVitalsCount >= 2
                        }
                        
                        // If at least 3 out of 5 readings have 2+ valid major vitals
                        if (readingsWithValidMajorVitals >= 3) {
                            return window.first().timeObj
                        }
                    }
                }
            }
        }
        
        // If we still don't have a good starting point, use the first valid reading if available
        if (firstValidReading != null) {
            return firstValidReading.timeObj
        }
        
        // If we still couldn't determine a good start time, use the first reading with any valid data
        val firstAnyValid = relevantVitals.find { vital ->
            (vital.hr != null && vital.hrStatus == "valid") || 
            (vital.spO2 != null && vital.spO2Status == "valid") ||
            (vital.nibpMap != null && vital.nibpMapStatus == "valid") ||
            (vital.nibpSys != null && vital.nibpSysStatus == "valid") ||
            (vital.nibpDia != null && vital.nibpDiaStatus == "valid") ||
            (vital.respRate != null && vital.respRateStatus == "valid") ||
            (vital.etCO2 != null && vital.etCO2Status == "valid") ||
            (vital.temp1 != null && vital.temp1Status == "valid")
        }
        
        // Use the first reading with any valid vital sign, or scheduled start as last resort
        return firstAnyValid?.timeObj ?: scheduledStart
    }
    
    /**
     * Detects the actual end time by looking for sustained periods without valid data
     * or specific power-off annotation events
     */
    private fun detectEndTime(
        vitalSigns: List<VitalSign>, 
        actualStart: Date, 
        scheduledEnd: Date,
        annotationEvents: List<Map<String, Any>>? = null
    ): Date {
        // Sort by time
        val sortedVitals = vitalSigns
            .filter { it.timeObj >= actualStart }
            .sortedBy { it.timeObj }
        
        if (sortedVitals.isEmpty()) {
            return scheduledEnd
        }
        
        // Check for power-off or shutdown annotation events
        if (annotationEvents != null) {
            // Get events after the actual start time
            val relevantEvents = annotationEvents
                .filter { event ->
                    val eventTime = event["DevDateTime"] as? String ?: ""
                    if (eventTime.isNotEmpty()) {
                        try {
                            val date = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.US).parse(eventTime)
                            date != null && date >= actualStart
                        } catch (e: Exception) {
                            false
                        }
                    } else {
                        false
                    }
                }
                .sortedBy { event ->
                    val eventTime = event["DevDateTime"] as? String ?: ""
                    try {
                        SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.US).parse(eventTime)
                    } catch (e: Exception) {
                        Date(0) // Earliest possible date as fallback
                    }
                }
            
            // Look for power off events
            for (event in relevantEvents) {
                val eventName = event["@EvtName"] as? String ?: ""
                val eventTime = event["DevDateTime"] as? String ?: ""
                
                // Check for power off or system shutdown events
                if (eventName.contains("Power Off", ignoreCase = true) || 
                    eventName.contains("System Off", ignoreCase = true) ||
                    eventName.contains("Shutdown", ignoreCase = true) ||
                    eventName.contains("Last Power", ignoreCase = true)) {
                    
                    try {
                        val date = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.US).parse(eventTime)
                        if (date != null) {
                            // Make sure the event is after start and not too far past the scheduled end
                            if (date > actualStart && 
                                date.time - scheduledEnd.time < TimeUnit.MINUTES.toMillis(15)) {
                                return date
                            }
                        }
                    } catch (e: Exception) {
                        // Parsing failed, continue with other detection methods
                    }
                }
            }
        }
        
        // Enhanced detection: Check for a sustained period without valid data, 
        // but also look ahead to see if valid data resumes shortly after
        var lastValidDataTime = actualStart
        var potentialEndTime: Date? = null 
        var inactiveStartTime: Date? = null
        
        for (i in sortedVitals.indices) {
            val vital = sortedVitals[i]
            
            // Check if this vital sign has any valid major vitals (with physiological bounds check)
            val hasValidMajorVitals = listOf(
                Triple(vital.hr, vital.hrStatus, isValidHeartRate(vital.hr)),
                Triple(vital.spO2, vital.spO2Status, isValidSpO2(vital.spO2)),
                Triple(vital.nibpMap, vital.nibpMapStatus, isValidBloodPressure(vital.nibpMap))
            ).any { (value, status, isPhysiologicallyValid) ->
                value != null && status == "valid" && isPhysiologicallyValid
            }
            
            if (hasValidMajorVitals) {
                lastValidDataTime = vital.timeObj
                
                // If we were in an inactive period but valid data resumed, clear potential end time
                if (potentialEndTime != null) {
                    // Check if valid data resumed within a reasonable time (10 minutes)
                    val gapDuration = vital.timeObj.time - inactiveStartTime!!.time
                    if (TimeUnit.MILLISECONDS.toMinutes(gapDuration) < 10) {
                        potentialEndTime = null
                        inactiveStartTime = null
                    }
                }
            } else if (i > 0) {
                // Only start tracking inactive period if we don't already have one
                if (inactiveStartTime == null) {
                    inactiveStartTime = vital.timeObj
                }
                
                // Check if we have a sustained period of no valid data
                val timeWithoutValidData = vital.timeObj.time - lastValidDataTime.time
                val minutesWithoutValidData = TimeUnit.MILLISECONDS.toMinutes(timeWithoutValidData)
                
                if (minutesWithoutValidData >= MIN_SUSTAINED_INACTIVE_MINUTES && potentialEndTime == null) {
                    // Mark a potential end time, but don't return it yet - check if valid data resumes
                    potentialEndTime = lastValidDataTime
                    
                    // Check if we're near the end of available data
                    val remainingDataPoints = sortedVitals.size - i - 1
                    if (remainingDataPoints < 5) {
                        // If we're near the end and have a sustained gap, use it as the end
                        return potentialEndTime
                    }
                }
            }
        }
        
        // If we identified a potential end time and reached the end of the data without finding
        // a resumption of valid data, use the potential end time
        if (potentialEndTime != null) {
            return potentialEndTime
        }
        
        // If we reach here, use the last data point as the end
        return sortedVitals.last().timeObj
    }
    
    /**
     * Fallback method that detects timeframe purely from data patterns
     * without relying on scheduled windows
     */
    private fun detectTimeframeFromDataOnly(
        vitalSigns: List<VitalSign>,
        annotationEvents: List<Map<String, Any>>? = null
    ): Pair<Date, Date>? {
        if (vitalSigns.isEmpty()) {
            return null
        }
        
        // Look for SpO₂ probe connected events first (if annotations available)
        var probeConnectedTime: Date? = null
        if (annotationEvents != null) {
            val probeEvents = annotationEvents.filter { event ->
                val eventName = event["@EvtName"] as? String ?: ""
                eventName.contains("SpO2 Probe", ignoreCase = true) && 
                eventName.contains("Connected", ignoreCase = true)
            }
            
            if (probeEvents.isNotEmpty()) {
                val eventTimeStr = probeEvents.first()["DevDateTime"] as? String ?: ""
                try {
                    probeConnectedTime = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.US).parse(eventTimeStr)
                } catch (e: Exception) {
                    // Parsing failed, continue without probe time
                }
            }
        }
        
        // Sort by time and filter by probe connection time if available
        val sortedVitals = vitalSigns.sortedBy { it.timeObj }
        val relevantVitals = if (probeConnectedTime != null) {
            sortedVitals.filter { it.timeObj >= probeConnectedTime!! }
        } else {
            sortedVitals
        }
        
        // Identify periods of activity (>= 3 consecutive readings with 2+ valid major vitals)
        val activityPeriods = mutableListOf<Pair<Date, Date>>()
        var currentStartTime: Date? = null
        var lastValidTime: Date? = null
        var consecutiveValidCount = 0
        
        for (i in relevantVitals.indices) {
            val vital = relevantVitals[i]
            
            // Check if this vital sign has valid major vitals (with physiological bounds check)
            val validMajorVitalsCount = listOf(
                Triple(vital.hr, vital.hrStatus, isValidHeartRate(vital.hr)),
                Triple(vital.spO2, vital.spO2Status, isValidSpO2(vital.spO2)),
                Triple(vital.nibpMap, vital.nibpMapStatus, isValidBloodPressure(vital.nibpMap))
            ).count { (value, status, isPhysiologicallyValid) ->
                value != null && status == "valid" && isPhysiologicallyValid
            }
            
            // Require at least 2 major vitals to be valid
            val hasValidMajorVitals = validMajorVitalsCount >= 2
            
            if (hasValidMajorVitals) {
                consecutiveValidCount++
                lastValidTime = vital.timeObj
                
                // Only start recording activity after 3 consecutive readings with 2+ valid major vitals
                if (currentStartTime == null && consecutiveValidCount >= 3) {
                    // Look back to find the actual start time (when valid data first appeared)
                    val lookbackIndex = maxOf(0, i - 2)
                    currentStartTime = relevantVitals[lookbackIndex].timeObj
                }
                
                // If this is the last element, end an activity period
                if (i == relevantVitals.size - 1 && currentStartTime != null) {
                        activityPeriods.add(Pair(currentStartTime, lastValidTime))
                    }
                } else {
                consecutiveValidCount = 0
                
                // If we were in an activity period, check if it should end
                if (currentStartTime != null && lastValidTime != null) {
                    // Check for a gap in valid data
                    val gapTime = vital.timeObj.time - lastValidTime.time
                    
                    if (TimeUnit.MILLISECONDS.toMinutes(gapTime) >= MIN_SUSTAINED_INACTIVE_MINUTES) {
                        // End of an activity period
                        activityPeriods.add(Pair(currentStartTime, lastValidTime))
                        currentStartTime = null
                    }
                }
            }
            
            // Check for a gap to the next reading
            if (i < relevantVitals.size - 1) {
                val nextVital = relevantVitals[i + 1]
                val gapTime = nextVital.timeObj.time - vital.timeObj.time
                
                if (TimeUnit.MILLISECONDS.toMinutes(gapTime) >= MIN_SUSTAINED_INACTIVE_MINUTES) {
                    if (currentStartTime != null && lastValidTime != null) {
                    // End of an activity period
                    activityPeriods.add(Pair(currentStartTime, lastValidTime))
                    currentStartTime = null
                        consecutiveValidCount = 0
                    }
                }
            }
        }
        
        // Check for power-off events if we have annotation events
        if (annotationEvents != null && !activityPeriods.isEmpty()) {
            for (period in activityPeriods) {
                // Check if there are any power-off events that would end this period
                val (startDate, endDate) = period
                
                // Find relevant power-off events
                val powerOffEvents = annotationEvents.filter { event ->
                    val eventName = event["@EvtName"] as? String ?: ""
                    val eventTimeStr = event["DevDateTime"] as? String ?: ""
                    
                    // Check if it's a power-off type of event
                    val isPowerOffEvent = eventName.contains("Power Off", ignoreCase = true) || 
                                         eventName.contains("System Off", ignoreCase = true) ||
                                         eventName.contains("Shutdown", ignoreCase = true) ||
                                         eventName.contains("Last Power", ignoreCase = true)
                    
                    if (isPowerOffEvent && eventTimeStr.isNotEmpty()) {
                        try {
                            val eventDate = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.US).parse(eventTimeStr)
                            // Check if event is after start and within a reasonable time after the current end
                            eventDate != null && 
                            eventDate > startDate && 
                            eventDate.time - endDate.time < TimeUnit.MINUTES.toMillis(15)
                        } catch (e: Exception) {
                            false
                        }
                    } else {
                        false
                    }
                }
                
                // If we found power-off events, adjust the end time
                if (powerOffEvents.isNotEmpty()) {
                    val eventTimeStr = powerOffEvents.first()["DevDateTime"] as? String ?: ""
                    try {
                        val eventDate = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.US).parse(eventTimeStr)
                        if (eventDate != null) {
                            // Return the period with adjusted end time
                            return Pair(startDate, eventDate)
                        }
                    } catch (e: Exception) {
                        // If parsing fails, keep original end date
                    }
                }
            }
        }
        
        if (activityPeriods.isEmpty()) {
            return null
        }
        
        // Enhanced selection logic for multiple activity periods
        if (activityPeriods.size > 1) {
            // Sort periods by duration (descending)
            val sortedByDuration = activityPeriods.sortedByDescending { (start, end) -> 
            end.time - start.time
        }
            
            // Get the longest period
            val longestPeriod = sortedByDuration.first()
            val longestDuration = longestPeriod.second.time - longestPeriod.first.time
            
            // Check if there are other periods with similar duration (within 60 seconds/1 minute)
            val similarPeriods = sortedByDuration.filter { (start, end) ->
                val duration = end.time - start.time
                val differenceMs = Math.abs(duration - longestDuration)
                differenceMs <= TimeUnit.MINUTES.toMillis(1) // Within 1 minute of the longest
            }
            
            // If we have multiple similar-length periods, prefer the earliest one
            if (similarPeriods.size > 1) {
                return similarPeriods.minByOrNull { it.first.time }
            }
            
            // Otherwise return the longest period
            return longestPeriod
        }
        
        // If only one period, return it
        return activityPeriods.first()
    }
    
    /**
     * Validates if a heart rate value is within physiologically plausible bounds
     */
    private fun isValidHeartRate(hr: Double?): Boolean {
        return hr != null && hr >= 30.0 && hr <= 250.0
    }
    
    /**
     * Validates if an SpO₂ value is within physiologically plausible bounds
     */
    private fun isValidSpO2(spO2: Double?): Boolean {
        return spO2 != null && spO2 >= 50.0 && spO2 <= 100.0
    }
    
    /**
     * Validates if a blood pressure value is within physiologically plausible bounds
     */
    private fun isValidBloodPressure(bp: Double?): Boolean {
        return bp != null && bp >= 30.0 && bp <= 250.0
    }
    
    /**
     * Validates if a respiratory rate value is within physiologically plausible bounds
     */
    private fun isValidRespRate(respRate: Double?): Boolean {
        return respRate != null && respRate >= 5.0 && respRate <= 60.0
    }
    
    /**
     * Validates if an EtCO₂ value is within physiologically plausible bounds
     */
    private fun isValidEtCO2(etCO2: Double?): Boolean {
        return etCO2 != null && etCO2 >= 10.0 && etCO2 <= 100.0
    }
    
    /**
     * Validates if a temperature value is within physiologically plausible bounds
     */
    private fun isValidTemperature(temp: Double?): Boolean {
        return temp != null && temp >= 90.0 && temp <= 110.0
    }
    
    /**
     * Formats a date for debugging
     */
    fun formatTime(date: Date): String {
        return SimpleDateFormat("HH:mm:ss", Locale.US).format(date)
    }
    
    /**
     * Checks if a given timestamp is within the detected simulation timeframe
     */
    fun isInSimulationTimeframe(timestamp: Date, startTime: Date, endTime: Date): Boolean {
        return timestamp in startTime..endTime
    }
    
    /**
     * Test utility method to generate sample vital signs for testing the detection algorithm
     * with different edge conditions
     *
     * @param baseTime The base time to start the sequence
     * @param scenario The test scenario to generate
     * @param mannequinName The mannequin name to use
     * @return List of generated vital signs for testing
     */
    fun generateTestData(
        baseTime: Date,
        scenario: TestScenario,
        mannequinName: String = "Dave"
    ): Pair<List<VitalSign>, List<Map<String, Any>>> {
        val vitalSigns = mutableListOf<VitalSign>()
        val annotationEvents = mutableListOf<Map<String, Any>>()
        
        val calendar = Calendar.getInstance().apply { time = baseTime }
        
        // Generate data based on scenario
        when (scenario) {
            TestScenario.NORMAL_SIMULATION -> {
                // 2 minutes of unmonitored data
                for (i in 0 until 12) { // 2 minutes at 10-second intervals
                    vitalSigns.add(createUnmonitoredVitalSign(calendar.time, mannequinName))
                    calendar.add(Calendar.SECOND, 10)
                }
                
                // 45 minutes of valid data (typical sim duration)
                for (i in 0 until 270) { // 45 minutes at 10-second intervals
                    vitalSigns.add(createValidVitalSign(calendar.time, mannequinName))
                    calendar.add(Calendar.SECOND, 10)
                }
                
                // 2 minutes of unmonitored data
                for (i in 0 until 12) { // 2 minutes at 10-second intervals
                    vitalSigns.add(createUnmonitoredVitalSign(calendar.time, mannequinName))
                    calendar.add(Calendar.SECOND, 10)
                }
            }
            
            TestScenario.EARLY_JUNK_DATA -> {
                // 10 minutes of inconsistent data with occasional valid readings
                for (i in 0 until 60) { // 10 minutes at 10-second intervals
                    val isValid = (i % 7 == 0) // Occasional valid reading
                    if (isValid) {
                        vitalSigns.add(createValidVitalSign(calendar.time, mannequinName))
                    } else {
                        vitalSigns.add(createUnmonitoredVitalSign(calendar.time, mannequinName))
                    }
                    calendar.add(Calendar.SECOND, 10)
                }
                
                // 45 minutes of valid data (typical sim duration)
                for (i in 0 until 270) { // 45 minutes at 10-second intervals
                    vitalSigns.add(createValidVitalSign(calendar.time, mannequinName))
                    calendar.add(Calendar.SECOND, 10)
                }
                
                // 2 minutes of unmonitored data
                for (i in 0 until 12) { // 2 minutes at 10-second intervals
                    vitalSigns.add(createUnmonitoredVitalSign(calendar.time, mannequinName))
                    calendar.add(Calendar.SECOND, 10)
                }
            }
            
            TestScenario.MID_SIMULATION_GAP -> {
                // 2 minutes of unmonitored data
                for (i in 0 until 12) { // 2 minutes at 10-second intervals
                    vitalSigns.add(createUnmonitoredVitalSign(calendar.time, mannequinName))
                    calendar.add(Calendar.SECOND, 10)
                }
                
                // 20 minutes of valid data
                for (i in 0 until 120) { // 20 minutes at 10-second intervals
                    vitalSigns.add(createValidVitalSign(calendar.time, mannequinName))
                    calendar.add(Calendar.SECOND, 10)
                }
                
                // 2.5 minute gap (just under the 3-minute threshold)
                calendar.add(Calendar.MINUTE, 2)
                calendar.add(Calendar.SECOND, 30)
                
                // 25 minutes more of valid data
                for (i in 0 until 150) { // 25 minutes at 10-second intervals
                    vitalSigns.add(createValidVitalSign(calendar.time, mannequinName))
                    calendar.add(Calendar.SECOND, 10)
                }
                
                // 2 minutes of unmonitored data
                for (i in 0 until 12) { // 2 minutes at 10-second intervals
                    vitalSigns.add(createUnmonitoredVitalSign(calendar.time, mannequinName))
                    calendar.add(Calendar.SECOND, 10)
                }
            }
            
            TestScenario.SUDDEN_POWER_OFF -> {
                // 2 minutes of unmonitored data
                for (i in 0 until 12) { // 2 minutes at 10-second intervals
                    vitalSigns.add(createUnmonitoredVitalSign(calendar.time, mannequinName))
                    calendar.add(Calendar.SECOND, 10)
                }
                
                // 45 minutes of valid data (typical sim duration)
                for (i in 0 until 270) { // 45 minutes at 10-second intervals
                    vitalSigns.add(createValidVitalSign(calendar.time, mannequinName))
                    calendar.add(Calendar.SECOND, 10)
                }
                
                // Add power off annotation event
                val powerOffTime = calendar.time
                annotationEvents.add(
                    mapOf(
                        "@EvtName" to "System Power Off",
                        "DevDateTime" to SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.US).format(powerOffTime),
                        "DevEvtCode" to 24,
                        "XidCode" to "00000018"
                    )
                )
            }
            
            TestScenario.MULTIPLE_SIMILAR_PERIODS -> {
                // First activity period - 45 minutes
                for (i in 0 until 12) { // 2 minutes of unmonitored
                    vitalSigns.add(createUnmonitoredVitalSign(calendar.time, mannequinName))
                    calendar.add(Calendar.SECOND, 10)
                }
                
                val firstStart = calendar.time
                
                for (i in 0 until 270) { // 45 minutes of valid data
                    vitalSigns.add(createValidVitalSign(calendar.time, mannequinName))
                    calendar.add(Calendar.SECOND, 10)
                }
                
                for (i in 0 until 24) { // 4 minutes of unmonitored (clear gap)
                    vitalSigns.add(createUnmonitoredVitalSign(calendar.time, mannequinName))
                    calendar.add(Calendar.SECOND, 10)
                }
                
                // Second activity period - 44 minutes (slightly shorter)
                val secondStart = calendar.time
                
                for (i in 0 until 264) { // 44 minutes of valid data
                    vitalSigns.add(createValidVitalSign(calendar.time, mannequinName))
                    calendar.add(Calendar.SECOND, 10)
                }
                
                for (i in 0 until 12) { // 2 minutes of unmonitored
                    vitalSigns.add(createUnmonitoredVitalSign(calendar.time, mannequinName))
                    calendar.add(Calendar.SECOND, 10)
                }
            }
        }
        
        return Pair(vitalSigns, annotationEvents)
    }
    
    /**
     * Create a valid vital sign for testing
     */
    private fun createValidVitalSign(time: Date, mannequinName: String): VitalSign {
        return VitalSign(
            timeObj = time,
            timeStr = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.US).format(time),
            deviceSerial = "TEST-DEVICE",
            sourceFile = "test_file.json",
            
            hr = 80.0,
            hrStatus = "valid",
            
            spO2 = 99.0,
            spO2Status = "valid",
            
            nibpSys = 120.0,
            nibpSysStatus = "valid",
            
            nibpDia = 80.0,
            nibpDiaStatus = "valid",
            
            nibpMap = 93.0,
            nibpMapStatus = "valid",
            
            temp1 = 98.6,
            temp1Status = "valid",
            
            respRate = 16.0,
            respRateStatus = "valid",
            
            etCO2 = 40.0,
            etCO2Status = "valid",
            
            overrideMannequin = mannequinName,
            scenario = "TestScenario"
        )
    }
    
    /**
     * Create an unmonitored vital sign for testing
     */
    private fun createUnmonitoredVitalSign(time: Date, mannequinName: String): VitalSign {
        return VitalSign(
            timeObj = time,
            timeStr = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.US).format(time),
            deviceSerial = "TEST-DEVICE",
            sourceFile = "test_file.json",
            
            hr = null,
            hrStatus = "unmonitored",
            
            spO2 = null,
            spO2Status = "unmonitored",
            
            nibpSys = null,
            nibpSysStatus = "unmonitored",
            
            nibpDia = null,
            nibpDiaStatus = "unmonitored",
            
            nibpMap = null,
            nibpMapStatus = "unmonitored",
            
            temp1 = null,
            temp1Status = "unmonitored",
            
            respRate = null,
            respRateStatus = "unmonitored",
            
            etCO2 = null,
            etCO2Status = "unmonitored",
            
            overrideMannequin = mannequinName,
            scenario = "TestScenario"
        )
    }
    
    /**
     * Test scenarios for simulation time detection
     */
    enum class TestScenario {
        NORMAL_SIMULATION,         // Standard simulation with clean start/end
        EARLY_JUNK_DATA,           // Lots of inconsistent data before simulation starts
        MID_SIMULATION_GAP,        // Gap in the middle of simulation (< 3 minutes)
        SUDDEN_POWER_OFF,          // Simulation ends with sudden power off event
        MULTIPLE_SIMILAR_PERIODS   // Multiple activity periods with similar lengths
    }
    
    /**
     * Get the scheduled time window for a simulation on a specific date
     *
     * @param simName Simulation name (Sim1, Sim2, etc.)
     * @param date The date to check
     * @return Pair of scheduled start and end times, or null if not found
     */
    fun getScheduledSimulationWindow(simName: String, date: Date): Pair<Date, Date>? {
        // Get time blocks for this simulation
        val timeBlocks = Constants.SIM_SCHEDULES[simName] ?: return null
        
        // Extract date components
        val calendar = Calendar.getInstance().apply { time = date }
        val year = calendar.get(Calendar.YEAR)
        val month = calendar.get(Calendar.MONTH)
        val day = calendar.get(Calendar.DAY_OF_MONTH)
        
        // Check if the time falls within any of the scheduled blocks
        for ((startTime, endTime) in timeBlocks) {
            val startParts = startTime.split(":")
            val endParts = endTime.split(":")
            
            if (startParts.size == 2 && endParts.size == 2) {
                val startHour = startParts[0].toInt()
                val startMinute = startParts[1].toInt()
                
                val endHour = endParts[0].toInt()
                val endMinute = endParts[1].toInt()
                
                // Create calendar for start time
                val startCalendar = Calendar.getInstance().apply {
                    set(year, month, day, startHour, startMinute, 0)
                    set(Calendar.MILLISECOND, 0)
                }
                
                // Create calendar for end time
                val endCalendar = Calendar.getInstance().apply {
                    set(year, month, day, endHour, endMinute, 0)
                    set(Calendar.MILLISECOND, 0)
                }
                
                // Create formatted time string HH:MM for the target date
                val targetTimeStr = SimpleDateFormat("HH:mm", Locale.US).format(date)
                
                // Check if the target time string falls within this block
                if (targetTimeStr in startTime..endTime) {
                    return Pair(startCalendar.time, endCalendar.time)
                }
            }
        }
        
        // If we get here, check if any time block falls on this date
        // This is a fallback for cases where we have the date but not a specific time
        if (timeBlocks.isNotEmpty()) {
            val firstBlock = timeBlocks.first()
            val lastBlock = timeBlocks.last()
            
            val startParts = firstBlock.first.split(":")
            val endParts = lastBlock.second.split(":")
            
            if (startParts.size == 2 && endParts.size == 2) {
                val startHour = startParts[0].toInt()
                val startMinute = startParts[1].toInt()
                
                val endHour = endParts[0].toInt()
                val endMinute = endParts[1].toInt()
                
                // Create calendar for start time
                val startCalendar = Calendar.getInstance().apply {
                    set(year, month, day, startHour, startMinute, 0)
                    set(Calendar.MILLISECOND, 0)
                }
                
                // Create calendar for end time
                val endCalendar = Calendar.getInstance().apply {
                    set(year, month, day, endHour, endMinute, 0)
                    set(Calendar.MILLISECOND, 0)
                }
                
                return Pair(startCalendar.time, endCalendar.time)
            }
        }
        
        return null
    }
} 