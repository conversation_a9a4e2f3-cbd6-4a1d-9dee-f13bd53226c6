package com.example.myapplication.di

import android.content.Context
import android.util.Log
import com.example.myapplication.data.repository.VitalSignsRepository
import com.example.myapplication.ui.viewmodel.VitalSignsViewModel
import com.example.myapplication.util.CrashLogger

/**
 * Single object for providing and managing app-wide dependencies
 */
object AppModule {
    private const val TAG = "AppModule"
    private var repository: VitalSignsRepository? = null
    private var viewModel: VitalSignsViewModel? = null
    
    /**
     * Get or create the repository instance
     */
    fun provideRepository(context: Context): VitalSignsRepository {
        if (repository == null) {
            try {
                // First try to get the repository with the static getInstance method
                repository = VitalSignsRepository.getInstance(context)
                Log.d(TAG, "Created repository with database support")
            } catch (e: Exception) {
                // If that fails due to database issues, try a different approach
                Log.e(TAG, "Error creating repository with database. Using alternative approach.", e)
                CrashLogger.logError(TAG, "Error creating repository with database", e)
                
                try {
                    // Try to use the createNoDatabaseInstance method via reflection
                    // This uses the built-in factory method that creates a repository without database
                    val repositoryClass = VitalSignsRepository::class.java
                    val companionField = repositoryClass.getDeclaredField("Companion")
                    companionField.isAccessible = true
                    val companion = companionField.get(null)
                    
                    val createMethod = companion.javaClass.getDeclaredMethod(
                        "createNoDatabaseInstance", 
                        Context::class.java
                    )
                    createMethod.isAccessible = true
                    
                    repository = createMethod.invoke(companion, context) as VitalSignsRepository
                    Log.d(TAG, "Created repository without database support using reflection")
                } catch (e2: Exception) {
                    // If reflection fails, create a direct instance
                    Log.e(TAG, "Reflection approach failed, creating direct instance", e2)
                    CrashLogger.logError(TAG, "Failed to create no-database repository via reflection", e2)
                    
                    try {
                        // Direct instantiation as last resort
                        repository = VitalSignsRepository(context)
                        Log.d(TAG, "Created repository via direct instantiation")
                    } catch (e3: Exception) {
                        Log.e(TAG, "All repository creation methods failed", e3)
                        CrashLogger.logError(TAG, "All repository creation methods failed", e3)
                        throw e3
                    }
                }
            }
        }
        return repository!!
    }
    
    /**
     * Get or create the view model instance
     */
    fun provideViewModel(context: Context, repository: VitalSignsRepository): VitalSignsViewModel {
        if (viewModel == null) {
            viewModel = VitalSignsViewModel(repository, context)
        }
        return viewModel!!
    }
} 