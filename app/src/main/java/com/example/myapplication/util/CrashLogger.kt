package com.example.myapplication.util

import android.content.Context
import android.os.Environment
import android.util.Log
import java.io.File
import java.io.FileOutputStream
import java.io.PrintWriter
import java.io.StringWriter
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * Utility class to log crashes to a file
 */
object CrashLogger {
    private const val TAG = "CrashLogger"
    private const val LOG_FOLDER = "vital_signs_logs"
    private const val MAX_LOG_FILES = 5
    
    private var isInitialized = false
    private lateinit var appContext: Context
    
    /**
     * Initialize crash logging
     */
    fun initialize(context: Context) {
        appContext = context.applicationContext
        
        // Set up a global exception handler
        val defaultHandler = Thread.getDefaultUncaughtExceptionHandler()
        Thread.setDefaultUncaughtExceptionHandler { thread, throwable ->
            logCrash(context, thread, throwable)
            defaultHandler?.uncaughtException(thread, throwable)
        }
        
        isInitialized = true
        Log.i(TAG, "Crash logger initialized")
    }
    
    /**
     * Log a crash to a file
     */
    fun logCrash(context: Context, thread: Thread, throwable: Throwable) {
        try {
            // Get the stack trace as a string
            val sw = StringWriter()
            val pw = PrintWriter(sw)
            throwable.printStackTrace(pw)
            val stackTrace = sw.toString()
            
            // Create log message
            val timestamp = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US).format(Date())
            val logMessage = """
                |Crash at $timestamp
                |Thread: ${thread.name}
                |Exception: ${throwable.javaClass.name}
                |Message: ${throwable.message}
                |Stack trace:
                |$stackTrace
                |
                |------------------------
                |
            """.trimMargin()
            
            // Create log directory if it doesn't exist
            val logDir = File(context.getExternalFilesDir(null), LOG_FOLDER)
            if (!logDir.exists()) {
                logDir.mkdirs()
            }
            
            // Create log file name with timestamp
            val logFileName = "crash_${SimpleDateFormat("yyyyMMdd_HHmmss", Locale.US).format(Date())}.txt"
            val logFile = File(logDir, logFileName)
            
            // Write log to file
            FileOutputStream(logFile, true).use { fos ->
                fos.write(logMessage.toByteArray())
            }
            
            // Clean up old log files if needed
            cleanUpOldLogs(logDir)
            
            Log.e(TAG, "Crash logged to file: ${logFile.absolutePath}")
            Log.e(TAG, logMessage)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to log crash", e)
        }
    }
    
    /**
     * Log an error message and exception
     */
    fun logError(tag: String, message: String, throwable: Throwable? = null) {
        Log.e(tag, message, throwable)
        
        if (!isInitialized) {
            Log.e(TAG, "CrashLogger not initialized, error not logged to file")
            return
        }
        
        try {
            val timestamp = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US).format(Date())
            val stackTrace = throwable?.let {
                val sw = StringWriter()
                val pw = PrintWriter(sw)
                it.printStackTrace(pw)
                sw.toString()
            } ?: ""
            
            val logMessage = """
                |Error at $timestamp
                |Tag: $tag
                |Message: $message
                |${if (throwable != null) "Exception: ${throwable.javaClass.name}" else ""}
                |${if (throwable != null) "Stack trace:\n$stackTrace" else ""}
                |
                |------------------------
                |
            """.trimMargin()
            
            writeLogToFile("error_${SimpleDateFormat("yyyyMMdd", Locale.US).format(Date())}.txt", logMessage)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to log error to file", e)
        }
    }
    
    /**
     * Log a warning message
     */
    fun logWarning(tag: String, message: String) {
        Log.w(tag, message)
        
        if (!isInitialized) {
            Log.w(TAG, "CrashLogger not initialized, warning not logged to file")
            return
        }
        
        try {
            val timestamp = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US).format(Date())
            val logMessage = """
                |Warning at $timestamp
                |Tag: $tag
                |Message: $message
                |
                |------------------------
                |
            """.trimMargin()
            
            writeLogToFile("warning_${SimpleDateFormat("yyyyMMdd", Locale.US).format(Date())}.txt", logMessage)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to log warning to file", e)
        }
    }
    
    /**
     * Log an info message (for important application events)
     */
    fun logInfo(tag: String, message: String) {
        Log.i(tag, message)
        
        if (!isInitialized) {
            Log.i(TAG, "CrashLogger not initialized, info not logged to file")
            return
        }
        
        try {
            val timestamp = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US).format(Date())
            val logMessage = """
                |Info at $timestamp
                |Tag: $tag
                |Message: $message
                |
                |------------------------
                |
            """.trimMargin()
            
            writeLogToFile("info_${SimpleDateFormat("yyyyMMdd", Locale.US).format(Date())}.txt", logMessage)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to log info to file", e)
        }
    }
    
    /**
     * Write a log message to a file
     */
    private fun writeLogToFile(fileName: String, logMessage: String) {
        val logDir = File(appContext.getExternalFilesDir(null), LOG_FOLDER)
        if (!logDir.exists()) {
            logDir.mkdirs()
        }
        
        val logFile = File(logDir, fileName)
        
        FileOutputStream(logFile, true).use { fos ->
            fos.write(logMessage.toByteArray())
        }
        
        cleanUpOldLogs(logDir)
    }
    
    /**
     * Clean up old log files if there are more than MAX_LOG_FILES
     */
    private fun cleanUpOldLogs(logDir: File) {
        try {
            val logFiles = logDir.listFiles { file -> 
                file.name.startsWith("crash_") || 
                file.name.startsWith("error_") || 
                file.name.startsWith("warning_") ||
                file.name.startsWith("info_")
            }
            if (logFiles != null && logFiles.size > MAX_LOG_FILES) {
                // Sort files by last modified time (oldest first)
                logFiles.sortBy { it.lastModified() }
                
                // Delete oldest files
                for (i in 0 until logFiles.size - MAX_LOG_FILES) {
                    logFiles[i].delete()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to clean up old logs", e)
        }
    }
} 