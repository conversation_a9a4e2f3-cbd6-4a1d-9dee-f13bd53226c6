package com.example.myapplication.ui.navigation

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Analytics
import androidx.compose.material.icons.filled.Assessment
import androidx.compose.material.icons.filled.DataUsage
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.MonitorHeart
import androidx.compose.material.icons.outlined.Analytics
import androidx.compose.material.icons.outlined.Assessment
import androidx.compose.material.icons.outlined.DataUsage
import androidx.compose.material.icons.outlined.Home
import androidx.compose.material.icons.outlined.MonitorHeart
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.NavigationBarItemDefaults
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.foundation.layout.height
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import androidx.navigation.NavType
import androidx.navigation.navArgument
import com.example.myapplication.R
import com.example.myapplication.ui.screens.CPGComplianceScreen
import com.example.myapplication.ui.screens.ConfigureMonitorsScreen
import com.example.myapplication.ui.screens.DataExplorerScreen
import com.example.myapplication.ui.screens.HomeScreen
import com.example.myapplication.ui.screens.RealTimeMonitorScreen
import com.example.myapplication.ui.screens.VisualizationScreen
import com.example.myapplication.ui.viewmodel.VitalSignsTab
import com.example.myapplication.ui.viewmodel.VitalSignsViewModel
import androidx.lifecycle.viewmodel.compose.viewModel

/**
 * Represents a tab in the navigation bar
 */
data class NavigationItem(
    val tab: VitalSignsTab,
    val title: String,
    val selectedIcon: ImageVector,
    val unselectedIcon: ImageVector,
    val route: String
)

// Define route constants
object Routes {
    const val HOME = "home"
    const val DATA_EXPLORER = "data_explorer"
    const val CPG_COMPLIANCE = "cpg_compliance"
    const val VISUALIZATION = "visualization"
    const val REAL_TIME_MONITOR = "real_time_monitor"
}

/**
 * Main navigation component that handles tab switching and nested navigation
 */
@Composable
fun VitalSignsNavigation(
    viewModel: VitalSignsViewModel
) {
    val navController = rememberNavController()
    val currentTab by viewModel.currentTab.collectAsState()
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentRoute = navBackStackEntry?.destination?.route ?: Routes.HOME

    // Navigation items with routes - using shorter titles for better fit on small screens
    val items = listOf(
        NavigationItem(
            tab = VitalSignsTab.HOME,
            title = "Home",
            selectedIcon = Icons.Filled.Home,
            unselectedIcon = Icons.Outlined.Home,
            route = Routes.HOME
        ),
        NavigationItem(
            tab = VitalSignsTab.DATA_EXPLORER,
            title = "Data",
            selectedIcon = Icons.Filled.DataUsage,
            unselectedIcon = Icons.Outlined.DataUsage,
            route = Routes.DATA_EXPLORER
        ),
        NavigationItem(
            tab = VitalSignsTab.CPG_COMPLIANCE,
            title = "CPG",
            selectedIcon = Icons.Filled.Assessment,
            unselectedIcon = Icons.Outlined.Assessment,
            route = Routes.CPG_COMPLIANCE
        ),
        NavigationItem(
            tab = VitalSignsTab.VISUALIZATION,
            title = "Charts",
            selectedIcon = Icons.Filled.Analytics,
            unselectedIcon = Icons.Outlined.Analytics,
            route = Routes.VISUALIZATION
        ),
        NavigationItem(
            tab = VitalSignsTab.REAL_TIME_MONITOR,
            title = "Monitor",
            selectedIcon = Icons.Filled.MonitorHeart,
            unselectedIcon = Icons.Outlined.MonitorHeart,
            route = Routes.REAL_TIME_MONITOR
        )
    )

    // Find the tab that corresponds to the current route
    val routeToTabMap = items.associate { it.route to it.tab }

    // Only show bottom bar if we're on a main tab route
    val currentBaseRoute = currentRoute.substringBefore("/")
    val showBottomBar = currentBaseRoute in routeToTabMap.keys

    // We need to handle system insets properly for Samsung devices
    // DO NOT set contentWindowInsets to zero as it causes issues on Samsung devices

    Scaffold(
        // Let the Scaffold handle the insets properly
        bottomBar = {
            if (showBottomBar) {
                NavigationBar(
                    // Professional navigation bar with optimal height and appearance
                    modifier = Modifier
                        .fillMaxWidth(), // Ensure full width
                    containerColor = MaterialTheme.colorScheme.surface, // Clean solid background
                    contentColor = MaterialTheme.colorScheme.onSurface,
                    tonalElevation = 4.dp // Subtle elevation for depth without being too prominent
                ) {
                    items.forEach { item ->
                        NavigationBarItem(
                            selected = currentBaseRoute == item.route,
                            onClick = {
                                viewModel.updateCurrentTab(item.tab)

                                when (item.route) {
                                    Routes.HOME -> {
                                        // Clear back stack and go to home
                                        navController.navigate(Routes.HOME) {
                                            popUpTo(navController.graph.id) {
                                                inclusive = true
                                            }
                                        }
                                    }
                                    Routes.REAL_TIME_MONITOR -> {
                                        // Special handling for real-time monitor
                                        navController.navigate("${Routes.REAL_TIME_MONITOR}/Real-Time Monitoring") {
                                            popUpTo(navController.graph.findStartDestination().id) {
                                                saveState = true
                                            }
                                            launchSingleTop = true
                                            restoreState = true
                                        }
                                    }
                                    else -> {
                                        // Navigate to other routes if not already on that route
                                        if (currentRoute != item.route) {
                                            navController.navigate(item.route) {
                                                popUpTo(navController.graph.findStartDestination().id) {
                                                    saveState = true
                                                }
                                                launchSingleTop = true
                                                restoreState = true
                                            }
                                        }
                                    }
                                }
                            },
                            colors = NavigationBarItemDefaults.colors(
                                indicatorColor = MaterialTheme.colorScheme.primaryContainer,
                                selectedIconColor = MaterialTheme.colorScheme.primary,
                                selectedTextColor = MaterialTheme.colorScheme.primary,
                                unselectedIconColor = MaterialTheme.colorScheme.onSurfaceVariant,
                                unselectedTextColor = MaterialTheme.colorScheme.onSurfaceVariant
                            ),
                            icon = {
                                Icon(
                                    imageVector = if (currentBaseRoute == item.route) {
                                        item.selectedIcon
                                    } else {
                                        item.unselectedIcon
                                    },
                                    contentDescription = item.title,
                                    modifier = Modifier.size(22.dp) // Slightly smaller for cleaner look
                                )
                            },
                            label = {
                                Text(
                                    text = item.title,
                                    style = MaterialTheme.typography.labelSmall,
                                    maxLines = 1,
                                    overflow = TextOverflow.Ellipsis,
                                    textAlign = TextAlign.Center
                                    // Using the theme's typography instead of hardcoded font size
                                )
                            }
                        )
                    }
                }
            }
        }
    ) { innerPadding ->
        // Use the scaffold's inner padding directly
        // The scaffold handles the bottom bar padding automatically

        NavHost(
            navController = navController,
            startDestination = Routes.HOME,
            // Just use the innerPadding from Scaffold - it already accounts for the navigation bar
            modifier = Modifier.padding(innerPadding)
        ) {
            composable(Routes.HOME) {
                HomeScreen(
                    viewModel = viewModel,
                    navController = navController
                )
            }
            composable(Routes.DATA_EXPLORER) {
                DataExplorerScreen(viewModel)
            }
            composable(Routes.CPG_COMPLIANCE) {
                CPGComplianceScreen(viewModel)
            }
            composable(Routes.VISUALIZATION) {
                VisualizationScreen(viewModel)
            }
            composable(
                route = "${Routes.REAL_TIME_MONITOR}/{sectionTitle}",
                arguments = listOf(
                    navArgument("sectionTitle") { type = NavType.StringType }
                )
            ) { backStackEntry ->
                val sectionTitle = backStackEntry.arguments?.getString("sectionTitle") ?: "Real-Time Monitoring"
                RealTimeMonitorScreen(
                    navController = navController,
                    sectionTitle = sectionTitle,
                    mannequinRepository = viewModel.repository
                )
            }
            composable("configure_monitors") {
                ConfigureMonitorsScreen(
                    viewModel = viewModel,
                    onNavigateBack = {
                        navController.popBackStack()
                    }
                )
            }
        }
    }
}