package com.example.myapplication.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp

/**
 * Mannequin scenario selection item for the monitoring screen
 * 
 * @param mannequin The name of the mannequin
 * @param scenario The currently selected scenario
 * @param availableScenarios List of available scenarios
 * @param onScenarioChanged Callback when the scenario is changed
 * @param onRemove Callback when the remove button is clicked
 * @param enabled Whether the component is enabled or disabled
 */
@Composable
fun MannequinScenarioItem(
    mannequin: String,
    scenario: String,
    availableScenarios: List<String>,
    onScenarioChanged: (String) -> Unit,
    onRemove: () -> Unit,
    enabled: Boolean = true
) {
    var showScenarioDropdown by remember { mutableStateOf(false) }
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 2.dp
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp)
        ) {
            // Mannequin name and remove button
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = mannequin,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                if (enabled) {
                    IconButton(onClick = onRemove) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "Remove",
                            tint = MaterialTheme.colorScheme.error
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            HorizontalDivider()
            Spacer(modifier = Modifier.height(8.dp))
            
            // Scenario selection
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Scenario:",
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.width(80.dp)
                )
                
                // Scenario dropdown
                Box {
                    OutlinedTextField(
                        value = scenario,
                        onValueChange = {},
                        readOnly = true,
                        enabled = enabled,
                        modifier = Modifier.fillMaxWidth(),
                        trailingIcon = {
                            if (enabled) {
                                IconButton(onClick = { showScenarioDropdown = true }) {
                                    Icon(Icons.Default.Settings, "Select scenario")
                                }
                            }
                        }
                    )
                    
                    DropdownMenu(
                        expanded = showScenarioDropdown,
                        onDismissRequest = { showScenarioDropdown = false }
                    ) {
                        availableScenarios.forEach { scenarioOption ->
                            DropdownMenuItem(
                                text = { Text(scenarioOption) },
                                onClick = {
                                    onScenarioChanged(scenarioOption)
                                    showScenarioDropdown = false
                                }
                            )
                        }
                    }
                }
            }
        }
    }
} 