package com.example.myapplication.ui.screens

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Assessment
import androidx.compose.material.icons.filled.DataUsage
import androidx.compose.material.icons.filled.MonitorHeart
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Divider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.example.myapplication.ui.components.SectionHeader
import com.example.myapplication.ui.theme.Primary
import com.example.myapplication.ui.theme.PrimaryDark
import com.example.myapplication.ui.theme.PrimaryLight
import com.example.myapplication.ui.viewmodel.VitalSignsTab
import com.example.myapplication.ui.viewmodel.VitalSignsViewModel
import com.example.myapplication.ui.navigation.Routes

/**
 * Home screen of the app
 */
@Composable
fun HomeScreen(
    viewModel: VitalSignsViewModel,
    navController: NavController? = null
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(16.dp)
            // No extra bottom padding needed - the Scaffold handles this
    ) {
        // Header
        HomeHeader()

        Spacer(modifier = Modifier.height(24.dp))

        // Quick navigation cards
        SectionHeader(title = "Quick Navigation")

        QuickNavCards(
            onDataExplorerClick = {
                viewModel.updateCurrentTab(VitalSignsTab.DATA_EXPLORER)
                navController?.navigate(Routes.DATA_EXPLORER)
            },
            onCPGComplianceClick = {
                viewModel.updateCurrentTab(VitalSignsTab.CPG_COMPLIANCE)
                navController?.navigate(Routes.CPG_COMPLIANCE)
            },
            onRealTimeMonitorClick = {
                viewModel.updateCurrentTab(VitalSignsTab.REAL_TIME_MONITOR)
                navController?.navigate("${Routes.REAL_TIME_MONITOR}/Real-Time Monitoring")
            }
        )

        Spacer(modifier = Modifier.height(24.dp))

        // App description
        SectionHeader(title = "About This App")

        AppDescriptionCard()

        Spacer(modifier = Modifier.height(24.dp))
    }
}

/**
 * Header component for the Home screen with a professional medical appearance
 */
@Composable
private fun HomeHeader() {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.fillMaxWidth()
    ) {
        // Professional medical logo with subtle gradient background
        Box(
            modifier = Modifier
                .size(100.dp)
                .background(
                    brush = Brush.radialGradient(
                        colors = listOf(
                            MaterialTheme.colorScheme.primaryContainer,
                            MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.0f)
                        ),
                        radius = 100f
                    ),
                    shape = RoundedCornerShape(50)
                ),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = Icons.Filled.MonitorHeart,
                contentDescription = "Vital Signs Monitor Logo",
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(56.dp)
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        // App name with professional styling
        Text(
            text = "Vital Signs Monitor",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(8.dp))

        // Subtitle with professional medical description
        Text(
            text = "Professional Patient Physiologic Monitoring",
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.8f),
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(4.dp))

        // Additional descriptive text
        Text(
            text = "Real-time analysis and clinical decision support",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(16.dp))
    }
}

/**
 * Quick navigation cards component
 */
@Composable
private fun QuickNavCards(
    onDataExplorerClick: () -> Unit,
    onCPGComplianceClick: () -> Unit,
    onRealTimeMonitorClick: () -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        // Data Explorer Card
        NavCard(
            title = "Data Explorer",
            description = "Load and explore vital signs data from JSON files",
            icon = Icons.Filled.DataUsage,
            onClick = onDataExplorerClick,
            isPrimary = true
        )

        Spacer(modifier = Modifier.height(8.dp))

        // CPG Compliance Card
        NavCard(
            title = "CPG Compliance",
            description = "Analyze compliance with Clinical Practice Guidelines",
            icon = Icons.Filled.Assessment,
            onClick = onCPGComplianceClick,
            isPrimary = true
        )

        Spacer(modifier = Modifier.height(8.dp))

        // Real-time Monitor Card
        NavCard(
            title = "Real-Time Monitor",
            description = "Monitor vital signs in real-time through simulation or network",
            icon = Icons.Filled.MonitorHeart,
            onClick = onRealTimeMonitorClick,
            isPrimary = true
        )
    }
}

/**
 * Navigation card component with professional medical styling
 */
@Composable
private fun NavCard(
    title: String,
    description: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    onClick: () -> Unit,
    isPrimary: Boolean = false
) {
    // Use theme colors for consistent appearance
    val backgroundColor = if (isPrimary) {
        MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.8f)
    } else {
        MaterialTheme.colorScheme.surface
    }

    val iconTint = if (isPrimary) {
        MaterialTheme.colorScheme.primary
    } else {
        MaterialTheme.colorScheme.primary.copy(alpha = 0.8f)
    }

    val titleColor = if (isPrimary) {
        MaterialTheme.colorScheme.onPrimaryContainer
    } else {
        MaterialTheme.colorScheme.onSurface
    }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp), // Increased vertical padding for better spacing
        colors = CardDefaults.cardColors(
            containerColor = backgroundColor
        ),
        shape = RoundedCornerShape(16.dp), // More rounded corners for a modern medical look
        elevation = CardDefaults.cardElevation(
            defaultElevation = 2.dp // Subtle elevation
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp), // Increased padding for better touch targets
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Icon with background for more professional appearance
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .background(
                        color = if (isPrimary) {
                            MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)
                        } else {
                            MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
                        },
                        shape = RoundedCornerShape(12.dp)
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = title,
                    tint = iconTint,
                    modifier = Modifier.size(28.dp)
                )
            }

            Column(
                modifier = Modifier
                    .weight(1f)
                    .padding(horizontal = 16.dp)
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = titleColor
                )

                Spacer(modifier = Modifier.height(4.dp))

                Text(
                    text = description,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    lineHeight = 20.sp // Improved line height for readability
                )
            }

            Button(
                onClick = onClick,
                colors = ButtonDefaults.buttonColors(
                    containerColor = if (isPrimary) {
                        MaterialTheme.colorScheme.primary
                    } else {
                        MaterialTheme.colorScheme.primary.copy(alpha = 0.8f)
                    }
                ),
                shape = RoundedCornerShape(8.dp) // Consistent rounded corners
            ) {
                Text(
                    text = "Open",
                    style = MaterialTheme.typography.labelMedium,
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

/**
 * App description card component with professional medical styling
 */
@Composable
private fun AppDescriptionCard() {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.7f)
        ),
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 1.dp // Subtle elevation
        )
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            // Title for the description card
            Text(
                text = "About This Application",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            Spacer(modifier = Modifier.height(12.dp))

            // Main description with improved formatting
            Text(
                text = "This professional medical application provides comprehensive analysis tools for patient physiologic data monitoring and evaluation. It features:",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                lineHeight = 22.sp
            )

            Spacer(modifier = Modifier.height(8.dp))

            // Bullet points for key features
            BulletPoint("Real-time monitoring of vital signs with clinical alerts")
            BulletPoint("Integration with ZOLL monitoring systems")
            BulletPoint("Analysis of compliance with Clinical Practice Guidelines (CPGs)")
            BulletPoint("Advanced visualization of physiologic trends")
            BulletPoint("Comprehensive data export and reporting capabilities")
        }
    }
}

/**
 * Bullet point text component for consistent styling
 */
@Composable
private fun BulletPoint(text: String) {
    Row(
        modifier = Modifier.padding(vertical = 3.dp),
        verticalAlignment = Alignment.Top
    ) {
        Text(
            text = "•",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.primary,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(end = 8.dp, top = 0.dp)
        )

        Text(
            text = text,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            lineHeight = 20.sp
        )
    }
}