package com.example.myapplication.ui.components

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Divider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.example.myapplication.data.model.ActiveNonComplianceEvent
import com.example.myapplication.data.model.NonComplianceEvent
import com.example.myapplication.data.model.VitalSignStatus
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * Displays an indicator for an active non-compliance event
 */
@Composable
fun ActiveNonComplianceIndicator(
    event: ActiveNonComplianceEvent,
    currentTime: Date = Date(),
    onClick: (ActiveNonComplianceEvent) -> Unit = {}
) {
    // Animate the background color for critical events
    val isCritical = event.status == VitalSignStatus.CRITICAL_HIGH || event.status == VitalSignStatus.CRITICAL_LOW
    
    val infiniteTransition = rememberInfiniteTransition(label = "pulse")
    val alpha by infiniteTransition.animateFloat(
        initialValue = if (isCritical) 0.7f else 0.2f,
        targetValue = if (isCritical) 1.0f else 0.2f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = LinearEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "alpha"
    )
    
    val backgroundColor = when (event.status) {
        VitalSignStatus.WARNING_HIGH, VitalSignStatus.WARNING_LOW -> Color(0xFFFFEB3B).copy(alpha = 0.2f)
        VitalSignStatus.CRITICAL_HIGH, VitalSignStatus.CRITICAL_LOW -> Color(0xFFF44336).copy(alpha = alpha)
        else -> Color.Transparent
    }
    
    val durationSeconds = event.getDurationSeconds(currentTime)
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(4.dp)
            .clickable { onClick(event) },
        colors = CardDefaults.cardColors(containerColor = backgroundColor)
    ) {
        Row(
            modifier = Modifier.padding(8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.Warning,
                contentDescription = "Non-compliant vital sign",
                tint = when (event.status) {
                    VitalSignStatus.WARNING_HIGH, VitalSignStatus.WARNING_LOW -> Color(0xFFFFEB3B)
                    VitalSignStatus.CRITICAL_HIGH, VitalSignStatus.CRITICAL_LOW -> Color(0xFFF44336)
                    else -> Color.Gray
                }
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "${event.vitalSign}: ${event.currentValue.toInt()} (${event.status.getLabel()})",
                    fontWeight = FontWeight.Bold
                )
                Text(
                    text = "Outside threshold ${event.getThresholdString()} for ${durationSeconds}s",
                    style = MaterialTheme.typography.bodySmall
                )
            }
        }
    }
}

/**
 * Displays a timeline of non-compliance events
 */
@Composable
fun NonComplianceTimeline(
    nonComplianceEvents: List<NonComplianceEvent>,
    sessionStartTime: Date,
    sessionEndTime: Date,
    onEventClick: (NonComplianceEvent) -> Unit = {}
) {
    val sessionDurationMs = sessionEndTime.time - sessionStartTime.time
    if (sessionDurationMs <= 0) return
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(60.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
    ) {
        Box(modifier = Modifier
            .fillMaxSize()
            .padding(horizontal = 8.dp, vertical = 4.dp)) {
            // Timeline base line
            Divider(
                modifier = Modifier.align(Alignment.CenterStart),
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.3f)
            )
            
            // Event markers
            nonComplianceEvents.forEach { event ->
                val startOffset = ((event.startTime.time - sessionStartTime.time).toFloat() / sessionDurationMs)
                    .coerceIn(0f, 1f)
                val endOffset = ((event.endTime.time - sessionStartTime.time).toFloat() / sessionDurationMs)
                    .coerceIn(0f, 1f)
                val width = ((endOffset - startOffset) * 100f).coerceAtLeast(0.5f)
                
                val color = when {
                    event.durationMin >= 3.0 -> Color(0xFFF44336) // Red
                    event.durationMin >= 1.0 -> Color(0xFFF57C00) // Orange
                    else -> Color(0xFFFFEB3B) // Yellow
                }
                
                Box(
                    modifier = Modifier
                        .fillMaxHeight(0.6f)
                        .width(width.dp)
                        .align(Alignment.CenterStart)
                        .offset(x = (startOffset * 100).dp)
                        .background(color)
                        .clickable { onEventClick(event) }
                )
            }
            
            // Time markers
            val timeFormat = SimpleDateFormat("HH:mm", Locale.US)
            Text(
                text = timeFormat.format(sessionStartTime),
                style = MaterialTheme.typography.bodySmall,
                modifier = Modifier
                    .align(Alignment.BottomStart)
                    .padding(top = 4.dp)
            )
            
            Text(
                text = timeFormat.format(sessionEndTime),
                style = MaterialTheme.typography.bodySmall,
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(top = 4.dp)
            )
        }
    }
}

/**
 * Displays a list of non-compliance events
 */
@Composable
fun NonComplianceEventList(
    nonComplianceEvents: List<NonComplianceEvent>,
    onEventClick: (NonComplianceEvent) -> Unit = {}
) {
    LazyColumn {
        items(nonComplianceEvents.sortedByDescending { it.startTime }) { event ->
            NonComplianceEventItem(event = event, onClick = { onEventClick(event) })
        }
    }
}

/**
 * Displays a single non-compliance event
 */
@Composable
fun NonComplianceEventItem(
    event: NonComplianceEvent,
    onClick: () -> Unit = {}
) {
    val timeFormat = SimpleDateFormat("HH:mm:ss", Locale.US)
    val startTimeStr = timeFormat.format(event.startTime)
    val endTimeStr = timeFormat.format(event.endTime)
    
    val backgroundColor = when {
        event.durationMin >= 3.0 -> Color(0xFFF44336).copy(alpha = 0.1f) // Red
        event.durationMin >= 1.0 -> Color(0xFFF57C00).copy(alpha = 0.1f) // Orange
        else -> Color(0xFFFFEB3B).copy(alpha = 0.1f) // Yellow
    }
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 2.dp)
            .clickable { onClick() },
        colors = CardDefaults.cardColors(containerColor = backgroundColor)
    ) {
        Row(
            modifier = Modifier.padding(8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "${event.vitalSign} (${String.format("%.1f", event.durationMin)} min)",
                    fontWeight = FontWeight.Bold
                )
                
                Text(
                    text = "$startTimeStr - $endTimeStr",
                    style = MaterialTheme.typography.bodySmall
                )
                
                Text(
                    text = "Avg: ${String.format("%.1f", event.values.average())} " +
                           "Range: ${String.format("%.1f", event.values.minOrNull() ?: 0.0)} - " +
                           "${String.format("%.1f", event.values.maxOrNull() ?: 0.0)}",
                    style = MaterialTheme.typography.bodySmall
                )
                
                Text(
                    text = "Threshold: ${
                        when {
                            event.minThreshold != null && event.maxThreshold != null -> 
                                "${event.minThreshold} - ${event.maxThreshold}"
                            event.minThreshold != null -> "≥ ${event.minThreshold}"
                            event.maxThreshold != null -> "≤ ${event.maxThreshold}"
                            else -> "Unknown"
                        }
                    }",
                    style = MaterialTheme.typography.bodySmall
                )
            }
        }
    }
}
