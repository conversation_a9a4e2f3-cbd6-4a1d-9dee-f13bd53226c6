package com.example.myapplication.ui.screens

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Save
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ExposedDropdownMenuBox
import androidx.compose.material3.ExposedDropdownMenuDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.example.myapplication.data.model.MannequinConfig
import com.example.myapplication.data.repository.VitalSignsRepository.DataSourceType
import com.example.myapplication.ui.viewmodel.VitalSignsViewModel

/**
 * Screen for configuring Zoll monitor IP addresses for all mannequins
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ConfigureMonitorsScreen(
    viewModel: VitalSignsViewModel,
    onNavigateBack: () -> Unit
) {
    val settings by viewModel.settings.collectAsState()
    
    // Load all mannequins
    val allMannequins = MannequinConfig.DEFAULT_MANNEQUIN_IPS.keys.sorted()
    
    // State for all monitor IP addresses
    var monitorIpAddresses by remember { 
        mutableStateOf(
            allMannequins.associate { mannequin ->
                val savedIp = settings.mannequinIpAddresses[mannequin]
                mannequin to (savedIp ?: MannequinConfig.DEFAULT_MANNEQUIN_IPS[mannequin] ?: "")
            }
        )
    }
    
    // State for data source selection
    var dataSourceType by remember { mutableStateOf(settings.dataSourceType) }
    var showDataSourceDropdown by remember { mutableStateOf(false) }
    
    // State for IP address and port for single monitor mode
    var ipAddress by remember { mutableStateOf(settings.ipAddress) }
    var port by remember { mutableStateOf(settings.port.toString()) }
    
    // State for authentication
    var authRequired by remember { mutableStateOf(settings.authRequired) }
    var username by remember { mutableStateOf(settings.username) }
    var password by remember { mutableStateOf(settings.password) }
    
    // State for update interval
    var updateIntervalMs by remember { mutableStateOf(settings.updateIntervalMs.toString()) }
    
    // State for validation
    var ipErrors by remember { mutableStateOf(emptyMap<String, String>()) }
    
    // Validate IP address
    fun validateIp(ip: String): Boolean {
        if (ip.isEmpty()) return true // Allow empty for now
        
        val ipPattern = """^([01]?\d\d?|2[0-4]\d|25[0-5])\.([01]?\d\d?|2[0-4]\d|25[0-5])\.([01]?\d\d?|2[0-4]\d|25[0-5])\.([01]?\d\d?|2[0-4]\d|25[0-5])$""".toRegex()
        return ipPattern.matches(ip)
    }
    
    // Validate port
    fun validatePort(portStr: String): Boolean {
        return try {
            val portNum = portStr.toInt()
            portNum in 1..65535
        } catch (e: Exception) {
            false
        }
    }
    
    // Validate update interval
    fun validateUpdateInterval(intervalStr: String): Boolean {
        return try {
            val interval = intervalStr.toLong()
            interval >= 100 // Minimum 100ms
        } catch (e: Exception) {
            false
        }
    }
    
    // Save all settings
    fun saveSettings() {
        // Validate all IPs first
        val newErrors = mutableMapOf<String, String>()
        
        monitorIpAddresses.forEach { (mannequin, ip) ->
            if (!validateIp(ip)) {
                newErrors[mannequin] = "Invalid IP address format"
            }
        }
        
        // Validate single monitor IP if applicable
        if (dataSourceType == DataSourceType.REAL_TIME_MONITOR || dataSourceType == DataSourceType.WEBSOCKET) {
            if (!validateIp(ipAddress)) {
                newErrors["singleMonitor"] = "Invalid IP address format"
            }
            
            if (!validatePort(port)) {
                newErrors["port"] = "Invalid port (must be 1-65535)"
            }
        }
        
        // Validate update interval
        if (!validateUpdateInterval(updateIntervalMs)) {
            newErrors["updateInterval"] = "Invalid update interval (must be ≥ 100ms)"
        }
        
        ipErrors = newErrors
        
        // Only save if all validations pass
        if (newErrors.isEmpty()) {
            // Filter out empty IP addresses
            val validIpAddresses = monitorIpAddresses.filter { (_, ip) -> ip.isNotEmpty() }
            
            viewModel.updateSettings(
                dataSourceType = dataSourceType,
                ipAddress = ipAddress,
                port = port.toIntOrNull() ?: 80,
                authRequired = authRequired,
                username = username,
                password = password,
                updateIntervalMs = updateIntervalMs.toLongOrNull() ?: 1000,
                scenario = settings.scenario,
                mannequinIpAddresses = validIpAddresses
            )
            
            onNavigateBack()
        }
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Configure Monitors") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "Back")
                    }
                },
                actions = {
                    IconButton(onClick = { saveSettings() }) {
                        Icon(Icons.Filled.Save, contentDescription = "Save")
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
                .verticalScroll(rememberScrollState())
        ) {
            // Introduction text
            Text(
                text = "Monitor Configuration",
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "Set up your data source and monitor connections for real-time monitoring.",
                style = MaterialTheme.typography.bodyMedium
            )
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // Data Source Selection
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Data Source",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // Data source dropdown
                    ExposedDropdownMenuBox(
                        expanded = showDataSourceDropdown,
                        onExpandedChange = { showDataSourceDropdown = it }
                    ) {
                        OutlinedTextField(
                            value = when(dataSourceType) {
                                DataSourceType.SIMULATION -> "Simulation Demo"
                                DataSourceType.JSON_FILE -> "JSON File"
                                DataSourceType.REAL_TIME_MONITOR -> "Real-Time Monitor (HTTP)"
                                DataSourceType.WEBSOCKET -> "WebSocket"
                                DataSourceType.DATABASE -> "Database"
                            },
                            onValueChange = {},
                            readOnly = true,
                            label = { Text("Data Source Type") },
                            trailingIcon = { 
                                ExposedDropdownMenuDefaults.TrailingIcon(expanded = showDataSourceDropdown)
                            },
                            modifier = Modifier
                                .fillMaxWidth()
                                .menuAnchor()
                        )
                        
                        ExposedDropdownMenu(
                            expanded = showDataSourceDropdown,
                            onDismissRequest = { showDataSourceDropdown = false }
                        ) {
                            DropdownMenuItem(
                                text = { Text("Simulation Demo") },
                                onClick = {
                                    dataSourceType = DataSourceType.SIMULATION
                                    showDataSourceDropdown = false
                                }
                            )
                            DropdownMenuItem(
                                text = { Text("JSON File") },
                                onClick = {
                                    dataSourceType = DataSourceType.JSON_FILE
                                    showDataSourceDropdown = false
                                }
                            )
                            DropdownMenuItem(
                                text = { Text("Real-Time Monitor (HTTP)") },
                                onClick = {
                                    dataSourceType = DataSourceType.REAL_TIME_MONITOR
                                    showDataSourceDropdown = false
                                }
                            )
                            DropdownMenuItem(
                                text = { Text("WebSocket") },
                                onClick = {
                                    dataSourceType = DataSourceType.WEBSOCKET
                                    showDataSourceDropdown = false
                                }
                            )
                            DropdownMenuItem(
                                text = { Text("Database") },
                                onClick = {
                                    dataSourceType = DataSourceType.DATABASE
                                    showDataSourceDropdown = false
                                }
                            )
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // Update interval
                    OutlinedTextField(
                        value = updateIntervalMs,
                        onValueChange = { 
                            updateIntervalMs = it 
                            // Clear error when typing
                            if (ipErrors.containsKey("updateInterval")) {
                                ipErrors = ipErrors.toMutableMap().apply {
                                    remove("updateInterval")
                                }
                            }
                        },
                        label = { Text("Update Interval (ms)") },
                        placeholder = { Text("1000") },
                        modifier = Modifier.fillMaxWidth(),
                        isError = ipErrors.containsKey("updateInterval"),
                        supportingText = {
                            if (ipErrors.containsKey("updateInterval")) {
                                Text(ipErrors["updateInterval"] ?: "")
                            }
                        }
                    )
                }
            }
            
            // Only show single monitor config for real-time and websocket modes
            if (dataSourceType == DataSourceType.REAL_TIME_MONITOR || dataSourceType == DataSourceType.WEBSOCKET) {
                Spacer(modifier = Modifier.height(16.dp))
                
                // Single Monitor Configuration
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surface
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "Single Monitor Configuration",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold
                        )
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        // IP Address
                        OutlinedTextField(
                            value = ipAddress,
                            onValueChange = { 
                                ipAddress = it 
                                // Clear error when typing
                                if (ipErrors.containsKey("singleMonitor")) {
                                    ipErrors = ipErrors.toMutableMap().apply {
                                        remove("singleMonitor")
                                    }
                                }
                            },
                            label = { Text("Monitor IP Address") },
                            placeholder = { Text("e.g., *************") },
                            modifier = Modifier.fillMaxWidth(),
                            isError = ipErrors.containsKey("singleMonitor"),
                            supportingText = {
                                if (ipErrors.containsKey("singleMonitor")) {
                                    Text(ipErrors["singleMonitor"] ?: "")
                                }
                            }
                        )
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        // Port
                        OutlinedTextField(
                            value = port,
                            onValueChange = { 
                                port = it 
                                // Clear error when typing
                                if (ipErrors.containsKey("port")) {
                                    ipErrors = ipErrors.toMutableMap().apply {
                                        remove("port")
                                    }
                                }
                            },
                            label = { Text("Port") },
                            placeholder = { Text("80") },
                            modifier = Modifier.fillMaxWidth(),
                            isError = ipErrors.containsKey("port"),
                            supportingText = {
                                if (ipErrors.containsKey("port")) {
                                    Text(ipErrors["port"] ?: "")
                                }
                            }
                        )
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        // Authentication toggle
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "Authentication Required",
                                style = MaterialTheme.typography.bodyMedium
                            )
                            
                            Spacer(modifier = Modifier.weight(1f))
                            
                            Switch(
                                checked = authRequired,
                                onCheckedChange = { authRequired = it }
                            )
                        }
                        
                        // Authentication fields if required
                        if (authRequired) {
                            Spacer(modifier = Modifier.height(8.dp))
                            
                            OutlinedTextField(
                                value = username,
                                onValueChange = { username = it },
                                label = { Text("Username") },
                                modifier = Modifier.fillMaxWidth()
                            )
                            
                            Spacer(modifier = Modifier.height(8.dp))
                            
                            OutlinedTextField(
                                value = password,
                                onValueChange = { password = it },
                                label = { Text("Password") },
                                modifier = Modifier.fillMaxWidth()
                            )
                        }
                    }
                }
            }
            
            // Multi-Mannequin Configuration
            if (dataSourceType == DataSourceType.REAL_TIME_MONITOR) {
                Spacer(modifier = Modifier.height(16.dp))
                
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surface
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "Multi-Mannequin Configuration",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold
                        )
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        Text(
                            text = "Configure IP addresses for all Zoll monitors connected to mannequins. " +
                                  "These will be used when running simulations with multiple mannequins.",
                            style = MaterialTheme.typography.bodyMedium
                        )
                        
                        Spacer(modifier = Modifier.height(16.dp))
                        
                        // List all mannequins
                        allMannequins.forEach { mannequin ->
                            Card(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 8.dp),
                                colors = CardDefaults.cardColors(
                                    containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
                                )
                            ) {
                                Column(
                                    modifier = Modifier.padding(16.dp)
                                ) {
                                    Text(
                                        text = "Mannequin: $mannequin",
                                        style = MaterialTheme.typography.titleMedium,
                                        fontWeight = FontWeight.Bold
                                    )
                                    
                                    Spacer(modifier = Modifier.height(8.dp))
                                    
                                    OutlinedTextField(
                                        value = monitorIpAddresses[mannequin] ?: "",
                                        onValueChange = { newIp ->
                                            monitorIpAddresses = monitorIpAddresses.toMutableMap().apply {
                                                put(mannequin, newIp)
                                            }
                                            
                                            // Clear error when typing
                                            if (ipErrors.containsKey(mannequin)) {
                                                ipErrors = ipErrors.toMutableMap().apply {
                                                    remove(mannequin)
                                                }
                                            }
                                        },
                                        label = { Text("Zoll Monitor IP Address") },
                                        placeholder = { Text("e.g., *************") },
                                        modifier = Modifier.fillMaxWidth(),
                                        isError = ipErrors.containsKey(mannequin),
                                        supportingText = {
                                            if (ipErrors.containsKey(mannequin)) {
                                                Text(ipErrors[mannequin] ?: "")
                                            }
                                        },
                                        trailingIcon = {
                                            if (monitorIpAddresses[mannequin]?.isNotEmpty() == true && 
                                                validateIp(monitorIpAddresses[mannequin] ?: "")) {
                                                Icon(
                                                    Icons.Filled.Check,
                                                    contentDescription = "Valid",
                                                    tint = MaterialTheme.colorScheme.primary
                                                )
                                            }
                                        }
                                    )
                                    
                                    // Show default IP hint
                                    val defaultIp = MannequinConfig.DEFAULT_MANNEQUIN_IPS[mannequin]
                                    if (defaultIp != null) {
                                        Text(
                                            text = "Default: $defaultIp",
                                            style = MaterialTheme.typography.bodySmall,
                                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // Save button
            Button(
                onClick = { saveSettings() },
                modifier = Modifier.fillMaxWidth()
            ) {
                Icon(
                    Icons.Filled.Save,
                    contentDescription = "Save",
                    modifier = Modifier.padding(end = 8.dp)
                )
                Text("Save Configuration")
            }
            
            // Bottom spacing for better scrolling
            Spacer(modifier = Modifier.height(60.dp))
        }
    }
} 