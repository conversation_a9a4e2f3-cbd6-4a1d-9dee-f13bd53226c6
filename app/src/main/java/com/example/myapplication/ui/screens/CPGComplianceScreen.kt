package com.example.myapplication.ui.screens

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Divider
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ExposedDropdownMenuBox
import androidx.compose.material3.ExposedDropdownMenuDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import java.util.Date
import com.example.myapplication.data.ClinicalPracticeGuidelines
import com.example.myapplication.data.model.Annotation
import com.example.myapplication.data.model.AnnotationType
import com.example.myapplication.data.model.MannequinConfig
import com.example.myapplication.ui.components.ComplianceStatusIndicator
import com.example.myapplication.ui.components.NonComplianceEventList
import com.example.myapplication.ui.components.NonComplianceTimeline
import com.example.myapplication.ui.components.SectionHeader
import com.example.myapplication.ui.theme.Danger
import com.example.myapplication.ui.theme.ExcellentColor
import com.example.myapplication.ui.theme.FairColor
import com.example.myapplication.ui.theme.GoodColor
import com.example.myapplication.ui.theme.Orange700
import com.example.myapplication.ui.theme.PoorColor
import com.example.myapplication.ui.theme.UnknownColor
import com.example.myapplication.ui.theme.Yellow700
import com.example.myapplication.ui.viewmodel.ComplianceStatus
import com.example.myapplication.ui.viewmodel.MonitoringState
import com.example.myapplication.ui.viewmodel.SelectedFilters
import com.example.myapplication.ui.viewmodel.VitalSignsViewModel
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Warning
import androidx.compose.foundation.BorderStroke
import com.example.myapplication.data.model.NonComplianceEvent
import androidx.compose.foundation.layout.size
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.clickable
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.ui.draw.rotate
import androidx.compose.material3.ExposedDropdownMenuBox

/**
 * Screen for analyzing compliance with Clinical Practice Guidelines
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CPGComplianceScreen(viewModel: VitalSignsViewModel) {
    // State
    val vitalSigns by viewModel.vitalSigns.collectAsState()
    val filteredVitalSigns by viewModel.filteredVitalSigns.collectAsState()
    val settings by viewModel.settings.collectAsState()
    val complianceStats by viewModel.complianceStats.collectAsState()
    val monitoringState by viewModel.monitoringState.collectAsState()
    val selectedFilters by viewModel.selectedFilters.collectAsState()
    val vitalRangeFilters by viewModel.vitalRangeFilters.collectAsState()
    val availableVitalSigns by viewModel.availableVitalSigns.collectAsState()

    // Determine which scenario to use based on app state
    val currentScenario = when {
        // If we're in real-time monitoring mode and have a selected simulation
        monitoringState is MonitoringState.Running && settings.selectedSimulation.isNotEmpty() ->
            settings.scenario

        // If we've recently stopped monitoring and have data
        monitoringState is MonitoringState.Stopped && vitalSigns.isNotEmpty() && settings.selectedSimulation.isNotEmpty() ->
            settings.scenario

        // If we have applied filters in Data Explorer, use the scenario from selected mannequin
        selectedFilters.mannequin != null && selectedFilters.mannequin != "<all>" ->
            getMannequinScenario(selectedFilters.mannequin, selectedFilters)

        // Otherwise use the currently selected scenario
        else -> settings.scenario
    }

    // Determine if we should show the scenario selector
    val shouldShowScenarioSelector = when {
        // Hide selector if specific mannequin is selected in filters
        selectedFilters.mannequin != null && selectedFilters.mannequin != "<all>" -> false

        // Hide selector during active real-time monitoring with a simulation selected
        monitoringState is MonitoringState.Running && settings.selectedSimulation.isNotEmpty() -> false

        // Otherwise show the selector
        else -> true
    }

    // Check if vital range filters are active
    val hasActiveVitalRangeFilters = availableVitalSigns.isNotEmpty() && vitalRangeFilters.run {
        (heartRate.available && (heartRate.min > heartRate.defaultMin || heartRate.max < heartRate.defaultMax)) ||
        (spo2.available && (spo2.min > spo2.defaultMin || spo2.max < spo2.defaultMax)) ||
        (nibpSys.available && (nibpSys.min > nibpSys.defaultMin || nibpSys.max < nibpSys.defaultMax)) ||
        (nibpDia.available && (nibpDia.min > nibpDia.defaultMin || nibpDia.max < nibpDia.defaultMax)) ||
        (nibpMap.available && (nibpMap.min > nibpMap.defaultMin || nibpMap.max < nibpMap.defaultMax)) ||
        (temp.available && (temp.min > temp.defaultMin || temp.max < temp.defaultMax)) ||
        (respRate.available && (respRate.min > respRate.defaultMin || respRate.max < respRate.defaultMax)) ||
        (etco2.available && (etco2.min > etco2.defaultMin || etco2.max < etco2.defaultMax))
    }

    // Ensure compliance stats are updated when monitoring state changes
    LaunchedEffect(monitoringState) {
        if (monitoringState is MonitoringState.Running) {
            // When monitoring starts, make sure scenario is selected correctly
            viewModel.updateSettings(scenario = currentScenario)
        }
    }

    // Make sure we're using the correct scenario based on data source
    LaunchedEffect(currentScenario) {
        viewModel.updateSettings(scenario = currentScenario)
    }

    // Scenario selector
    var expanded by remember { mutableStateOf(false) }
    var selectedScenario by remember(currentScenario) { mutableStateOf(currentScenario) }
    val scenarios = remember { ClinicalPracticeGuidelines.ALL_THRESHOLDS.keys.toList() }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState())
    ) {
        // Header
        SectionHeader(title = "CPG Compliance")

        Text(
            text = "Analyze compliance with Clinical Practice Guidelines",
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
        )

        // Active data source information
        Spacer(modifier = Modifier.height(8.dp))

        val dataSourceText = when {
            // If we have data from a recent simulation that was stopped
            !vitalSigns.isEmpty() && monitoringState is MonitoringState.Stopped &&
            settings.selectedSimulation.isNotEmpty() ->
                "Using data from recent ${settings.selectedSimulation} simulation"

            // If monitoring is actively running
            monitoringState is MonitoringState.Running ->
                "Using real-time data from ${if (settings.selectedSimulation.isNotEmpty()) settings.selectedSimulation else "simulation"}"

            // If using filtered data from explorer with a specific mannequin
            !filteredVitalSigns.isEmpty() && selectedFilters.mannequin != null && selectedFilters.mannequin != "<all>" -> {
                val scenario = getMannequinScenario(selectedFilters.mannequin, selectedFilters)
                "Using filtered data for mannequin: ${selectedFilters.mannequin} (${scenario} scenario)"
            }

            // If using filtered data from explorer with a simulation filter
            !filteredVitalSigns.isEmpty() && selectedFilters.sim != null && selectedFilters.sim != "<all>" ->
                "Using filtered data from ${selectedFilters.sim}"

            // If general vital signs data exists
            !vitalSigns.isEmpty() ->
                "Using collected vital signs data"

            else -> "Select a scenario to analyze compliance"
        }

        Text(
            text = dataSourceText,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.primary
        )

        // Display active vital range filters notification if any are applied
        if (hasActiveVitalRangeFilters) {
            Spacer(modifier = Modifier.height(8.dp))

            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.7f)
                )
            ) {
                Column(modifier = Modifier.padding(12.dp)) {
                    Text(
                        text = "Vital Sign Range Filters Active",
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )

                    Text(
                        text = "Compliance is calculated only on vital signs within the filtered ranges. Visit Data Explorer to adjust filters.",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.8f)
                    )

                    // Display the active filters
                    Spacer(modifier = Modifier.height(8.dp))

                    // Heart Rate filter if active
                    if (vitalRangeFilters.heartRate.available &&
                        (vitalRangeFilters.heartRate.min > vitalRangeFilters.heartRate.defaultMin ||
                         vitalRangeFilters.heartRate.max < vitalRangeFilters.heartRate.defaultMax)) {
                        Text(
                            text = "• Heart Rate: ${vitalRangeFilters.heartRate.min.toInt()}-${vitalRangeFilters.heartRate.max.toInt()} bpm",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                    }

                    // SpO2 filter if active
                    if (vitalRangeFilters.spo2.available &&
                        (vitalRangeFilters.spo2.min > vitalRangeFilters.spo2.defaultMin ||
                         vitalRangeFilters.spo2.max < vitalRangeFilters.spo2.defaultMax)) {
                        Text(
                            text = "• SpO₂: ${vitalRangeFilters.spo2.min.toInt()}-${vitalRangeFilters.spo2.max.toInt()}%",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                    }

                    // NIBP Systolic filter if active
                    if (vitalRangeFilters.nibpSys.available &&
                        (vitalRangeFilters.nibpSys.min > vitalRangeFilters.nibpSys.defaultMin ||
                         vitalRangeFilters.nibpSys.max < vitalRangeFilters.nibpSys.defaultMax)) {
                        Text(
                            text = "• NIBP Systolic: ${vitalRangeFilters.nibpSys.min.toInt()}-${vitalRangeFilters.nibpSys.max.toInt()} mmHg",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                    }

                    // NIBP Diastolic filter if active
                    if (vitalRangeFilters.nibpDia.available &&
                        (vitalRangeFilters.nibpDia.min > vitalRangeFilters.nibpDia.defaultMin ||
                         vitalRangeFilters.nibpDia.max < vitalRangeFilters.nibpDia.defaultMax)) {
                        Text(
                            text = "• NIBP Diastolic: ${vitalRangeFilters.nibpDia.min.toInt()}-${vitalRangeFilters.nibpDia.max.toInt()} mmHg",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                    }

                    // MAP filter if active
                    if (vitalRangeFilters.nibpMap.available &&
                        (vitalRangeFilters.nibpMap.min > vitalRangeFilters.nibpMap.defaultMin ||
                         vitalRangeFilters.nibpMap.max < vitalRangeFilters.nibpMap.defaultMax)) {
                        Text(
                            text = "• MAP: ${vitalRangeFilters.nibpMap.min.toInt()}-${vitalRangeFilters.nibpMap.max.toInt()} mmHg",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                    }

                    // Temp filter if active
                    if (vitalRangeFilters.temp.available &&
                        (vitalRangeFilters.temp.min > vitalRangeFilters.temp.defaultMin ||
                         vitalRangeFilters.temp.max < vitalRangeFilters.temp.defaultMax)) {
                        Text(
                            text = "• Temperature: ${vitalRangeFilters.temp.min.toInt()}-${vitalRangeFilters.temp.max.toInt()} °C",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                    }

                    // Respiratory Rate filter if active
                    if (vitalRangeFilters.respRate.available &&
                        (vitalRangeFilters.respRate.min > vitalRangeFilters.respRate.defaultMin ||
                         vitalRangeFilters.respRate.max < vitalRangeFilters.respRate.defaultMax)) {
                        Text(
                            text = "• Respiratory Rate: ${vitalRangeFilters.respRate.min.toInt()}-${vitalRangeFilters.respRate.max.toInt()} breaths/min",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                    }

                    // EtCO2 filter if active
                    if (vitalRangeFilters.etco2.available &&
                        (vitalRangeFilters.etco2.min > vitalRangeFilters.etco2.defaultMin ||
                         vitalRangeFilters.etco2.max < vitalRangeFilters.etco2.defaultMax)) {
                        Text(
                            text = "• EtCO₂: ${vitalRangeFilters.etco2.min.toInt()}-${vitalRangeFilters.etco2.max.toInt()} mmHg",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(24.dp))

        // Scenario selector - only show when needed
        if (shouldShowScenarioSelector) {
            Text(
                text = "Select Clinical Scenario",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium
            )

            Spacer(modifier = Modifier.height(8.dp))

            ExposedDropdownMenuBox(
                expanded = expanded,
                onExpandedChange = { expanded = it }
            ) {
                OutlinedTextField(
                    value = selectedScenario,
                    onValueChange = {},
                    readOnly = true,
                    trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded) },
                    modifier = Modifier
                        .fillMaxWidth()
                        .menuAnchor()
                )

                ExposedDropdownMenu(
                    expanded = expanded,
                    onDismissRequest = { expanded = false }
                ) {
                    scenarios.forEach { scenario ->
                        DropdownMenuItem(
                            text = { Text(scenario) },
                            onClick = {
                                selectedScenario = scenario
                                viewModel.updateSettings(scenario = scenario)
                                expanded = false
                            }
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(24.dp))
        } else {
            // Show which scenario is being used without the dropdown
            Text(
                text = "Using scenario: $currentScenario",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium
            )

            Spacer(modifier = Modifier.height(24.dp))
        }

        // Display filtered data notice if vital range filters are active
        if (hasActiveVitalRangeFilters && filteredVitalSigns.isNotEmpty()) {
            Text(
                text = "Showing compliance for ${filteredVitalSigns.size} of ${vitalSigns.size} total records (${(filteredVitalSigns.size * 100f / vitalSigns.size).toInt()}%)",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.primary
            )

            Spacer(modifier = Modifier.height(16.dp))
        }

        // Compliance results
        if (vitalSigns.isEmpty() && filteredVitalSigns.isEmpty()) {
            // No data loaded
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(200.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "No vital signs data loaded.\nLoad data first to calculate compliance.",
                    textAlign = TextAlign.Center,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            }
        } else if (complianceStats.vitalStats.isEmpty()) {
            // No compliance data
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(200.dp),
                contentAlignment = Alignment.Center
            ) {
                if (hasActiveVitalRangeFilters && filteredVitalSigns.isEmpty()) {
                    Text(
                        text = "No vital signs match the current filter ranges.\nAdjust filters in Data Explorer to include data.",
                        textAlign = TextAlign.Center,
                        color = MaterialTheme.colorScheme.error
                    )
                } else {
                    Text(
                        text = "No compliance data available for the selected scenario.",
                        textAlign = TextAlign.Center,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                }
            }
        } else {
            // Display compliance data
            SectionHeader(title = "Compliance Results")

            // Overall compliance
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.Center
            ) {
                ComplianceStatusIndicator(
                    status = complianceStats.overallStatus,
                    score = complianceStats.overallScore
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Scenario details
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surface
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Scenario: ${complianceStats.scenario}",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = "Duration: ${String.format("%.1f", complianceStats.durationMin)} minutes",
                        style = MaterialTheme.typography.bodyLarge
                    )

                    if (complianceStats.overallStatus != ComplianceStatus.UNKNOWN) {
                        Spacer(modifier = Modifier.height(8.dp))

                        val statusText = when (complianceStats.overallStatus) {
                            ComplianceStatus.EXCELLENT -> "CPG guidelines were consistently met"
                            ComplianceStatus.GOOD -> "CPG guidelines were generally followed with some deviations"
                            ComplianceStatus.FAIR -> "Significant deviations from CPG guidelines"
                            ComplianceStatus.POOR -> "Major deviations from CPG guidelines"
                            else -> ""
                        }

                        val statusColor = when (complianceStats.overallStatus) {
                            ComplianceStatus.EXCELLENT -> ExcellentColor
                            ComplianceStatus.GOOD -> GoodColor
                            ComplianceStatus.FAIR -> FairColor
                            ComplianceStatus.POOR -> PoorColor
                            else -> UnknownColor
                        }

                        Text(
                            text = statusText,
                            style = MaterialTheme.typography.bodyLarge,
                            color = statusColor
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(24.dp))

            // Non-Compliance Events Log
            if (complianceStats.nonComplianceEvents.isNotEmpty()) {
                // Track expanded state
                var nonComplianceEventsExpanded by remember { mutableStateOf(true) }

                // Add timeline view of non-compliance events
                Text(
                    text = "Non-Compliance Timeline",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(vertical = 8.dp)
                )

                // Get the session start and end times from the events
                val sessionStartTime = complianceStats.nonComplianceEvents.minByOrNull { it.startTime }?.startTime ?: Date()
                val sessionEndTime = complianceStats.nonComplianceEvents.maxByOrNull { it.endTime }?.endTime ?: Date()

                NonComplianceTimeline(
                    nonComplianceEvents = complianceStats.nonComplianceEvents,
                    sessionStartTime = sessionStartTime,
                    sessionEndTime = sessionEndTime
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Collapsible section header
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.secondaryContainer
                    )
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { nonComplianceEventsExpanded = !nonComplianceEventsExpanded }
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Column {
                            Text(
                                text = "Non-Compliance Events Log",
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.Bold
                            )

                            Text(
                                text = "${complianceStats.nonComplianceEvents.size} events detected",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                            )
                        }

                        // Animated arrow icon
                        Icon(
                            imageVector = Icons.Default.KeyboardArrowDown,
                            contentDescription = if (nonComplianceEventsExpanded) "Collapse" else "Expand",
                            modifier = Modifier
                                .size(24.dp)
                                .rotate(if (nonComplianceEventsExpanded) 180f else 0f)
                        )
                    }
                }

                // Animated collapsible content
                AnimatedVisibility(
                    visible = nonComplianceEventsExpanded,
                    enter = expandVertically() + fadeIn(),
                    exit = shrinkVertically() + fadeOut()
                ) {
                    Column(
                        modifier = Modifier.padding(top = 8.dp)
                    ) {
                        Text(
                            text = "This log shows periods when vital signs were outside of CPG thresholds",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                        )

                        Spacer(modifier = Modifier.height(8.dp))

                        // Use our new NonComplianceEventList component
                        NonComplianceEventList(
                            nonComplianceEvents = complianceStats.nonComplianceEvents,
                            onEventClick = { event ->
                                // Optional: Add annotation for this event
                                viewModel.addAnnotation(
                                    text = "Non-compliant ${event.vitalSign}: ${event.values.average().toInt()}",
                                    type = AnnotationType.CRITICAL_ALERT,
                                    vitalSign = event.vitalSign,
                                    mannequin = event.mannequin
                                )
                            }
                        )
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))
            }

            // Vital signs details
            SectionHeader(title = "Vital Signs")

            complianceStats.vitalStats.forEach { (name, stat) ->
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surface
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = name,
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.Bold
                            )

                            val color = when (stat.status) {
                                ComplianceStatus.EXCELLENT -> ExcellentColor
                                ComplianceStatus.GOOD -> GoodColor
                                ComplianceStatus.FAIR -> FairColor
                                ComplianceStatus.POOR -> PoorColor
                                else -> UnknownColor
                            }

                            Text(
                                text = "${stat.inRangePercent.toInt()}%",
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.Bold,
                                color = color
                            )
                        }

                        Spacer(modifier = Modifier.height(8.dp))

                        Text(
                            text = "Target: ${stat.target}",
                            style = MaterialTheme.typography.bodyMedium
                        )

                        Text(
                            text = "Actual range: ${stat.actualRange}",
                            style = MaterialTheme.typography.bodyMedium
                        )

                        Text(
                            text = "Mean value: ${String.format("%.1f", stat.meanValue)}",
                            style = MaterialTheme.typography.bodyMedium
                        )

                        Spacer(modifier = Modifier.height(4.dp))

                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                text = "Time in range: ${String.format("%.1f", stat.timeMonitoredMin - stat.timeOutOfRangeMin)} min",
                                style = MaterialTheme.typography.bodySmall
                            )

                            Text(
                                text = "Time out of range: ${String.format("%.1f", stat.timeOutOfRangeMin)} min",
                                style = MaterialTheme.typography.bodySmall,
                                color = if (stat.timeOutOfRangeMin > 0) Danger else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                            )
                        }
                    }
                }
            }
        }

        // Bottom padding for better scrolling
        Spacer(modifier = Modifier.height(60.dp))
    }
}

/**
 * Helper function to get the scenario for a mannequin
 */
private fun getMannequinScenario(mannequin: String?, selectedFilters: SelectedFilters): String {
    if (mannequin == null) return "TBI" // Default

    // If we have a selected simulation and mannequin, use MannequinConfig to get the correct scenario
    if (selectedFilters.sim != null && selectedFilters.sim != "<all>") {
        return MannequinConfig.getScenarioForMannequin(selectedFilters.sim, mannequin)
    }

    // Fall back to hardcoded defaults if no simulation is selected
    return when (mannequin) {
        "Dave" -> "TBI"
        "Chuck" -> "Pneumonia" // Updated from Sepsis to Pneumonia
        "Freddy" -> "TBI_unmonitored"
        "Matt" -> "PenetratingThoracoabdominalInjury"
        "Oscar" -> "DCR"
        else -> "TBI" // Default
    }
}