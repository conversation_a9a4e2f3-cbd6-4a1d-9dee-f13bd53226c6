package com.example.myapplication.ui.util

import android.content.Context
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

/**
 * Utility class for handling screen dimensions and adaptations
 */
object ScreenUtils {
    /**
     * Get the screen width in dp
     */
    @Composable
    fun getScreenWidthDp(): Dp {
        val configuration = LocalConfiguration.current
        return with(LocalDensity.current) {
            configuration.screenWidthDp.dp
        }
    }

    /**
     * Get the screen height in dp
     */
    @Composable
    fun getScreenHeightDp(): Dp {
        val configuration = LocalConfiguration.current
        return with(LocalDensity.current) {
            configuration.screenHeightDp.dp
        }
    }

    /**
     * Check if the device is a tablet
     */
    @Composable
    fun isTablet(): Boolean {
        val configuration = LocalConfiguration.current
        return configuration.screenWidthDp >= 600
    }

    /**
     * Get the screen density
     */
    @Composable
    fun getScreenDensity(): Float {
        val context = LocalContext.current
        return remember(context) {
            context.resources.displayMetrics.density
        }
    }

    /**
     * Calculate adaptive padding based on screen size
     * @param basePadding The base padding value
     * @param minPadding The minimum padding value
     * @param maxPadding The maximum padding value
     */
    @Composable
    fun calculateAdaptivePadding(basePadding: Dp, minPadding: Dp = 4.dp, maxPadding: Dp = 32.dp): Dp {
        val screenWidth = getScreenWidthDp()
        val density = getScreenDensity()
        
        // Calculate adaptive padding based on screen width and density
        val calculatedPadding = (basePadding.value * (screenWidth.value / 360f) * (density / 2.75f)).dp
        
        // Ensure the padding is within the specified range
        return calculatedPadding.coerceIn(minPadding, maxPadding)
    }

    /**
     * Calculate adaptive font size based on screen density
     * @param baseSize The base font size
     * @param minSize The minimum font size
     * @param maxSize The maximum font size
     */
    @Composable
    fun calculateAdaptiveFontSize(baseSize: Float, minSize: Float = 8f, maxSize: Float = 24f): Float {
        val density = getScreenDensity()
        
        // Calculate adaptive font size based on density
        val calculatedSize = baseSize * (density / 2.75f)
        
        // Ensure the font size is within the specified range
        return calculatedSize.coerceIn(minSize, maxSize)
    }

    /**
     * Convert dp to pixels
     */
    fun dpToPx(context: Context, dp: Float): Float {
        return dp * context.resources.displayMetrics.density
    }

    /**
     * Convert pixels to dp
     */
    fun pxToDp(context: Context, px: Float): Float {
        return px / context.resources.displayMetrics.density
    }
}
