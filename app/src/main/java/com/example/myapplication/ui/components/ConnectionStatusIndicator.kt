package com.example.myapplication.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.example.myapplication.data.repository.VitalSignsRepository.ConnectionStatus

/**
 * Indicator for connection status
 * 
 * @param connectionStatus The current connection status
 * @param showText Whether to show the status text next to the indicator
 */
@Composable
fun ConnectionStatusIndicator(
    connectionStatus: ConnectionStatus,
    showText: Boolean = true
) {
    val (color, text) = when (connectionStatus) {
        ConnectionStatus.CONNECTED -> Color.Green to "Connected"
        ConnectionStatus.CONNECTING -> Color.Yellow to "Connecting..."
        ConnectionStatus.DISCONNECTED -> Color.Gray to "Disconnected"
        ConnectionStatus.ERROR -> Color.Red to "Error"
    }
    
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.padding(4.dp)
    ) {
        // Status indicator dot
        Box(
            modifier = Modifier
                .size(12.dp)
                .background(color, CircleShape)
        )
        
        // Optional status text
        if (showText) {
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = text,
                style = MaterialTheme.typography.bodySmall,
                fontWeight = FontWeight.Medium
            )
        }
    }
} 