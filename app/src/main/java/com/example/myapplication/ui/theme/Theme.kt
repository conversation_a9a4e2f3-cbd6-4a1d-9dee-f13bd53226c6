package com.example.myapplication.ui.theme

import android.app.Activity
import android.os.Build
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.dynamicDarkColorScheme
import androidx.compose.material3.dynamicLightColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.core.view.WindowCompat

private val DarkColorScheme = darkColorScheme(
    // Primary colors
    primary = Primary,
    onPrimary = Color.White,
    primaryContainer = PrimaryDark,
    onPrimaryContainer = Color.White,

    // Secondary colors
    secondary = Secondary,
    onSecondary = Color.White,
    secondaryContainer = SecondaryDark,
    onSecondaryContainer = Color.White,

    // Tertiary colors
    tertiary = Accent,
    onTertiary = Color.White,
    tertiaryContainer = PrimaryLight,
    onTertiaryContainer = Color.White,

    // Background and surface colors
    background = NeutralDark,
    onBackground = Color.White,
    surface = Color(0xFF1C2731), // Slightly lighter than background
    onSurface = Color.White,
    surfaceVariant = Color(0xFF2B3740), // Even lighter for variant surfaces
    onSurfaceVariant = Color.White.copy(alpha = 0.8f),

    // Error colors
    error = Danger,
    onError = Color.White,
    errorContainer = Danger.copy(alpha = 0.2f),
    onErrorContainer = Color.White,

    // Other colors
    outline = Color.White.copy(alpha = 0.2f),
    outlineVariant = Color.White.copy(alpha = 0.1f),
    scrim = Color.Black.copy(alpha = 0.3f)
)

private val LightColorScheme = lightColorScheme(
    // Primary colors
    primary = Primary,
    onPrimary = Color.White,
    primaryContainer = PrimaryContainer,
    onPrimaryContainer = OnPrimaryContainer,

    // Secondary colors
    secondary = Secondary,
    onSecondary = Color.White,
    secondaryContainer = SecondaryContainer,
    onSecondaryContainer = SecondaryDark,

    // Tertiary colors
    tertiary = Accent,
    onTertiary = Color.White,
    tertiaryContainer = PrimaryLight.copy(alpha = 0.2f),
    onTertiaryContainer = PrimaryDark,

    // Background and surface colors
    background = Background,
    onBackground = NeutralDark,
    surface = Surface,
    onSurface = NeutralDark,
    surfaceVariant = SurfaceVariant,
    onSurfaceVariant = Neutral,

    // Error colors
    error = Danger,
    onError = Color.White,
    errorContainer = Danger.copy(alpha = 0.1f),
    onErrorContainer = Danger,

    // Other colors
    outline = Neutral,
    outlineVariant = NeutralLight,
    scrim = Color.Black.copy(alpha = 0.3f)
)

@Composable
fun MyApplicationTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    // Dynamic color is available on Android 12+
    dynamicColor: Boolean = false,
    content: @Composable () -> Unit
) {
    val colorScheme = when {
        dynamicColor && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
            val context = LocalContext.current
            if (darkTheme) dynamicDarkColorScheme(context) else dynamicLightColorScheme(context)
        }

        darkTheme -> DarkColorScheme
        else -> LightColorScheme
    }
    val view = LocalView.current
    if (!view.isInEditMode) {
        SideEffect {
            val window = (view.context as Activity).window

            // Set status bar color to match the app theme for a more professional look
            // Using the primary color for a cohesive appearance
            window.statusBarColor = colorScheme.primary.toArgb()

            // For Samsung devices with Android 14, we need to ensure the navigation bar is visible
            // Use a solid color for the navigation bar to ensure it's visible
            window.navigationBarColor = if (darkTheme) {
                Color(0xFF000000).toArgb() // Pure black for dark theme
            } else {
                Color(0xFFFFFFFF).toArgb() // Pure white for light theme
            }

            // Set the appearance of status bar icons based on theme
            WindowCompat.getInsetsController(window, view).apply {
                // Light status bar icons only in light theme
                isAppearanceLightStatusBars = false // Always use light icons on primary color
                isAppearanceLightNavigationBars = !darkTheme

                // Ensure the system bars are shown - critical for Samsung devices
                isAppearanceLightNavigationBars = !darkTheme

                // Don't hide the navigation bar
                systemBarsBehavior = WindowCompat.getInsetsController(window, view).systemBarsBehavior
            }
        }
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = Typography,
        content = content
    )
}