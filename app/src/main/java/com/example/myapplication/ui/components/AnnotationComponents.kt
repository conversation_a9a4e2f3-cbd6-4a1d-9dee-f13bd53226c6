package com.example.myapplication.ui.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Event
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.Note
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.unit.dp
import com.example.myapplication.data.model.Annotation
import com.example.myapplication.data.model.AnnotationType
import java.text.SimpleDateFormat
import java.util.Locale

/**
 * Input component for adding annotations
 */
@Composable
fun AnnotationInput(
    onAddAnnotation: (String, AnnotationType) -> Unit
) {
    var annotationText by remember { mutableStateOf("") }
    var selectedType by remember { mutableStateOf(AnnotationType.NOTE) }
    var showTypeDropdown by remember { mutableStateOf(false) }
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(8.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier.padding(8.dp)
        ) {
            Text(
                text = "Add Annotation",
                style = MaterialTheme.typography.titleMedium,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            
            OutlinedTextField(
                value = annotationText,
                onValueChange = { annotationText = it },
                placeholder = { Text("Add note or event...") },
                modifier = Modifier.fillMaxWidth()
            )
            
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Type selector
                Row(
                    modifier = Modifier
                        .clickable { showTypeDropdown = true }
                        .padding(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = getIconForAnnotationType(selectedType),
                        contentDescription = "Annotation type"
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(text = selectedType.name)
                    
                    DropdownMenu(
                        expanded = showTypeDropdown,
                        onDismissRequest = { showTypeDropdown = false }
                    ) {
                        AnnotationType.values().forEach { type ->
                            DropdownMenuItem(
                                text = { Text(type.name) },
                                leadingIcon = {
                                    Icon(
                                        imageVector = getIconForAnnotationType(type),
                                        contentDescription = type.name
                                    )
                                },
                                onClick = {
                                    selectedType = type
                                    showTypeDropdown = false
                                }
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.weight(1f))
                
                Button(
                    onClick = {
                        if (annotationText.isNotEmpty()) {
                            onAddAnnotation(annotationText, selectedType)
                            annotationText = ""
                        }
                    }
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "Add annotation"
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("Add")
                }
            }
        }
    }
}

/**
 * List of annotations
 */
@Composable
fun AnnotationsList(
    annotations: List<Annotation>,
    onDeleteAnnotation: (String) -> Unit = {},
    onEditAnnotation: (Annotation) -> Unit = {}
) {
    LazyColumn {
        items(annotations.sortedByDescending { it.timestamp }) { annotation ->
            AnnotationItem(
                annotation = annotation,
                onDelete = { onDeleteAnnotation(annotation.id) },
                onEdit = { onEditAnnotation(annotation) }
            )
        }
    }
}

/**
 * Single annotation item
 */
@Composable
fun AnnotationItem(
    annotation: Annotation,
    onDelete: () -> Unit = {},
    onEdit: () -> Unit = {}
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        colors = CardDefaults.cardColors(
            containerColor = when (annotation.type) {
                AnnotationType.NOTE -> MaterialTheme.colorScheme.surfaceVariant
                AnnotationType.EVENT -> MaterialTheme.colorScheme.primaryContainer
                AnnotationType.INTERVENTION -> MaterialTheme.colorScheme.secondaryContainer
                AnnotationType.CRITICAL_ALERT -> MaterialTheme.colorScheme.errorContainer
            }
        )
    ) {
        Row(
            modifier = Modifier.padding(8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = getIconForAnnotationType(annotation.type),
                contentDescription = annotation.type.name,
                tint = when (annotation.type) {
                    AnnotationType.NOTE -> MaterialTheme.colorScheme.onSurfaceVariant
                    AnnotationType.EVENT -> MaterialTheme.colorScheme.onPrimaryContainer
                    AnnotationType.INTERVENTION -> MaterialTheme.colorScheme.onSecondaryContainer
                    AnnotationType.CRITICAL_ALERT -> MaterialTheme.colorScheme.onErrorContainer
                }
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = annotation.text,
                    style = MaterialTheme.typography.bodyMedium
                )
                
                Row {
                    Text(
                        text = SimpleDateFormat("HH:mm:ss", Locale.US).format(annotation.timestamp),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                    
                    if (annotation.mannequin != null) {
                        Text(
                            text = " | ${annotation.mannequin}",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                        )
                    }
                    
                    if (annotation.vitalSign != null) {
                        Text(
                            text = " | ${annotation.vitalSign}",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                        )
                    }
                }
            }
            
            IconButton(onClick = onEdit) {
                Icon(
                    imageVector = Icons.Default.Edit,
                    contentDescription = "Edit annotation"
                )
            }
            
            IconButton(onClick = onDelete) {
                Icon(
                    imageVector = Icons.Default.Delete,
                    contentDescription = "Delete annotation"
                )
            }
        }
    }
}

/**
 * Get the appropriate icon for an annotation type
 */
@Composable
fun getIconForAnnotationType(type: AnnotationType): ImageVector {
    return when (type) {
        AnnotationType.NOTE -> Icons.Default.Note
        AnnotationType.EVENT -> Icons.Default.Event
        AnnotationType.INTERVENTION -> Icons.Default.Info
        AnnotationType.CRITICAL_ALERT -> Icons.Default.Warning
    }
}
