package com.example.myapplication.ui.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.example.myapplication.data.model.VitalSign
import com.example.myapplication.data.model.VitalSignStatus
import com.example.myapplication.data.repository.VitalSignsRepository.ConnectionStatus
import com.example.myapplication.ui.components.VitalSignCardData

/**
 * Card component to display a mannequin's vital signs with status indicators
 *
 * @param mannequinName The name of the mannequin
 * @param scenario The scenario being used
 * @param vitalSign The latest vital sign data
 * @param connectionStatus The connection status to the mannequin
 */
@Composable
fun MannequinMonitorCard(
    mannequinName: String,
    scenario: String,
    vitalSign: VitalSign?,
    connectionStatus: ConnectionStatus
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(12.dp), // Increased padding for better spacing
        shape = RoundedCornerShape(16.dp), // More rounded corners for a modern medical look
        elevation = CardDefaults.cardElevation(
            defaultElevation = 3.dp // Slightly reduced elevation for a cleaner look
        ),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface, // Ensure consistent surface color
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // Header with mannequin name, scenario, and connection status
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = mannequinName,
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onSurface // Ensure consistent text color
                    )

                    Text(
                        text = "Scenario: $scenario",
                        style = MaterialTheme.typography.bodyMedium,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        color = MaterialTheme.colorScheme.onSurfaceVariant // Slightly muted color for secondary text
                    )
                }

                ConnectionStatusIndicator(connectionStatus = connectionStatus)
            }

            Spacer(modifier = Modifier.height(12.dp)) // Increased spacing
            HorizontalDivider(
                color = MaterialTheme.colorScheme.outlineVariant, // Use theme color for divider
                thickness = 1.dp // Thinner divider for a more refined look
            )
            Spacer(modifier = Modifier.height(12.dp)) // Increased spacing

            if (vitalSign == null || connectionStatus != ConnectionStatus.CONNECTED) {
                // No data or not connected - improved empty state
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = if (connectionStatus == ConnectionStatus.CONNECTING) {
                            "Connecting..."
                        } else if (connectionStatus == ConnectionStatus.DISCONNECTED) {
                            "Disconnected"
                        } else {
                            "No data available"
                        },
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        fontWeight = FontWeight.Medium
                    )

                    Spacer(modifier = Modifier.height(4.dp))

                    Text(
                        text = if (connectionStatus == ConnectionStatus.CONNECTING) {
                            "Attempting to establish connection..."
                        } else if (connectionStatus == ConnectionStatus.DISCONNECTED) {
                            "Check connection settings and try again"
                        } else {
                            "Waiting for vital signs data"
                        },
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f),
                        textAlign = TextAlign.Center
                    )
                }
            } else {
                // First row: HR, SpO2, NIBP
                VitalSignsRow(
                    vitalSigns = listOf(
                        VitalSignCardData(
                            label = "HR",
                            value = vitalSign.hr,
                            unit = "bpm",
                            status = vitalSign.vitalSignStatuses["hr"] ?: VitalSignStatus.UNKNOWN
                        ),
                        VitalSignCardData(
                            label = "SpO₂",  // Proper subscript for O2
                            value = vitalSign.spO2,
                            unit = "%",
                            status = vitalSign.vitalSignStatuses["spo2"] ?: VitalSignStatus.UNKNOWN
                        ),
                        // Special handling for NIBP to show systolic/diastolic properly
                        createNIBPCardData(
                            systolic = vitalSign.nibpSys,
                            diastolic = vitalSign.nibpDia,
                            status = vitalSign.vitalSignStatuses["nibp_sys"] ?: VitalSignStatus.UNKNOWN
                        )
                    )
                )

                Spacer(modifier = Modifier.height(16.dp)) // Increased spacing between rows

                // Second row: Temp, RR, EtCO2
                VitalSignsRow(
                    vitalSigns = listOf(
                        VitalSignCardData(
                            label = "Temp",
                            value = vitalSign.temp1,
                            unit = "°C",
                            status = vitalSign.vitalSignStatuses["temp"] ?: VitalSignStatus.UNKNOWN
                        ),
                        VitalSignCardData(
                            label = "RR",
                            value = vitalSign.respRate,
                            unit = "bpm",
                            status = vitalSign.vitalSignStatuses["resp_rate"] ?: VitalSignStatus.UNKNOWN
                        ),
                        VitalSignCardData(
                            label = "EtCO₂", // Proper subscript for CO2
                            value = vitalSign.etCO2,
                            unit = "mmHg",
                            status = vitalSign.vitalSignStatuses["etco2"] ?: VitalSignStatus.UNKNOWN
                        )
                    )
                )
            }
        }
    }
}

/**
 * Helper function to create a properly formatted NIBP card data
 * This ensures systolic/diastolic values are displayed correctly
 */
private fun createNIBPCardData(
    systolic: Double?,
    diastolic: Double?,
    status: VitalSignStatus
): VitalSignCardData {
    // Format the display value to show systolic
    val displayValue = systolic

    // Format the unit to show "/ diastolic"
    val unitText = if (diastolic != null && systolic != null) {
        "/ ${diastolic.toInt()}"
    } else {
        "/ --"
    }

    return VitalSignCardData(
        label = "NIBP",
        value = displayValue,
        unit = unitText,
        status = status
    )
}