package com.example.myapplication.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import com.example.myapplication.data.ClinicalPracticeGuidelines
import com.example.myapplication.data.model.ActiveNonComplianceEvent
import com.example.myapplication.data.model.Annotation
import com.example.myapplication.data.model.AnnotationType
import com.example.myapplication.data.model.ClinicalEvent
import com.example.myapplication.data.model.VitalSign
import com.example.myapplication.data.repository.VitalSignsRepository
import com.example.myapplication.data.repository.VitalSignsRepository.ConnectionStatus
import com.example.myapplication.data.repository.VitalSignsRepository.DataSourceType
import com.example.myapplication.data.repository.VitalSignsRepository.MonitorSettings
import com.example.myapplication.ui.state.DataLoadingState
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.util.Date
import com.example.myapplication.data.model.MannequinConfig
import com.example.myapplication.data.model.NonComplianceEvent
import kotlinx.coroutines.delay
import android.content.Context
import android.net.Uri
import androidx.documentfile.provider.DocumentFile
import com.example.myapplication.data.SimulationScenarios
import android.util.Log
import com.example.myapplication.util.CrashLogger
import java.text.SimpleDateFormat
import java.util.Locale
import com.example.myapplication.ui.screens.SortConfig
import java.io.OutputStreamWriter
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.setValue
import com.example.myapplication.data.util.SimulationTimeDetector
import kotlinx.coroutines.Job

/**
 * ViewModel for vital signs monitoring and analysis
 */
class VitalSignsViewModel(
    val repository: VitalSignsRepository,
    private val context: Context
) : ViewModel() {

    companion object {
        private const val TAG = "VitalSignsViewModel"
    }

    // Data loading state
    private val _dataLoadingState = MutableStateFlow<DataLoadingState>(DataLoadingState.Idle)
    val dataLoadingState: StateFlow<DataLoadingState> = _dataLoadingState.asStateFlow()

    // Monitoring state
    private val _monitoringState = MutableStateFlow<MonitoringState>(MonitoringState.Stopped)
    val monitoringState: StateFlow<MonitoringState> = _monitoringState.asStateFlow()

    // Current tab
    private val _currentTab = MutableStateFlow(VitalSignsTab.HOME)
    val currentTab: StateFlow<VitalSignsTab> = _currentTab.asStateFlow()

    // Settings from repository
    val settings = repository.settings

    // Latest vital sign
    val latestVitalSign = repository.latestVitalSign

    // All vital signs
    val vitalSigns = repository.vitalSigns

    // Clinical events
    val clinicalEvents = repository.clinicalEvents

    // Connection status
    val connectionStatus = repository.connectionStatus

    // Latest vital sign data by mannequin
    val latestVitalSignsByMannequin = repository.latestVitalSignsByMannequin

    // Connection status by mannequin
    val connectionStatuses = repository.connectionStatuses

    // State for the compliance statistics
    private val _complianceStats = MutableStateFlow(ComplianceStats())
    val complianceStats: StateFlow<ComplianceStats> = _complianceStats.asStateFlow()

    // Filter state
    private val _filteredVitalSigns = MutableStateFlow<List<VitalSign>>(emptyList())
    val filteredVitalSigns: StateFlow<List<VitalSign>> = _filteredVitalSigns.asStateFlow()

    // Filter values for UI
    private val _filterValues = MutableStateFlow<VitalSignsRepository.FilterValues?>(null)
    val filterValues: StateFlow<VitalSignsRepository.FilterValues?> = _filterValues.asStateFlow()

    // Selected filters
    private val _selectedFilters = MutableStateFlow(SelectedFilters())
    val selectedFilters: StateFlow<SelectedFilters> = _selectedFilters.asStateFlow()

    // Stats summary
    private val _statsSummary = MutableStateFlow<Map<String, VitalSignsRepository.StatsSummary>>(emptyMap())
    val statsSummary: StateFlow<Map<String, VitalSignsRepository.StatsSummary>> = _statsSummary.asStateFlow()

    // Database record count
    private val _databaseRecordCount = MutableStateFlow(0)
    val databaseRecordCount: StateFlow<Int> = _databaseRecordCount.asStateFlow()

    // Database availability flag
    private val _isDatabaseAvailable = MutableStateFlow(true)
    val isDatabaseAvailable: Boolean
        get() = _isDatabaseAvailable.value

    // Store the actual detected timeframe (start/end Date objects)
    private val _actualDetectedTimeframe = MutableStateFlow<Pair<Date, Date>?>(null)
    val actualDetectedTimeframe: StateFlow<Pair<Date, Date>?> = _actualDetectedTimeframe.asStateFlow()

    // Detected timeframe summary - maps mannequin names to (start time, end time) pairs
    private val _detectedTimeframeSummary = MutableStateFlow<Map<String, Pair<String, String>>>(emptyMap())
    val detectedTimeframeSummary: StateFlow<Map<String, Pair<String, String>>> = _detectedTimeframeSummary

    // *** NEW STATE: Holds the fully processed data within the detected timeframe ***
    private val _processedDataWithinTimeframe = MutableStateFlow<List<VitalSign>>(emptyList())

    // Sort configuration for the vital signs table
    private val _sortConfig = MutableStateFlow<SortConfig?>(null)
    val sortConfig: StateFlow<SortConfig?> = _sortConfig

    // Vital range filters
    private val _vitalRangeFilters = MutableStateFlow(VitalRangeFilters())
    val vitalRangeFilters: StateFlow<VitalRangeFilters> = _vitalRangeFilters

    // Available vital signs in the current dataset
    private val _availableVitalSigns = MutableStateFlow<Set<String>>(emptySet())
    val availableVitalSigns: StateFlow<Set<String>> = _availableVitalSigns

    // Data loaded flag
    private val _dataLoaded = MutableStateFlow(false)
    val dataLoaded: StateFlow<Boolean> = _dataLoaded.asStateFlow()

    // Active non-compliance events
    val activeNonComplianceEvents = repository.activeNonComplianceEvents

    // Completed non-compliance events
    val nonComplianceEvents = repository.nonComplianceEvents

    // Annotations
    val annotations = repository.annotations

    // To store the Job from listenToRepositoryUpdates
    private var repositoryUpdatesJob: Job? = null

    init {
        Log.d(TAG, "Initializing VitalSignsViewModel")

        // Check database availability
        checkDatabaseAvailability()

        // Calculate compliance stats when settings change
        viewModelScope.launch {
            repository.settings.collect { settings ->
                val vitalSignsData = repository.vitalSigns.value
                if (vitalSignsData.isNotEmpty()) {
                    // First try with precise timeframe
                    val preciseStats = calculateComplianceStats(vitalSignsData, settings.scenario, true)

                    // If we didn't get any results with precise timeframe, fall back to non-precise
                    if (preciseStats.vitalStats.isEmpty()) {
                        _complianceStats.value = calculateComplianceStats(vitalSignsData, settings.scenario, false)
                    } else {
                        _complianceStats.value = preciseStats
                    }
                } else {
                    _complianceStats.value = ComplianceStats(scenario = settings.scenario)
                }
            }
        }

        // Also recalculate compliance stats when vital signs data changes
        viewModelScope.launch {
            repository.vitalSigns.collect { signs ->
                if (signs.isNotEmpty()) {
                    // First try with precise timeframe
                    val preciseStats = calculateComplianceStats(signs, settings.value.scenario, true)

                    // If we didn't get any results with precise timeframe, fall back to non-precise
                    if (preciseStats.vitalStats.isEmpty()) {
                        _complianceStats.value = calculateComplianceStats(signs, settings.value.scenario, false)
                    } else {
                        _complianceStats.value = preciseStats
                    }
                }
            }
        }

        // Try to get the database record count on initialization
        getDatabaseRecordCount()
    }

    /**
     * Update the current tab
     * @param tab The new tab
     */
    fun updateCurrentTab(tab: VitalSignsTab) {
        val previousTab = _currentTab.value
        _currentTab.value = tab

        // Always check if we're navigating to the data explorer
        if (tab == VitalSignsTab.DATA_EXPLORER) {
            viewModelScope.launch {
                // If we have vital signs data but no filter values, update them
                if (vitalSigns.value.isNotEmpty() && filterValues.value == null) {
                    _filterValues.value = repository.getUniqueFilterValues()
                }

                // If we have vital signs data but no filtered data, refresh it
                if (vitalSigns.value.isNotEmpty() && filteredVitalSigns.value.isEmpty()) {
                    // Restore the filtered data based on the selected filters
                    val currentFilters = _selectedFilters.value

                    if (currentFilters != SelectedFilters()) {
                        // If we have filters, apply them
                        applyFilters()
                    } else {
                        // Otherwise, show all valid data
                        val allData = vitalSigns.value
                        val filteredData = allData.filter {
                            it.inPreciseSimTimeframe || it.inSimWindow
                        }

                        if (filteredData.isNotEmpty()) {
                            _filteredVitalSigns.value = filteredData
                            _statsSummary.value = repository.calculateStatsSummary(filteredData)
                        }
                    }
                }
            }
        }
    }

    /**
     * Load data from a directory
     * @param directoryPath The path to the directory or URI string
     */
    fun loadDataFromDirectory(directoryPath: String) {
        viewModelScope.launch {
            _dataLoadingState.value = DataLoadingState.Loading("Loading data from directory...")
            var loadedSigns: List<VitalSign> = emptyList() // Store raw loaded signs
            try {
                // Determine if it's a URI or a path
                val isUri = directoryPath.startsWith("content://")

                // Load RAW data using the repository
                loadedSigns = withContext(Dispatchers.IO) {
                    if (isUri) {
                        val uri = Uri.parse(directoryPath)
                        repository.loadDataFromDocumentUri(uri, context)
                    } else {
                        val directory = File(directoryPath)
                        if (!directory.exists() || !directory.isDirectory) {
                            throw Exception("Invalid directory path: $directoryPath")
                        }
                        repository.loadDataFromDirectory(directory)
                    }
                }

                _dataLoaded.value = true
                Log.i(TAG, "Successfully loaded ${loadedSigns.size} raw records.")

                // *** Perform processing and timeframe detection HERE in ViewModel ***
                processLoadedDataAndUpdateStates(loadedSigns)

                _dataLoadingState.value = DataLoadingState.Success(loadedSigns.size)

            } catch (e: Exception) {
                Log.e(TAG, "Error loading data from directory", e)
                CrashLogger.logError(TAG, "Error loading directory data: ${e.message}", e)
                _dataLoadingState.value = DataLoadingState.Error(e.message ?: "Unknown error loading directory")
                _dataLoaded.value = false
                // Clear states on error?
                // clearDataStates()
            } finally {
                // Stop loading indicator if not already stopped
                if (_dataLoadingState.value is DataLoadingState.Loading) {
                    _dataLoadingState.value = DataLoadingState.Idle
                }
            }
        }
    }

    /**
     * Load data from a single file URI
     * @param fileUriString The URI of the file as a string
     */
    fun loadDataFromFile(fileUriString: String) {
        viewModelScope.launch {
            _dataLoadingState.value = DataLoadingState.Loading("Loading data from file...")
            var loadedSigns: List<VitalSign> = emptyList() // Store raw loaded signs
            try {
                val uri = Uri.parse(fileUriString)

                // Load RAW data using the repository
                loadedSigns = withContext(Dispatchers.IO) {
                        repository.loadDataFromFileUri(uri, context)
                }

                _dataLoaded.value = true
                Log.i(TAG, "Successfully loaded ${loadedSigns.size} raw records from file.")

                // *** Perform processing and timeframe detection HERE in ViewModel ***
                processLoadedDataAndUpdateStates(loadedSigns)

                _dataLoadingState.value = DataLoadingState.Success(loadedSigns.size)

            } catch (e: Exception) {
                Log.e(TAG, "Error loading data from file", e)
                CrashLogger.logError(TAG, "Error loading file data: ${e.message}", e)
                _dataLoadingState.value = DataLoadingState.Error(e.message ?: "Unknown error loading file")
                _dataLoaded.value = false
                // Clear states on error?
                // clearDataStates()
            } finally {
                 // Stop loading indicator if not already stopped
                if (_dataLoadingState.value is DataLoadingState.Loading) {
                    _dataLoadingState.value = DataLoadingState.Idle
                }
            }
        }
    }

    /**
     * Common function to process loaded raw data, detect timeframe, and update all relevant states.
     */
    private fun processLoadedDataAndUpdateStates(rawVitalSigns: List<VitalSign>) {
        if (rawVitalSigns.isEmpty()) {
            Log.w(TAG, "processLoadedDataAndUpdateStates called with empty data list.")
            clearDataStates() // Clear states if no data loaded
            return
        }

        Log.d(TAG, "Processing ${rawVitalSigns.size} raw vital signs...")
        // *** ADD LOGGING ***
        Log.d("ViewModelProcessData", "processLoadedDataAndUpdateStates: START - Input rawVitalSigns size: ${rawVitalSigns.size}")

        // 1. Detect Timeframe using SimulationTimeDetector
        val firstTimestamp = rawVitalSigns.minByOrNull { it.timeObj }?.timeObj
        var detectedStartTime: Date? = null
        var detectedEndTime: Date? = null
        val timeframeSummaryMap = mutableMapOf<String, Pair<String, String>>()
        var detectorResult: Pair<String, Pair<Date, Date>>? = null // Declare detectorResult here

        if (firstTimestamp != null) {
            // Group by potential mannequin for separate timeframe detection if needed
            // For now, using the combined list as SimulationTimeDetector handles multiple mannequins
            detectorResult = SimulationTimeDetector.identifySimulationAndTimeframe( // Assign value here
                vitalSigns = rawVitalSigns,
                date = firstTimestamp
                // TODO: Pass annotation events if available/needed
            )
            // *** ADD LOGGING ***
            Log.d("ViewModelProcessData", "processLoadedDataAndUpdateStates: SimulationTimeDetector result: ${detectorResult?.let { res -> "Sim=${res.first}, Start=${SimulationTimeDetector.formatTime(res.second.first)}, End=${SimulationTimeDetector.formatTime(res.second.second)}" } ?: "null"}")


            if (detectorResult != null) {
                val (detectedSim, timeframe) = detectorResult
                detectedStartTime = timeframe.first
                detectedEndTime = timeframe.second
                _actualDetectedTimeframe.value = timeframe
                Log.i(TAG, "Detected Sim: $detectedSim, Timeframe: ${SimulationTimeDetector.formatTime(detectedStartTime)} - ${SimulationTimeDetector.formatTime(detectedEndTime)}")
                // *** ADD LOGGING ***
                Log.d("ViewModelProcessData", "processLoadedDataAndUpdateStates: Derived startTime=${detectedStartTime?.let { SimulationTimeDetector.formatTime(it) }}, endTime=${detectedEndTime?.let { SimulationTimeDetector.formatTime(it) }}")

                // For now, apply the overall detected timeframe to all mannequins for summary
                // A more advanced approach could detect per-mannequin timeframes
                val uniqueMannequins = rawVitalSigns.mapNotNull { it.overrideMannequin }.distinct()
                val startStr = SimulationTimeDetector.formatTime(detectedStartTime)
                val endStr = SimulationTimeDetector.formatTime(detectedEndTime)
                uniqueMannequins.forEach { mannequin ->
                     timeframeSummaryMap[mannequin] = Pair(startStr, endStr)
                }
                 _detectedTimeframeSummary.value = timeframeSummaryMap

            } else {
                Log.w(TAG, "Failed to detect simulation timeframe from data.")
                _actualDetectedTimeframe.value = null
                _detectedTimeframeSummary.value = emptyMap()
                // Handle fallback? Use full data range? Use schedule?
                // For now, if detection fails, we might process without timeframe filtering?
                // Or potentially set start/end to min/max of data
                detectedStartTime = rawVitalSigns.minOfOrNull { it.timeObj }
                detectedEndTime = rawVitalSigns.maxOfOrNull { it.timeObj }
            }
        } else {
             Log.w(TAG, "Cannot detect timeframe, no valid first timestamp found.")
             _actualDetectedTimeframe.value = null
             _detectedTimeframeSummary.value = emptyMap()
        }

        // 2. Process Vital Signs: Assign flags based on detected timeframe
        // If detection failed, potentially skip this or use min/max times
        val processedVitalSigns = if (detectedStartTime != null && detectedEndTime != null) {
            val detectedSimName = detectorResult?.first // Get detected sim name
            val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.US) // Date formatter

            rawVitalSigns.map { vs ->
                val isInDetectedTimeframe = vs.timeObj in detectedStartTime..detectedEndTime
                val dateString = try { dateFormat.format(vs.timeObj) } catch (e: Exception) { null }

                vs.copy(
                    sim = detectedSimName ?: vs.sim, // Use detected sim, fallback to original if detection failed
                    dateStr = dateString ?: vs.dateStr, // Use derived date string, fallback to original
                    // Keep previously assigned mannequin/scenario if parser did that
                    inSimWindow = isInDetectedTimeframe, // Use detected timeframe
                    isValid = isInDetectedTimeframe // Mark valid if within detected window
                )
            }
        } else {
            rawVitalSigns // Return raw data if timeframe detection failed
        }
        Log.d(TAG, "Finished processing vital signs. Count: ${processedVitalSigns.size}")
        // *** ADD LOGGING ***
        Log.d("ViewModelProcessData", "processLoadedDataAndUpdateStates: Size of processedVitalSigns (after mapping with isValid flag): ${processedVitalSigns.size}")


        // 3. Update Repository/ViewModel State
        // THIS IS THE KEY: Update the repository's underlying list AND the ViewModel's filtered list
        // Option A: Update Repository directly (if repository exposes a setter or update method)
        // repository.updateVitalSigns(processedVitalSigns)
        // Option B: If repository uses a private MutableStateFlow, we might need a different approach
        //             or accept that repository.vitalSigns won't reflect this processing.
        // FOR NOW: Let's assume we primarily care about the ViewModel's states for UI display.

        // Update _filteredVitalSigns immediately with data within the detected timeframe
        _filteredVitalSigns.value = processedVitalSigns.filter { it.isValid }
        Log.d(TAG, "Updated _filteredVitalSigns. Count: ${_filteredVitalSigns.value.size}")
        // *** ADD LOGGING ***
        Log.d("ViewModelProcessData", "processLoadedDataAndUpdateStates: Size of _filteredVitalSigns.value (after filtering for isValid): ${_filteredVitalSigns.value.size}")

        // *** UPDATE NEW STATE ***
        _processedDataWithinTimeframe.value = _filteredVitalSigns.value
        Log.d(TAG, "Updated _processedDataWithinTimeframe. Count: ${_processedDataWithinTimeframe.value.size}")

        // Update Filter Values based on the *processed* data within the timeframe
        _filterValues.value = repository.getUniqueFilterValues(vitalSignsToAnalyze = _filteredVitalSigns.value)
        Log.d(TAG, "Updated _filterValues.")

        // Calculate Stats Summary based on the *filtered* data
        _statsSummary.value = repository.calculateStatsSummary(_filteredVitalSigns.value)
        Log.d(TAG, "Updated _statsSummary.")

        // Determine Available Vital Signs from the *filtered* data
        detectAvailableVitals(_filteredVitalSigns.value) // *** FIX: Renamed from updateAvailableVitalSigns ***
        Log.d(TAG, "Updated _availableVitalSigns.")

        // Reset any applied user filters
        // resetFilters(applyFilter = false) // Avoid double filtering initially
        Log.d(TAG, "Processing complete.")
    }

    // Need to add clearDataStates function if we want to clear on error
    private fun clearDataStates() {
        // _vitalSigns.value = emptyList() // If ViewModel manages this state
        _filteredVitalSigns.value = emptyList()
        _filterValues.value = null
        _statsSummary.value = emptyMap()
        _actualDetectedTimeframe.value = null
        _detectedTimeframeSummary.value = emptyMap()
        _availableVitalSigns.value = emptySet()
        _dataLoaded.value = false
        // repository.clearData() // Optional: Also clear repo state if needed
        Log.i(TAG, "Cleared data states.")
    }

    /**
     * Start monitoring vital signs
     */
    fun startMonitoring() {
        viewModelScope.launch {
            _monitoringState.value = MonitoringState.Starting
            try {
                Log.d(TAG, "Starting vital signs monitoring")
                val flow = repository.startMonitoring()
                _monitoringState.value = MonitoringState.Running

                // Start a separate coroutine to periodically update compliance stats during monitoring
                val statsUpdateJob = viewModelScope.launch {
                    // Initial delay to let the first vital signs be collected
                    delay(1000)

                    while (monitoringState.value is MonitoringState.Running) {
                        try {
                            // Get the appropriate scenario to use
                            val scenario = when {
                                settings.value.selectedSimulation.isNotEmpty() -> {
                                    val mannequinConfigs = MannequinConfig.getMannequinConfigsForSimulation(settings.value.selectedSimulation)
                                    if (mannequinConfigs.isNotEmpty()) mannequinConfigs.first().scenario else settings.value.scenario
                                }
                                else -> settings.value.scenario
                            }

                            // Update compliance stats using the latest vital signs data
                            val vitalSignsData = repository.vitalSigns.value
                            if (vitalSignsData.isNotEmpty()) {
                                // Always include all data in real-time as it's all relevant
                                _complianceStats.value = calculateComplianceStats(
                                    vitalSignsData,
                                    scenario,
                                    true
                                )

                                // Also update filtered data view
                                _filteredVitalSigns.value = vitalSignsData
                                _statsSummary.value = repository.calculateStatsSummary(vitalSignsData)
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "Error updating stats during monitoring", e)
                        }

                        // Wait before updating again (every 1 second for more responsive updates)
                        delay(1000)
                    }
                }

                try {
                    // Simple collection to keep the monitoring flow active
                    // Actual values are handled by the repository
                    flow.collect {
                        // Check if we've stopped monitoring and cancel the stats update job
                        if (monitoringState.value !is MonitoringState.Running) {
                            statsUpdateJob.cancel()
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error collecting monitoring flow", e)
                    _monitoringState.value = MonitoringState.Error(e.message ?: "Error collecting monitoring data")
                    statsUpdateJob.cancel()
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error starting monitoring", e)
                _monitoringState.value = MonitoringState.Error(e.message ?: "Unknown error")
            }
        }
    }

    /**
     * Stop monitoring vital signs
     */
    fun stopMonitoring() {
        viewModelScope.launch {
            try {
                Log.d(TAG, "Stopping vital signs monitoring")
                repository.stopMonitoring()
                _monitoringState.value = MonitoringState.Stopped

                // After stopping, update compliance stats with the collected data
                val vitalSignsData = repository.vitalSigns.value
                if (vitalSignsData.isNotEmpty()) {
                    val scenario = when {
                        settings.value.selectedSimulation.isNotEmpty() -> {
                            val mannequinConfigs = MannequinConfig.getMannequinConfigsForSimulation(settings.value.selectedSimulation)
                            if (mannequinConfigs.isNotEmpty()) mannequinConfigs.first().scenario else settings.value.scenario
                        }
                        else -> settings.value.scenario
                    }

                    // First try with precise timeframe
                    val preciseStats = calculateComplianceStats(vitalSignsData, scenario, true)

                    // If we didn't get any results with precise timeframe, fall back to non-precise
                    if (preciseStats.vitalStats.isEmpty()) {
                        _complianceStats.value = calculateComplianceStats(vitalSignsData, scenario, false)
                    } else {
                        _complianceStats.value = preciseStats
                    }

                    // Also update the filtered vital signs to include all data from the simulation
                    val filteredData = vitalSignsData.filter {
                        it.inPreciseSimTimeframe || it.inSimWindow
                    }
                    _filteredVitalSigns.value = filteredData
                    _statsSummary.value = repository.calculateStatsSummary(filteredData)
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error stopping monitoring", e)
                _monitoringState.value = MonitoringState.Error(e.message ?: "Unknown error")
            }
        }
    }

    /**
     * Update monitor settings
     * @param dataSourceType The data source type
     * @param ipAddress The IP address
     * @param port The port number
     * @param authRequired Whether authentication is required
     * @param username The username
     * @param password The password
     * @param authMethod The authentication method
     * @param updateIntervalMs The update interval in milliseconds
     * @param scenario The scenario
     * @param mannequinIpAddresses Map of mannequin names to their IP addresses
     */
    fun updateSettings(
        dataSourceType: DataSourceType = settings.value.dataSourceType,
        ipAddress: String = settings.value.ipAddress,
        port: Int = settings.value.port,
        authRequired: Boolean = settings.value.authRequired,
        username: String = settings.value.username,
        password: String = settings.value.password,
        authMethod: String = settings.value.authMethod,
        updateIntervalMs: Long = settings.value.updateIntervalMs,
        scenario: String = settings.value.scenario,
        mannequinIpAddresses: Map<String, String> = settings.value.mannequinIpAddresses
    ) {
        val newSettings = MonitorSettings(
            dataSourceType = dataSourceType,
            ipAddress = ipAddress,
            port = port,
            authRequired = authRequired,
            username = username,
            password = password,
            authMethod = authMethod,
            updateIntervalMs = updateIntervalMs,
            scenario = scenario,
            selectedSimulation = settings.value.selectedSimulation,
            mannequinIpAddresses = mannequinIpAddresses
        )
        repository.updateSettings(newSettings)
    }

    /**
     * Clear all data
     */
    fun clearData() {
        repository.clearData()
    }

    /**
     * Calculate compliance statistics for a given set of vital signs and scenario
     * @param data The list of vital signs to analyze
     * @param scenarioName The name of the scenario to compare against
     * @param usePreciseTimeframeOnly If true, only consider data within the precise sim timeframe
     * @return ComplianceStats object
     */
    private fun calculateComplianceStats(
        data: List<VitalSign>,
        scenarioName: String,
        usePreciseTimeframeOnly: Boolean
    ): ComplianceStats {
        Log.d(TAG, "Calculating compliance for scenario: $scenarioName, usePrecise: $usePreciseTimeframeOnly. Input data size: ${data.size}")

        // Filter data by the provided scenarioName, especially if data might be mixed from live sources
        // Also, ensure that if usePreciseTimeframeOnly is true, we respect that.
        val relevantData = data.filter { vs ->
            val scenarioMatches = vs.scenario == scenarioName || scenarioName.isBlank() // If target scenarioName is blank, match any
            val timeframeMatches = if (usePreciseTimeframeOnly) vs.inPreciseSimTimeframe else true
            scenarioMatches && timeframeMatches
        }

        Log.d(TAG, "Relevant data for $scenarioName after filtering: ${relevantData.size}")

        if (relevantData.isEmpty()) {
            Log.w(TAG, "No relevant data for scenario '$scenarioName' after filtering. Returning empty stats.")
            return ComplianceStats(scenario = scenarioName, overallStatus = ComplianceStatus.UNKNOWN)
        }

        val thresholds = ClinicalPracticeGuidelines.ALL_THRESHOLDS[scenarioName]
            ?: return ComplianceStats(scenario = scenarioName, overallStatus = ComplianceStatus.UNKNOWN)

        // Stats for each vital sign
        val vitalStats = mutableMapOf<String, VitalSignStat>()

        // List to track non-compliance events
        val nonComplianceEvents = mutableListOf<NonComplianceEvent>()

        // Sort vital signs by time to ensure correct order
        val sortedVitalSigns = relevantData.sortedBy { it.timeObj }

        // Duration in minutes - ensure endTime is after startTime
        val startTime = sortedVitalSigns.first().timeObj
        val endTime = sortedVitalSigns.last().timeObj
        val durationMin = if (endTime.time >= startTime.time) {
            (endTime.time - startTime.time) / (60 * 1000.0)
        } else {
            Log.w(TAG, "End time is before start time, defaulting to zero duration")
            0.0
        }

        // Keep track of which vital signs are available in our dataset
        val availableVitalSignsInData = mutableSetOf<String>()

        // Preprocess to determine which vital signs are actually available in data
        for (vital in thresholds.keys) {
            val extractor = getVitalExtractor(vital)
            if (extractor != null) {
                val values = sortedVitalSigns.mapNotNull { extractor(it) }
                if (values.isNotEmpty()) {
                    availableVitalSignsInData.add(vital)
                }
            }
        }

        // Calculate stats for each vital sign defined in the thresholds for this scenario
        for ((vital, threshold) in thresholds) {
            // Try to get the extractor for this vital sign
            val extractor = getVitalExtractor(vital)

            // If we don't have an extractor for this vital sign, add a missing stat
            if (extractor == null) {
                Log.w(TAG, "No extractor available for vital sign: $vital in scenario: $scenarioName")
                continue
            }

            // Extract values for this vital sign
            val values = sortedVitalSigns.mapNotNull { extractor(it) }

            // If no data for this vital sign, create a "missing" stat
            if (values.isEmpty()) {
                // This is a required vital sign for this scenario but no data available
                Log.w(TAG, "No data available for required vital sign: $vital in scenario: $scenarioName")

                // Add a zero compliance stat for this missing vital sign
                val (minValue, maxValue) = threshold
                vitalStats[vital] = VitalSignStat(
                    name = vital,
                    target = when {
                        minValue != null && maxValue != null -> "$minValue - $maxValue"
                        minValue != null -> "≥ $minValue"
                        maxValue != null -> "≤ $maxValue"
                        else -> "No target"
                    },
                    actualRange = "No data",
                    meanValue = 0.0,
                    inRangePercent = 0.0,  // Zero compliance for missing data
                    coveragePercent = 0.0, // Zero coverage for missing data
                    status = ComplianceStatus.POOR, // Missing data is considered poor compliance
                    timeOutOfRangeMin = durationMin, // All time is considered out of range
                    timeMonitoredMin = 0.0 // No time monitored
                )
                continue
            }

            val minObserved = values.minOrNull() ?: 0.0
            val maxObserved = values.maxOrNull() ?: 0.0
            val count = values.size
            var sumValue = 0.0

            // Track time spent out of range
            var timeOutOfRangeMs = 0L
            var timeMonitoredMs = 0L

            // Last valid vital sign and timestamp
            var lastTimestamp: Date? = null
            var lastValue: Double? = null
            var isCurrentlyOutOfRange = false
            var currentEventStartTime: Date? = null
            val currentEventValues = mutableListOf<Double>()

            // Get threshold values
            val (minValue, maxValue) = threshold

            // Go through all valid readings for this vital
            for (i in sortedVitalSigns.indices) {
                val vitalSign = sortedVitalSigns[i]
                val value = extractor(vitalSign)

                if (value != null) {
                    val timestamp = vitalSign.timeObj
                    sumValue += value

                    // If this is not the first reading, calculate time elapsed
                    if (lastTimestamp != null && lastValue != null) {
                        // Ensure timestamps are in order
                        val elapsedMs = if (timestamp.time >= lastTimestamp.time) {
                            timestamp.time - lastTimestamp.time
                        } else {
                            // Log warning and skip calculation for this reading
                            Log.w(TAG, "Out of order timestamp detected: $timestamp < $lastTimestamp")
                            0L
                        }

                        // Only count time if elapsed time is valid
                        if (elapsedMs > 0) {
                        timeMonitoredMs += elapsedMs

                        // Check if this reading is out of range
                        val isOutOfRange = (minValue != null && value < minValue) ||
                                         (maxValue != null && value > maxValue)

                        // If out of range, add to the total time
                        if (isOutOfRange) {
                            timeOutOfRangeMs += elapsedMs

                            // Track non-compliance event start
                            if (!isCurrentlyOutOfRange) {
                                isCurrentlyOutOfRange = true
                                currentEventStartTime = lastTimestamp
                                currentEventValues.clear()
                                currentEventValues.add(lastValue) // Add the last value that was still in compliance
                            }
                            currentEventValues.add(value)
                        } else if (isCurrentlyOutOfRange) {
                            // This reading is back in range, so the event has ended
                            isCurrentlyOutOfRange = false

                            // Create and add the non-compliance event
                            if (currentEventStartTime != null && currentEventValues.isNotEmpty()) {
                                nonComplianceEvents.add(
                                    NonComplianceEvent(
                                        startTime = currentEventStartTime,
                                        endTime = timestamp,
                                        vitalSign = vital,
                                        minThreshold = minValue,
                                        maxThreshold = maxValue,
                                        values = currentEventValues.toList(),
                                        mannequin = vitalSign.overrideMannequin,
                                        scenario = scenarioName
                                    )
                                )
                                }
                            }
                        }
                    }

                    lastTimestamp = timestamp
                    lastValue = value
                }
            }

            // Check if we have an ongoing non-compliance event at the end of the data
            if (isCurrentlyOutOfRange && currentEventStartTime != null && lastTimestamp != null && currentEventValues.isNotEmpty()) {
                nonComplianceEvents.add(
                    NonComplianceEvent(
                        startTime = currentEventStartTime,
                        endTime = lastTimestamp,
                        vitalSign = vital,
                        minThreshold = minValue,
                        maxThreshold = maxValue,
                        values = currentEventValues.toList(),
                        mannequin = sortedVitalSigns.last().overrideMannequin,
                        scenario = scenarioName
                    )
                )
            }

            // Calculate statistics if we have data
            if (count > 0) {
                val mean = sumValue / count

                // Ensure timeMonitoredMs is greater than zero to avoid division by zero
                // and cap inRangePercent at 100%
                val inRangePercent = if (timeMonitoredMs > 0) {
                    val percentage = 100 * (1 - (timeOutOfRangeMs.toDouble() / timeMonitoredMs.toDouble()))
                    // Ensure percentage doesn't exceed 100%
                    percentage.coerceIn(0.0, 100.0)
                } else {
                    0.0
                }

                // Ensure durationMin is greater than zero to avoid division by zero
                val coveragePercent = if (durationMin > 0) {
                    val percentage = 100 * (timeMonitoredMs.toDouble() / (durationMin * 60 * 1000))
                    // Ensure percentage doesn't exceed 100%
                    percentage.coerceIn(0.0, 100.0)
                } else {
                    0.0
                }

                // Determine status
                val status = when {
                    inRangePercent >= 90 -> ComplianceStatus.EXCELLENT
                    inRangePercent >= 75 -> ComplianceStatus.GOOD
                    inRangePercent >= 60 -> ComplianceStatus.FAIR
                    else -> ComplianceStatus.POOR
                }

                vitalStats[vital] = VitalSignStat(
                    name = vital,
                    target = when {
                        minValue != null && maxValue != null -> "$minValue - $maxValue"
                        minValue != null -> "≥ $minValue"
                        maxValue != null -> "≤ $maxValue"
                        else -> "No target"
                    },
                    actualRange = "$minObserved - $maxObserved",
                    meanValue = mean,
                    inRangePercent = inRangePercent,
                    coveragePercent = coveragePercent,
                    status = status,
                    timeOutOfRangeMin = timeOutOfRangeMs / (60 * 1000.0),
                    timeMonitoredMin = timeMonitoredMs / (60 * 1000.0)
                )
            }
        }

        // Log warning if any vital signs are missing that should be present for this scenario
        if (vitalStats.size < thresholds.size) {
            val missingVitalSigns = thresholds.keys.filter { it !in vitalStats.keys }
            Log.w(TAG, "Missing vital signs for scenario $scenarioName: $missingVitalSigns")
        }

        // Calculate overall compliance score - must include ALL required vital signs, even missing ones
        val overallScore = if (vitalStats.isNotEmpty()) {
            val score = vitalStats.values.sumOf { it.inRangePercent } / thresholds.size
            score.coerceIn(0.0, 100.0)
        } else {
            0.0
        }

        // Determine overall status
        val overallStatus = when {
            overallScore >= 90 -> ComplianceStatus.EXCELLENT
            overallScore >= 75 -> ComplianceStatus.GOOD
            overallScore >= 60 -> ComplianceStatus.FAIR
            else -> ComplianceStatus.POOR
        }

        // Sort non-compliance events by start time
        val sortedEvents = nonComplianceEvents.sortedBy { it.startTime }

        return ComplianceStats(
            scenario = scenarioName,
            durationMin = durationMin,
            overallScore = overallScore,
            overallStatus = overallStatus,
            vitalStats = vitalStats,
            nonComplianceEvents = sortedEvents
        )
    }

    /**
     * Helper method to get an extractor function for a vital sign
     */
    private fun getVitalExtractor(vital: String): ((VitalSign) -> Double?)? {
        return when (vital) {
            "Hr" -> { vs -> vs.hr }
            "SpO2" -> { vs -> vs.spO2 }
            "NIBP_SYS" -> { vs -> vs.nibpSys }
            "NIBP_DIA" -> { vs -> vs.nibpDia }
            "NIBP_MAP" -> { vs -> vs.nibpMap }
            "RespRate" -> { vs -> vs.respRate }
            "Temp", "TempF" -> { vs -> vs.temp1 }
            "EtCO2" -> { vs -> vs.etCO2 }
            else -> null
        }
    }

    /**
     * Update the selected simulation
     * @param simulation The simulation name
     */
    fun updateSelectedSimulation(simulation: String) {
        viewModelScope.launch {
            repository.updateSelectedSimulation(simulation)

            // Recalculate compliance stats for the new simulation
            if (simulation.isNotEmpty()) {
                val mannequinConfigs = MannequinConfig.getMannequinConfigsForSimulation(simulation)
                val scenario = if (mannequinConfigs.isNotEmpty()) mannequinConfigs.first().scenario else settings.value.scenario
                _complianceStats.value = calculateComplianceStats(repository.vitalSigns.value, scenario, false)
            }
        }
    }

    /**
     * Update the detected timeframe summary based on SimulationTimeDetector result
     */
    private fun updateDetectedTimeframeSummary(detectorResult: Pair<String, Pair<Date, Date>>?) {
        val summary = mutableMapOf<String, Pair<String, String>>()
        val timeFormat = SimpleDateFormat("HH:mm:ss", Locale.US)

        if (detectorResult != null) {
            val (simName, timeframe) = detectorResult
            val (startTime, endTime) = timeframe

            // Get the vital signs in the detected timeframe
            val vitalsInTimeframe = repository.vitalSigns.value.filter {
                it.timeObj >= startTime && it.timeObj <= endTime
            }

            // Organize by mannequin
            val mannequinGroups = vitalsInTimeframe.groupBy {
                it.overrideMannequin ?: "Unknown"
            }

            // For each mannequin, find the first and last timestamps with actual valid vitals
            mannequinGroups.forEach { (mannequin, signs) ->
            if (signs.isNotEmpty()) {
                    // Look for signs that have actual physiological data
                    val validSigns = signs.filter { sign ->
                        (sign.hr != null && sign.hrStatus == "valid") ||
                        (sign.nibpSys != null && sign.nibpSysStatus == "valid") ||
                        (sign.nibpDia != null && sign.nibpDiaStatus == "valid") ||
                        (sign.nibpMap != null && sign.nibpMapStatus == "valid") ||
                        (sign.spO2 != null && sign.spO2Status == "valid")
                    }

                    if (validSigns.isNotEmpty()) {
                        // Sort by time
                        val sortedSigns = validSigns.sortedBy { it.timeObj }

                        // Get the first and last signs with valid data
                        val firstValidSign = sortedSigns.first()
                        val lastValidSign = sortedSigns.last()

                        // Format the timestamps
                        val mannequinStartTime = timeFormat.format(firstValidSign.timeObj)
                        val mannequinEndTime = timeFormat.format(lastValidSign.timeObj)

                        summary[mannequin] = Pair(mannequinStartTime, mannequinEndTime)

                        Log.i(TAG, "Mannequin $mannequin valid data: $mannequinStartTime - $mannequinEndTime")
                    } else {
                        // No valid signs found for this mannequin
                        val firstTime = timeFormat.format(signs.first().timeObj)
                        val lastTime = timeFormat.format(signs.last().timeObj)
                        summary[mannequin] = Pair("$firstTime (no valid data)", lastTime)

                        Log.w(TAG, "Mannequin $mannequin has no valid vital signs in detected timeframe")
                    }
                }
            }

            // If no mannequins were found, still add the overall timeframe
            if (summary.isEmpty()) {
                summary["Overall"] = Pair(timeFormat.format(startTime), timeFormat.format(endTime))
                Log.i(TAG, "Detected Overall Timeframe: ${timeFormat.format(startTime)} - ${timeFormat.format(endTime)}")
            }
        } else {
            Log.w(TAG, "Could not detect simulation timeframe.")
        }

        _detectedTimeframeSummary.value = summary
    }

    /**
     * Set the sort configuration for the vital signs table
     */
    fun setSortConfig(config: SortConfig) {
        _sortConfig.value = config

        // Apply the sort to the filtered vital signs
        applySort()
    }

    /**
     * Apply the current sort configuration to the filtered vital signs
     */
    private fun applySort() {
        val config = _sortConfig.value ?: return
        val currentData = _filteredVitalSigns.value

        val sortedData = when (config.field) {
            "time" -> {
                if (config.ascending) {
                    currentData.sortedBy { it.timeObj }
                } else {
                    currentData.sortedByDescending { it.timeObj }
                }
            }
            "hr" -> {
                if (config.ascending) {
                    currentData.sortedBy { it.hr ?: Double.MIN_VALUE }
                } else {
                    currentData.sortedByDescending { it.hr ?: Double.MIN_VALUE }
                }
            }
            "spO2" -> {
                if (config.ascending) {
                    currentData.sortedBy { it.spO2 ?: Double.MIN_VALUE }
                } else {
                    currentData.sortedByDescending { it.spO2 ?: Double.MIN_VALUE }
                }
            }
            "temp" -> {
                if (config.ascending) {
                    currentData.sortedBy { it.temp1 ?: Double.MIN_VALUE }
                } else {
                    currentData.sortedByDescending { it.temp1 ?: Double.MIN_VALUE }
                }
            }
            else -> currentData
        }

        _filteredVitalSigns.value = sortedData
    }

    /**
     * Detect which vital signs are available in the dataset and their min/max values
     */
    private fun detectAvailableVitals(vitalSigns: List<VitalSign>) {
        if (vitalSigns.isEmpty()) return

        // Set to track which vitals are available
        val availableVitals = mutableSetOf<String>()

        // Current filter state
        val currentFilters = _vitalRangeFilters.value

        // Check Heart Rate
        val hrValues = vitalSigns.mapNotNull { it.hr }.filter { it in 30.0..220.0 }
        val hrFilter = if (hrValues.isNotEmpty()) {
            availableVitals.add("heartRate")
            val min = hrValues.minOrNull()?.toFloat() ?: currentFilters.heartRate.defaultMin
            val max = hrValues.maxOrNull()?.toFloat() ?: currentFilters.heartRate.defaultMax
            currentFilters.heartRate.copy(
                min = currentFilters.heartRate.min,
                max = currentFilters.heartRate.max,
                defaultMin = min.coerceAtLeast(30f),
                defaultMax = max.coerceAtMost(220f),
                available = true
            )
        } else {
            currentFilters.heartRate.copy(available = false)
        }

        // Check SpO2
        val spo2Values = vitalSigns.mapNotNull { it.spO2 }.filter { it in 70.0..100.0 }
        val spo2Filter = if (spo2Values.isNotEmpty()) {
            availableVitals.add("spo2")
            val min = spo2Values.minOrNull()?.toFloat() ?: currentFilters.spo2.defaultMin
            val max = spo2Values.maxOrNull()?.toFloat() ?: currentFilters.spo2.defaultMax
            currentFilters.spo2.copy(
                min = currentFilters.spo2.min,
                max = currentFilters.spo2.max,
                defaultMin = min.coerceAtLeast(70f),
                defaultMax = max.coerceAtMost(100f),
                available = true
            )
        } else {
            currentFilters.spo2.copy(available = false)
        }

        // Check NIBP Systolic
        val nibpSysValues = vitalSigns.mapNotNull { it.nibpSys }.filter { it in 60.0..220.0 }
        val nibpSysFilter = if (nibpSysValues.isNotEmpty()) {
            availableVitals.add("nibpSys")
            val min = nibpSysValues.minOrNull()?.toFloat() ?: currentFilters.nibpSys.defaultMin
            val max = nibpSysValues.maxOrNull()?.toFloat() ?: currentFilters.nibpSys.defaultMax
            currentFilters.nibpSys.copy(
                min = currentFilters.nibpSys.min,
                max = currentFilters.nibpSys.max,
                defaultMin = min.coerceAtLeast(60f),
                defaultMax = max.coerceAtMost(220f),
                available = true
            )
        } else {
            currentFilters.nibpSys.copy(available = false)
        }

        // Check NIBP Diastolic
        val nibpDiaValues = vitalSigns.mapNotNull { it.nibpDia }.filter { it in 30.0..120.0 }
        val nibpDiaFilter = if (nibpDiaValues.isNotEmpty()) {
            availableVitals.add("nibpDia")
            val min = nibpDiaValues.minOrNull()?.toFloat() ?: currentFilters.nibpDia.defaultMin
            val max = nibpDiaValues.maxOrNull()?.toFloat() ?: currentFilters.nibpDia.defaultMax
            currentFilters.nibpDia.copy(
                min = currentFilters.nibpDia.min,
                max = currentFilters.nibpDia.max,
                defaultMin = min.coerceAtLeast(30f),
                defaultMax = max.coerceAtMost(120f),
                available = true
            )
        } else {
            currentFilters.nibpDia.copy(available = false)
        }

        // Check NIBP MAP
        val nibpMapValues = vitalSigns.mapNotNull { it.nibpMap }.filter { it in 40.0..150.0 }
        val nibpMapFilter = if (nibpMapValues.isNotEmpty()) {
            availableVitals.add("nibpMap")
            val min = nibpMapValues.minOrNull()?.toFloat() ?: currentFilters.nibpMap.defaultMin
            val max = nibpMapValues.maxOrNull()?.toFloat() ?: currentFilters.nibpMap.defaultMax
            currentFilters.nibpMap.copy(
                min = currentFilters.nibpMap.min,
                max = currentFilters.nibpMap.max,
                defaultMin = min.coerceAtLeast(40f),
                defaultMax = max.coerceAtMost(150f),
                available = true
            )
        } else {
            currentFilters.nibpMap.copy(available = false)
        }

        // Check Temperature
        val tempValues = vitalSigns.mapNotNull { it.temp1 }.filter { it in 35.0..42.0 }
        val tempFilter = if (tempValues.isNotEmpty()) {
            availableVitals.add("temp")
            val min = tempValues.minOrNull()?.toFloat() ?: currentFilters.temp.defaultMin
            val max = tempValues.maxOrNull()?.toFloat() ?: currentFilters.temp.defaultMax
            currentFilters.temp.copy(
                min = currentFilters.temp.min,
                max = currentFilters.temp.max,
                defaultMin = min.coerceAtLeast(35f),
                defaultMax = max.coerceAtMost(42f),
                available = true
            )
        } else {
            currentFilters.temp.copy(available = false)
        }

        // Check Respiratory Rate
        val respRateValues = vitalSigns.mapNotNull { it.respRate }.filter { it in 4.0..40.0 }
        val respRateFilter = if (respRateValues.isNotEmpty()) {
            availableVitals.add("respRate")
            val min = respRateValues.minOrNull()?.toFloat() ?: currentFilters.respRate.defaultMin
            val max = respRateValues.maxOrNull()?.toFloat() ?: currentFilters.respRate.defaultMax
            currentFilters.respRate.copy(
                min = currentFilters.respRate.min,
                max = currentFilters.respRate.max,
                defaultMin = min.coerceAtLeast(4f),
                defaultMax = max.coerceAtMost(40f),
                available = true
            )
        } else {
            currentFilters.respRate.copy(available = false)
        }

        // Check ETCO2
        val etco2Values = vitalSigns.mapNotNull { it.etCO2 }.filter { it in 10.0..60.0 }
        val etco2Filter = if (etco2Values.isNotEmpty()) {
            availableVitals.add("etco2")
            val min = etco2Values.minOrNull()?.toFloat() ?: currentFilters.etco2.defaultMin
            val max = etco2Values.maxOrNull()?.toFloat() ?: currentFilters.etco2.defaultMax
            currentFilters.etco2.copy(
                min = currentFilters.etco2.min,
                max = currentFilters.etco2.max,
                defaultMin = min.coerceAtLeast(10f),
                defaultMax = max.coerceAtMost(60f),
                available = true
            )
        } else {
            currentFilters.etco2.copy(available = false)
        }

        // Update available vitals
        _availableVitalSigns.value = availableVitals

        // Create the updated filters object
        val updatedFilters = VitalRangeFilters(
            heartRate = hrFilter,
            spo2 = spo2Filter,
            nibpSys = nibpSysFilter,
            nibpDia = nibpDiaFilter,
            nibpMap = nibpMapFilter,
            temp = tempFilter,
            respRate = respRateFilter,
            etco2 = etco2Filter
        )

        // Only update if the available vitals have changed
        if (currentFilters.toString() != updatedFilters.toString()) {
            _vitalRangeFilters.value = updatedFilters
        }
    }

    /**
     * Update a specific vital range filter
     */
    fun updateVitalRangeFilter(vitalName: String, min: Float, max: Float) {
        val currentFilters = _vitalRangeFilters.value

        val updatedFilters = when (vitalName) {
            "heartRate" -> currentFilters.copy(
                heartRate = currentFilters.heartRate.copy(min = min, max = max)
            )
            "spo2" -> currentFilters.copy(
                spo2 = currentFilters.spo2.copy(min = min, max = max)
            )
            "nibpSys" -> currentFilters.copy(
                nibpSys = currentFilters.nibpSys.copy(min = min, max = max)
            )
            "nibpDia" -> currentFilters.copy(
                nibpDia = currentFilters.nibpDia.copy(min = min, max = max)
            )
            "nibpMap" -> currentFilters.copy(
                nibpMap = currentFilters.nibpMap.copy(min = min, max = max)
            )
            "temp" -> currentFilters.copy(
                temp = currentFilters.temp.copy(min = min, max = max)
            )
            "respRate" -> currentFilters.copy(
                respRate = currentFilters.respRate.copy(min = min, max = max)
            )
            "etco2" -> currentFilters.copy(
                etco2 = currentFilters.etco2.copy(min = min, max = max)
            )
            else -> currentFilters
        }

        if (updatedFilters != currentFilters) {
            _vitalRangeFilters.value = updatedFilters
            // Automatically apply filters when ranges change
            applyFilters()
        }
    }

    /**
     * Reset all vital range filters to their default values
     */
    fun resetVitalRangeFilters() {
        val currentFilters = _vitalRangeFilters.value

        val resetFilters = VitalRangeFilters(
            heartRate = currentFilters.heartRate.copy(
                min = currentFilters.heartRate.defaultMin,
                max = currentFilters.heartRate.defaultMax
            ),
            spo2 = currentFilters.spo2.copy(
                min = currentFilters.spo2.defaultMin,
                max = currentFilters.spo2.defaultMax
            ),
            nibpSys = currentFilters.nibpSys.copy(
                min = currentFilters.nibpSys.defaultMin,
                max = currentFilters.nibpSys.defaultMax
            ),
            nibpDia = currentFilters.nibpDia.copy(
                min = currentFilters.nibpDia.defaultMin,
                max = currentFilters.nibpDia.defaultMax
            ),
            nibpMap = currentFilters.nibpMap.copy(
                min = currentFilters.nibpMap.defaultMin,
                max = currentFilters.nibpMap.defaultMax
            ),
            temp = currentFilters.temp.copy(
                min = currentFilters.temp.defaultMin,
                max = currentFilters.temp.defaultMax
            ),
            respRate = currentFilters.respRate.copy(
                min = currentFilters.respRate.defaultMin,
                max = currentFilters.respRate.defaultMax
            ),
            etco2 = currentFilters.etco2.copy(
                min = currentFilters.etco2.defaultMin,
                max = currentFilters.etco2.defaultMax
            )
        )

        _vitalRangeFilters.value = resetFilters
        applyFilters()
    }

    /**
     * Apply all current filters including vital range filters
     */
    fun applyFilters() {
        viewModelScope.launch {
            // Get the currently selected filters
            val filters = _selectedFilters.value
            val rangeFilters = _vitalRangeFilters.value

            // Get the full dataset from the repository
            // val allData = repository.vitalSigns.value // <-- OLD: Started from raw data

            // *** NEW: Start filtering from the already processed data within the timeframe ***
            val baseDataForFiltering = _processedDataWithinTimeframe.value
            Log.i("ViewModelApplyFilters", "Starting applyFilters with base data size: ${baseDataForFiltering.size}")

            // Get the overall detected timeframe (if available)
            // val detectedTimeframe = _actualDetectedTimeframe.value // No longer needed here

            // 1. Filter by the overall detected timeframe first -- REMOVED (already done)
            // var timeframeFilteredData = if (detectedTimeframe != null) { ... } else { allData }
            // Log.i("ViewModelApplyFilters", "Size after timeframe filter: ${timeframeFilteredData.size}. ...")

            // 2. Apply user-selected filters (Sim, Date, Mannequin, Mode)
            // Using repository.filterVitalSigns with the timeframe-filtered data
            Log.i("ViewModelApplyFilters", "Calling repository.filterVitalSigns with ${baseDataForFiltering.size} records. Filters: Sim=${filters.sim ?: "N/A"}, Date=${filters.date ?: "N/A"}, Mannequin=${filters.mannequin ?: "N/A"}, Mode=${filters.simulationMode}")
            var userFilteredData = repository.filterVitalSigns(
                vitalSignsToFilter = baseDataForFiltering, // Pass the pre-processed, timeframe-filtered data
                simFilter = filters.sim,
                dateFilter = filters.date,
                mannequinFilter = filters.mannequin,
                startTimeStr = filters.startTime, // Apply time string filters here
                endTimeStr = filters.endTime,     // Apply time string filters here
                simulationMode = filters.simulationMode
            )
            // *** ADD LOGGING HERE ***
            Log.i("ViewModelApplyFilters", "Size after repository.filterVitalSigns: ${userFilteredData.size}")

            // 3. Apply vital range filters
            if (rangeFilters.heartRate.available) {
                userFilteredData = userFilteredData.filter { vs ->
                    vs.hr?.let { it >= rangeFilters.heartRate.min && it <= rangeFilters.heartRate.max } ?: true
                }
            }

            if (rangeFilters.spo2.available) {
                userFilteredData = userFilteredData.filter { vs ->
                    vs.spO2?.let { it >= rangeFilters.spo2.min && it <= rangeFilters.spo2.max } ?: true
                }
            }

            if (rangeFilters.nibpSys.available) {
                userFilteredData = userFilteredData.filter { vs ->
                    vs.nibpSys?.let { it >= rangeFilters.nibpSys.min && it <= rangeFilters.nibpSys.max } ?: true
                }
            }

            if (rangeFilters.nibpDia.available) {
                userFilteredData = userFilteredData.filter { vs ->
                    vs.nibpDia?.let { it >= rangeFilters.nibpDia.min && it <= rangeFilters.nibpDia.max } ?: true
                }
            }

            if (rangeFilters.nibpMap.available) {
                userFilteredData = userFilteredData.filter { vs ->
                    vs.nibpMap?.let { it >= rangeFilters.nibpMap.min && it <= rangeFilters.nibpMap.max } ?: true
                }
            }

            if (rangeFilters.temp.available) {
                userFilteredData = userFilteredData.filter { vs ->
                    vs.temp1?.let { it >= rangeFilters.temp.min && it <= rangeFilters.temp.max } ?: true
                }
            }

            if (rangeFilters.respRate.available) {
                userFilteredData = userFilteredData.filter { vs ->
                    vs.respRate?.let { it >= rangeFilters.respRate.min && it <= rangeFilters.respRate.max } ?: true
                }
            }

            if (rangeFilters.etco2.available) {
                userFilteredData = userFilteredData.filter { vs ->
                    vs.etCO2?.let { it >= rangeFilters.etco2.min && it <= rangeFilters.etco2.max } ?: true
                }
            }
             // *** ADD LOGGING HERE ***
             Log.i("ViewModelApplyFilters", "Size after vital range filters: ${userFilteredData.size}")

            // Update filtered data state
            _filteredVitalSigns.value = userFilteredData
            // *** ADD LOGGING ***
            Log.i("ViewModelApplyFilters", "Final _filteredVitalSigns size: ${_filteredVitalSigns.value.size}")

            // Apply any active sort
            if (_sortConfig.value != null) {
                applySort() // applySort uses _filteredVitalSigns directly
            }

            // Recalculate stats based on the final filtered data
            _statsSummary.value = repository.calculateStatsSummary(userFilteredData)

            // Calculate compliance stats if we have data
            if (userFilteredData.isNotEmpty()) {
                // Try to determine the appropriate scenario to use
                val scenario = determineScenarioFromData(userFilteredData)

                // Calculate compliance stats using filtered data
                _complianceStats.value = calculateComplianceStats(
                    userFilteredData,
                    scenario,
                    true // Always use precise/detected timeframe for compliance
                )
            } else {
                // No data after filtering, reset compliance stats
                _complianceStats.value = ComplianceStats(scenario = determineScenarioFromData(userFilteredData))
            }
        }
    }

    /**
     * Determine the most appropriate scenario based on the filtered data
     */
    private fun determineScenarioFromData(filteredData: List<VitalSign>): String {
        // If specific mannequin is selected in filters, use its scenario
        if (_selectedFilters.value.mannequin != null && _selectedFilters.value.mannequin != "<all>") {
            val mannequin = _selectedFilters.value.mannequin!!
            val sim = _selectedFilters.value.sim

            // If we have both simulation and mannequin, get the scenario from MannequinConfig
            if (sim != null && sim != "<all>") {
                return MannequinConfig.getScenarioForMannequin(sim, mannequin)
            }

            // If just mannequin is specified, use a default based on the mannequin
            return when (mannequin) {
                "Dave" -> "TBI"
                "Chuck" -> "Pneumonia"
                "Freddy" -> "TBI_unmonitored"
                "Matt" -> "PenetratingThoracoabdominalInjury"
                "Oscar" -> "DCR"
                else -> settings.value.scenario
            }
        }

        // If a simulation is specified, try to determine the scenario from the simulation
        if (_selectedFilters.value.sim != null && _selectedFilters.value.sim != "<all>") {
            val sim = _selectedFilters.value.sim!!
            // Get the most common scenario associated with vitals from this simulation
            val scenarioCounts = filteredData
                .filter { it.scenario != null }
                .groupBy { it.scenario }
                .mapValues { it.value.size }

            if (scenarioCounts.isNotEmpty()) {
                return scenarioCounts.maxByOrNull { it.value }?.key ?: settings.value.scenario
            }

            // If no scenario found in the data, use default mappings
            return when (sim) {
                "Sim1" -> "TBI"
                "Sim2" -> "DCR"
                "Sim3" -> "Pneumonia"
                "Sim4" -> "TBI_unmonitored"
                "Sim5" -> "PenetratingThoracoabdominalInjury"
                else -> settings.value.scenario
            }
        }

        // Otherwise use the currently selected scenario
        return settings.value.scenario
    }

    /**
     * Reset all filters
     */
    fun resetFilters() {
        viewModelScope.launch {
            // Store the current simulation mode before reset
            val currentMode = _selectedFilters.value.simulationMode

            // Clear selected filters but keep the simulation mode
            _selectedFilters.value = SelectedFilters(simulationMode = currentMode)

            // Get all data *within the initially detected timeframe*
            val allData = repository.vitalSigns.value
            val detectedTimeframe = _actualDetectedTimeframe.value
            val initialFilteredData = if (detectedTimeframe != null) {
                allData.filter {
                    it.timeObj >= detectedTimeframe.first && it.timeObj <= detectedTimeframe.second
                }
            } else {
                allData
            }

            // Update filter values with the current simulation mode based on the timeframe-filtered data
            _filterValues.value = repository.getUniqueFilterValues(
                vitalSignsToAnalyze = initialFilteredData, // Base values on data within detected timeframe
                simulationMode = currentMode
            )

            // Only update if we have data within the timeframe
            if (initialFilteredData.isNotEmpty()) {
                _filteredVitalSigns.value = initialFilteredData

                // Update stats
                _statsSummary.value = repository.calculateStatsSummary(initialFilteredData)
                detectAvailableVitals(initialFilteredData) // Update available vitals based on this data

                // Get most common scenario from the data or use default
                val scenario = determineScenarioFromData(initialFilteredData)

                // Update compliance stats with best matching scenario
                _complianceStats.value = calculateComplianceStats(
                    initialFilteredData,
                    scenario,
                    true // Always use precise timeframe for compliance
                )
            } else {
                 _filteredVitalSigns.value = emptyList()
                 _statsSummary.value = emptyMap()
                 _availableVitalSigns.value = emptySet()
                 _complianceStats.value = ComplianceStats(scenario = determineScenarioFromData(emptyList()))
            }

            // Reset vital range filters
            resetVitalRangeFilters()
        }
    }

    /**
     * Set the simulation filter
     */
    fun setSimFilter(sim: String?) {
        viewModelScope.launch {
            // Get data within the detected timeframe
            val allData = repository.vitalSigns.value
            val detectedTimeframe = _actualDetectedTimeframe.value
            val timeframeFilteredData = if (detectedTimeframe != null) {
                allData.filter {
                    it.timeObj >= detectedTimeframe.first && it.timeObj <= detectedTimeframe.second
                }
            } else {
                allData
            }

        if (sim == "<all>" || sim == null) {
            _selectedFilters.value = _selectedFilters.value.copy(sim = null)

            // When sim is not selected, show all mannequins regardless of date
            _filterValues.value = repository.getUniqueFilterValues(
                    vitalSignsToAnalyze = timeframeFilteredData, // Use timeframe-filtered data
                selectedSim = null,
                dateFilter = _selectedFilters.value.date,
                forceAllMannequins = true,
                simulationMode = _selectedFilters.value.simulationMode
            )
        } else {
            // Update the filter
            _selectedFilters.value = _selectedFilters.value.copy(sim = sim)

            // Update filter values to show only mannequins for this sim
            _filterValues.value = repository.getUniqueFilterValues(
                    vitalSignsToAnalyze = timeframeFilteredData, // Use timeframe-filtered data
                selectedSim = sim,
                dateFilter = _selectedFilters.value.date,
                forceAllMannequins = false,
                simulationMode = _selectedFilters.value.simulationMode
            )

            // If current mannequin is not valid for this sim, reset it
            val currentMannequin = _selectedFilters.value.mannequin
            val mannequinOptions = _filterValues.value?.mannequinValues ?: emptyList()

            if (currentMannequin != null && currentMannequin != "<all>" && !mannequinOptions.contains(currentMannequin)) {
                _selectedFilters.value = _selectedFilters.value.copy(mannequin = null)
            }
        }

        // Apply the filter
        applyFilters()
        }
    }

    /**
     * Set the date filter
     */
    fun setDateFilter(date: String?) {
        viewModelScope.launch {
            // Get data within the detected timeframe
            val allData = repository.vitalSigns.value
            val detectedTimeframe = _actualDetectedTimeframe.value
            val timeframeFilteredData = if (detectedTimeframe != null) {
                allData.filter {
                    it.timeObj >= detectedTimeframe.first && it.timeObj <= detectedTimeframe.second
                }
            } else {
                allData
            }

        if (date == "<all>" || date == null) {
            _selectedFilters.value = _selectedFilters.value.copy(date = null)
        } else {
            _selectedFilters.value = _selectedFilters.value.copy(date = date)
        }

        // Update the filter values to match the new selection
        _filterValues.value = repository.getUniqueFilterValues(
                vitalSignsToAnalyze = timeframeFilteredData, // Use timeframe-filtered data
            selectedSim = _selectedFilters.value.sim,
            dateFilter = _selectedFilters.value.date,
            forceAllMannequins = _selectedFilters.value.sim == null,
            simulationMode = _selectedFilters.value.simulationMode
        )

        // Apply the filter
        applyFilters()
        }
    }

    /**
     * Set the mannequin filter
     */
    fun setMannequinFilter(mannequin: String?) {
        if (mannequin == "<all>" || mannequin == null) {
            _selectedFilters.value = _selectedFilters.value.copy(mannequin = null)
        } else {
            _selectedFilters.value = _selectedFilters.value.copy(mannequin = mannequin)
        }

        // Apply the filter
        applyFilters()
    }

    /**
     * Update selected filters directly
     */
    fun updateSelectedFilters(filters: SelectedFilters) {
        _selectedFilters.value = filters
    }

    /**
     * Reset the data loading state to idle
     */
    fun resetDataLoadingState() {
        _dataLoadingState.value = DataLoadingState.Idle
    }

    /**
     * Save current vital signs data to the database with simulation metadata
     * @param simulationType Type of simulation (Training or Graded)
     * @param simulationName Name of the simulation
     * @param simulationRunNumber Run number of the simulation
     * @param startTime Start time of the simulation
     * @return Boolean indicating success
     */
    suspend fun saveCurrentDataToDatabase(
        simulationType: String?,
        simulationName: String,
        simulationRunNumber: String,
        startTime: Date?
    ): Boolean {
        if (!isDatabaseAvailable) {
            Log.w(TAG, "Cannot save to database: Database is not available")
            _dataLoadingState.value = DataLoadingState.Error("Database is not available")
            return false
        }

        _dataLoadingState.value = DataLoadingState.Loading("Saving data to database...")

        try {
            val sessionMetadata = SessionMetadata(
                type = simulationType ?: "Unknown",
                name = simulationName,
                runNumber = simulationRunNumber,
                startTime = startTime ?: Date(),
                endTime = Date()
            )

            val recordsSaved = withContext(Dispatchers.IO) {
                repository.saveCurrentDataToDatabase(sessionMetadata)
            }

            if (recordsSaved > 0) {
                _dataLoadingState.value = DataLoadingState.Success(recordsSaved)
                Log.d(TAG, "Successfully saved $recordsSaved records to database with metadata")

                // Update record count
                getDatabaseRecordCount()
                return true
            } else {
                _dataLoadingState.value = DataLoadingState.Error("No records were saved to database")
                Log.w(TAG, "No records were saved to database")
                return false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error saving to database", e)
            CrashLogger.logError(TAG, "Error saving to database in VitalSignsViewModel", e)
            _dataLoadingState.value = DataLoadingState.Error("Error saving to database: ${e.message}")
            return false
        }
    }

    /**
     * Export vital signs data to CSV file
     * @param simulationType Type of simulation (Training or Graded)
     * @param simulationName Name of the simulation
     * @param simulationRunNumber Run number of the simulation
     */
    fun exportDataToCsv(
        simulationType: String?,
        simulationName: String,
        simulationRunNumber: String
    ) {
        viewModelScope.launch {
            _dataLoadingState.value = DataLoadingState.Loading("Exporting data to CSV...")

            try {
                // Get the data to export
                val dataToExport = vitalSigns.value

                if (dataToExport.isEmpty()) {
                    _dataLoadingState.value = DataLoadingState.Error("No data to export")
                    return@launch
                }

                // Generate a filename with simulation details
                val dateFormat = SimpleDateFormat("yyyy-MM-dd_HH-mm", Locale.getDefault())
                val timestamp = dateFormat.format(Date())
                val filename = "${simulationType ?: "Unknown"}_${simulationName}_Run${simulationRunNumber}_${timestamp}.csv"

                // Create the CSV content
                val csvContent = withContext(Dispatchers.Default) {
                    buildCsvContent(dataToExport)
                }

                // Save the CSV file
                val success = withContext(Dispatchers.IO) {
                    repository.saveDataToCsvFile(filename, csvContent)
                }

                if (success) {
                    _dataLoadingState.value = DataLoadingState.Success(dataToExport.size)
                    Log.d(TAG, "Successfully exported data to CSV: $filename")
                } else {
                    _dataLoadingState.value = DataLoadingState.Error("Failed to export data to CSV")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error exporting data to CSV", e)
                CrashLogger.logError(TAG, "Error exporting data to CSV in VitalSignsViewModel", e)
                _dataLoadingState.value = DataLoadingState.Error("Error exporting data to CSV: ${e.message}")
            }
        }
    }

    /**
     * Builds CSV content from vital signs data
     */
    private fun buildCsvContent(vitalSigns: List<VitalSign>): String {
        val csvBuilder = StringBuilder()

        // Add header row
        csvBuilder.append("Time,Heart Rate,SpO2,NIBP (Sys),NIBP (Dia),NIBP (MAP),Temp,Respiratory Rate,EtCO2\n")

        // Add data rows
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())

        vitalSigns.forEach { vs ->
            csvBuilder.append(dateFormat.format(vs.timeObj)).append(",")
            csvBuilder.append(vs.hr ?: "").append(",")
            csvBuilder.append(vs.spO2 ?: "").append(",")
            csvBuilder.append(vs.nibpSys ?: "").append(",")
            csvBuilder.append(vs.nibpDia ?: "").append(",")
            csvBuilder.append(vs.nibpMap ?: "").append(",")
            csvBuilder.append(vs.temp1 ?: "").append(",")
            csvBuilder.append(vs.respRate ?: "").append(",")
            csvBuilder.append(vs.etCO2 ?: "").append("\n")
        }

        return csvBuilder.toString()
    }

    /**
     * Load data from the database
     * @return List of vital signs
     */
    suspend fun loadDataFromDatabase(): List<VitalSign> {
        if (!isDatabaseAvailable) {
            Log.w(TAG, "Cannot load from database: Database is not available")
            _dataLoadingState.value = DataLoadingState.Error("Database is not available")
            return emptyList()
        }

        _dataLoadingState.value = DataLoadingState.Loading("Loading data from database...")

        try {
            val data = withContext(Dispatchers.IO) {
                repository.loadDataFromDatabase()
            }

            if (data.isNotEmpty()) {
                // Update state with loaded data
                _dataLoadingState.value = DataLoadingState.Success(data.size)
                Log.d(TAG, "Successfully loaded ${data.size} records from database")

                // Update filter values
                _filterValues.value = repository.getUniqueFilterValues()

                // Apply default filters
                resetFilters()

                return data
            } else {
                _dataLoadingState.value = DataLoadingState.Error("No data found in database")
                Log.w(TAG, "No data found in database")
                return emptyList()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error loading from database", e)
            CrashLogger.logError(TAG, "Error loading from database in VitalSignsViewModel", e)
            _dataLoadingState.value = DataLoadingState.Error("Error loading from database: ${e.message}")
            return emptyList()
        }
    }

    /**
     * Get count of records in the database
     */
    fun getDatabaseRecordCount() {
        if (!isDatabaseAvailable) {
            _databaseRecordCount.value = 0
            return
        }

        viewModelScope.launch {
            try {
                val count = withContext(Dispatchers.IO) {
                    repository.getDatabaseRecordCount()
                }
                _databaseRecordCount.value = count
                Log.d(TAG, "Database record count: $count")
            } catch (e: Exception) {
                Log.e(TAG, "Error getting database record count", e)
                CrashLogger.logError(TAG, "Error getting database record count", e)
                _databaseRecordCount.value = 0
                // Set database as unavailable if there's an error
                _isDatabaseAvailable.value = false
            }
        }
    }

    /**
     * Clear all data from the database
     */
    suspend fun clearDatabase() {
        if (!isDatabaseAvailable) {
            Log.w(TAG, "Cannot clear database: Database is not available")
            _dataLoadingState.value = DataLoadingState.Error("Database is not available")
            return
        }

        _dataLoadingState.value = DataLoadingState.Loading("Clearing database...")

        try {
            withContext(Dispatchers.IO) {
                repository.clearDatabase()
            }
            _dataLoadingState.value = DataLoadingState.Success(0)
            Log.d(TAG, "Database cleared successfully")
            // Update record count
            _databaseRecordCount.value = 0
        } catch (e: Exception) {
            Log.e(TAG, "Error clearing database", e)
            CrashLogger.logError(TAG, "Error clearing database in VitalSignsViewModel", e)
            _dataLoadingState.value = DataLoadingState.Error("Error clearing database: ${e.message}")
        }
    }

    /**
     * Check if database is available
     */
    private fun checkDatabaseAvailability() {
        viewModelScope.launch {
            try {
                withContext(Dispatchers.IO) {
                    repository.getDatabaseRecordCount()
                }
                _isDatabaseAvailable.value = true
                Log.d(TAG, "Database is available")
            } catch (e: Exception) {
                _isDatabaseAvailable.value = false
                Log.e(TAG, "Database is not available", e)
                CrashLogger.logError(TAG, "Database is not available in VitalSignsViewModel", e)
            }
        }
    }

    /**
     * Set the start and end time filters
     */
    fun setTimeRangeFilter(startTime: String?, endTime: String?) {
        // Check if the values actually changed to avoid unnecessary updates
        if (_selectedFilters.value.startTime != startTime || _selectedFilters.value.endTime != endTime) {
            _selectedFilters.value = _selectedFilters.value.copy(
                startTime = startTime,
                endTime = endTime
            )
            // Important: Trigger applyFilters after time range change
            applyFilters()
        }
    }

    /**
     * Save current vital signs data to the database with default metadata
     * @return Boolean indicating success
     */
    suspend fun saveCurrentDataToDatabase(): Boolean {
        // Set default session metadata
        val defaultMetadata = SessionMetadata(
            type = "Unknown",
            name = "Unknown",
            runNumber = "1",
            startTime = Date(),
            endTime = Date()
        )

        val recordsSaved = repository.saveCurrentDataToDatabase(defaultMetadata)

        if (recordsSaved > 0) {
            _dataLoadingState.value = DataLoadingState.Success(recordsSaved)
            Log.d(TAG, "Successfully saved $recordsSaved records to database with default metadata")

            // Update record count
            getDatabaseRecordCount()
            return true
        } else {
            _dataLoadingState.value = DataLoadingState.Error("No records were saved to database")
            Log.w(TAG, "No records were saved to database")
            return false
        }
    }

    /**
     * Export filtered vital signs data to CSV
     * @param context Application context
     * @param uri The URI to write the CSV to
     * @return Boolean indicating success
     */
    suspend fun exportToCsv(context: Context, uri: Uri): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val data = _filteredVitalSigns.value

                if (data.isEmpty()) {
                    return@withContext false
                }

                // Create CSV content
                val csvContent = StringBuilder()

                // Header
                csvContent.append("Time,Sim,Mannequin,HR,SpO2,NIBP_SYS,NIBP_DIA,NIBP_MAP,Temp,RespRate,EtCO2,InSimTimeframe\n")

                // Format for timestamps
                val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US)

                // Data rows
                data.forEach { vs ->
                    csvContent.append(dateFormat.format(vs.timeObj)).append(",")
                    csvContent.append(vs.sim ?: "").append(",")
                    csvContent.append(vs.overrideMannequin ?: "").append(",")
                    csvContent.append(vs.hr ?: "").append(",")
                    csvContent.append(vs.spO2 ?: "").append(",")
                    csvContent.append(vs.nibpSys ?: "").append(",")
                    csvContent.append(vs.nibpDia ?: "").append(",")
                    csvContent.append(vs.nibpMap ?: "").append(",")
                    csvContent.append(vs.temp1 ?: "").append(",")
                    csvContent.append(vs.respRate ?: "").append(",")
                    csvContent.append(vs.etCO2 ?: "").append(",")
                    csvContent.append(if (vs.inPreciseSimTimeframe) "Yes" else "No").append("\n")
                }

                // Write to the URI
                context.contentResolver.openOutputStream(uri)?.use { outputStream ->
                    OutputStreamWriter(outputStream).use { writer ->
                        writer.write(csvContent.toString())
                    }
                }

                true
            } catch (e: Exception) {
                Log.e(TAG, "Error exporting to CSV", e)
                CrashLogger.logError(TAG, "Error exporting to CSV", e)
                false
            }
        }
    }

    /**
     * Export data to CSV file - non-suspend version that can be called from UI directly
     */
    fun exportToCSVFile(uri: Uri) {
        viewModelScope.launch {
            try {
                val success = exportToCsv(context, uri)
                if (success) {
                    Log.d(TAG, "Successfully exported data to CSV")
                } else {
                    Log.e(TAG, "Failed to export data to CSV")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error exporting to CSV", e)
                CrashLogger.logError(TAG, "Error exporting to CSV", e)
            }
        }
    }

    /**
     * Update filter values from repository
     */
    fun updateFilterValues() {
        viewModelScope.launch {
            if (vitalSigns.value.isNotEmpty()) {
                _filterValues.value = repository.getUniqueFilterValues(
                    simulationMode = _selectedFilters.value.simulationMode
                )
            }
        }
    }

    /**
     * Factory for creating VitalSignsViewModel
     */
    class Factory(private val repository: VitalSignsRepository, private val context: Context) : ViewModelProvider.Factory {
        @Suppress("UNCHECKED_CAST")
        override fun <T : ViewModel> create(modelClass: Class<T>): T {
            if (modelClass.isAssignableFrom(VitalSignsViewModel::class.java)) {
                return VitalSignsViewModel(repository, context) as T
            }
            throw IllegalArgumentException("Unknown ViewModel class")
        }
    }

    /**
     * Set the simulation mode filter and reapply filters
     * @param mode The simulation mode ("Graded" or "Training")
     */
    fun setSimulationMode(mode: String) {
        if (_selectedFilters.value.simulationMode != mode) {
            // Reset the sim filter when changing modes to avoid invalid combinations
            _selectedFilters.value = _selectedFilters.value.copy(
                simulationMode = mode,
                sim = null // Reset sim filter when changing modes
            )

            // Update filter values to match the new mode
            _filterValues.value = repository.getUniqueFilterValues(
                selectedSim = null,
                dateFilter = _selectedFilters.value.date,
                forceAllMannequins = true,
                simulationMode = mode
            )

            // Apply filters with the new mode
            applyFilters()

            // Update the timeframe summary based on the new mode
            val filteredVitals = repository.vitalSigns.value.filter { vitalSign ->
                when (mode) {
                    "Graded" -> vitalSign.sim == null || !vitalSign.sim.startsWith("Training")
                    "Training" -> vitalSign.sim == null || vitalSign.sim.startsWith("Training")
                    else -> true
                }
            }

            if (filteredVitals.isNotEmpty()) {
                val firstTimestamp = filteredVitals.minByOrNull { it.timeObj }?.timeObj
                if (firstTimestamp != null) {
                    val detectorResult = SimulationTimeDetector.identifySimulationAndTimeframe(
                        vitalSigns = filteredVitals,
                        date = firstTimestamp
                    )
                    updateDetectedTimeframeSummary(detectorResult)
                }
            }
        }
    }

    /**
     * Update data loading flags based on repository state
     */
    fun updateDataLoadingFlags() {
        viewModelScope.launch {
            _dataLoaded.value = repository.vitalSigns.value.isNotEmpty()
        }
    }

    /**
     * Process data loaded from a file or database
     * This is called after data is loaded from a source but before it's displayed.
     */
    fun processLoadedData() {
        viewModelScope.launch {
            // Add processing logic for file data

            if (repository.vitalSigns.value.isNotEmpty()) {
                val allData = repository.vitalSigns.value
                val firstTimestamp = allData.minByOrNull { it.timeObj }?.timeObj

                if (firstTimestamp != null) {
                    val date = firstTimestamp

                    // *** ADDED: Detect actual timeframe using SimulationTimeDetector ***
                    Log.d(TAG, "Detecting simulation and timeframe using SimulationTimeDetector")
                    val detectorResult = SimulationTimeDetector.identifySimulationAndTimeframe(
                        vitalSigns = repository.vitalSigns.value,
                        date = date
                    )

                    // Store the detected timeframe
                    _actualDetectedTimeframe.value = detectorResult?.second
                    Log.d(TAG, "Detected timeframe: ${detectorResult?.second?.first} to ${detectorResult?.second?.second}")

                    // Update detailed timeframe summary broken down by mannequin
                    updateDetectedTimeframeSummary(detectorResult)

                    Log.d(TAG, "Filtering data based on detected timeframe")

                    // Filter data based on detected timeframe
                    val initialFilteredData = if (_actualDetectedTimeframe.value != null) {
                        repository.vitalSigns.value.filter {
                            it.timeObj >= _actualDetectedTimeframe.value!!.first &&
                            it.timeObj <= _actualDetectedTimeframe.value!!.second
                        }
                    } else {
                        repository.vitalSigns.value
                    }

                    // Run filtering logic to update the UI state
                    if (initialFilteredData.isNotEmpty()) {
                        // Update filter values
                        _filterValues.value = repository.getUniqueFilterValues(
                            vitalSignsToAnalyze = initialFilteredData,
                            simulationMode = _selectedFilters.value.simulationMode
                        )

                        // Set filtered vital signs (initially show all within timeframe)
                        _filteredVitalSigns.value = initialFilteredData

                        // Update summary and available vitals
                        _statsSummary.value = repository.calculateStatsSummary(initialFilteredData)
                        detectAvailableVitals(initialFilteredData)

                        // Update compliance with auto-detected scenario
                        val scenario = determineScenarioFromData(initialFilteredData)
                        _complianceStats.value = calculateComplianceStats(
                            initialFilteredData,
                            scenario,
                            true // Always use data-driven timeframe
                        )

                        // Set data loaded flag
                        _dataLoaded.value = true
                    }
                }
            }
        }
    }

    /**
     * Process data loaded from a document URI (directory)
     */
    fun processLoadedDirectoryData() {
        viewModelScope.launch {
            // Similar to processLoadedData but for directory loading

            if (repository.vitalSigns.value.isNotEmpty()) {
                val allData = repository.vitalSigns.value
                val firstTimestamp = allData.minByOrNull { it.timeObj }?.timeObj

                if (firstTimestamp != null) {
                    val date = firstTimestamp

                    // *** ADDED: Detect actual timeframe using SimulationTimeDetector ***
                    Log.d(TAG, "Detecting simulation timeframe from directory data")
                    val detectorResult = SimulationTimeDetector.identifySimulationAndTimeframe(
                        vitalSigns = repository.vitalSigns.value,
                        date = date
                    )

                    // Store the detected timeframe
                    _actualDetectedTimeframe.value = detectorResult?.second
                    Log.d(TAG, "Detected timeframe: ${detectorResult?.second?.first} to ${detectorResult?.second?.second}")

                    // Filter data based on detected timeframe
                    val initialFilteredData = if (_actualDetectedTimeframe.value != null) {
                        repository.vitalSigns.value.filter {
                            it.timeObj >= _actualDetectedTimeframe.value!!.first &&
                            it.timeObj <= _actualDetectedTimeframe.value!!.second
                        }
                    } else {
                        repository.vitalSigns.value
                    }

                    // Update filter values
                    _filterValues.value = repository.getUniqueFilterValues(
                        vitalSignsToAnalyze = initialFilteredData,
                        simulationMode = _selectedFilters.value.simulationMode
                    )

                    // If we have data, apply the filters
                    if (initialFilteredData.isNotEmpty()) {
                        // Set filtered vital signs (initially show all within timeframe)
                        _filteredVitalSigns.value = initialFilteredData

                        // Update summary and available vitals
                        _statsSummary.value = repository.calculateStatsSummary(initialFilteredData)
                        detectAvailableVitals(initialFilteredData)

                        // Update compliance with auto-detected scenario
                        val scenario = determineScenarioFromData(initialFilteredData)
                        _complianceStats.value = calculateComplianceStats(
                            initialFilteredData,
                            scenario,
                            true // Always use data-driven timeframe
                        )

                        // Set data loaded flag
                        _dataLoaded.value = true
                    }
                }
            }
        }
    }

    /**
     * Load data from a file URI
     * @param fileUri The URI of the file to load
     */
    fun loadDataFromFileUri(fileUri: Uri) {
        viewModelScope.launch {
            try {
                _dataLoadingState.value = DataLoadingState.Loading("Loading file data...")
                _filteredVitalSigns.value = emptyList() // Clear current data

                val vitals = withContext(Dispatchers.IO) {
                    repository.loadDataFromFileUri(fileUri, context)
                }

                if (vitals.isNotEmpty()) {
                    // Set data loaded flag
                    _dataLoaded.value = true

                    // *** ADDED: Detect actual timeframe using SimulationTimeDetector ***
                    val firstTimestamp = vitals.minByOrNull { it.timeObj }?.timeObj
                    if (firstTimestamp != null) {
                        val detectorResult = SimulationTimeDetector.identifySimulationAndTimeframe(
                            vitalSigns = vitals,
                            date = firstTimestamp
                        )

                        // Store detected timeframe
                        _actualDetectedTimeframe.value = detectorResult?.second
                    }

                    // Use precise timeframe detection - this adds flags to each vital sign
                    // repository.processLoadedData(true) // Removed - functionality now handled in ViewModel

                    // Set loading state to success
                    _dataLoadingState.value = DataLoadingState.Success(vitals.size)

                    // Reset filters to show all data within the detected timeframe
                    resetFilters()
                } else {
                    _dataLoadingState.value = DataLoadingState.Error("No vital signs found in file")
                }
            } catch (e: Exception) {
                _dataLoadingState.value = DataLoadingState.Error(e.message ?: "Unknown error")
                CrashLogger.logError(TAG, "Error loading file", e)
            }
        }
    }

    /**
     * Load data from a directory URI
     * @param directoryUri The URI of the directory to load
     */
    fun loadDataFromDirectoryUri(directoryUri: Uri) {
        viewModelScope.launch {
            try {
                _dataLoadingState.value = DataLoadingState.Loading("Loading directory data...")
                _filteredVitalSigns.value = emptyList() // Clear current data

                val vitals = withContext(Dispatchers.IO) {
                    repository.loadDataFromDocumentUri(directoryUri, context)
                }

                if (vitals.isNotEmpty()) {
                    // Set data loaded flag
                    _dataLoaded.value = true

                    // *** ADDED: Detect actual timeframe using SimulationTimeDetector ***
                    val firstTimestamp = vitals.minByOrNull { it.timeObj }?.timeObj
                    if (firstTimestamp != null) {
                        val detectorResult = SimulationTimeDetector.identifySimulationAndTimeframe(
                            vitalSigns = vitals,
                            date = firstTimestamp
                        )

                        // Store detected timeframe
                        _actualDetectedTimeframe.value = detectorResult?.second
                    }

                    // Use precise timeframe detection - this adds flags to each vital sign
                    // repository.processLoadedData(true) // Removed - functionality now handled in ViewModel

                    // Set loading state to success
                    _dataLoadingState.value = DataLoadingState.Success(vitals.size)

                    // Reset filters to show all data within the detected timeframe
                    resetFilters()
                } else {
                    _dataLoadingState.value = DataLoadingState.Error("No vital signs found in directory")
                }
            } catch (e: Exception) {
                _dataLoadingState.value = DataLoadingState.Error(e.message ?: "Unknown error")
                CrashLogger.logError(TAG, "Error loading directory", e)
            }
        }
    }

    fun setSimulationModeFilter(mode: String?) { // New function
        viewModelScope.launch {
            val currentFilters = _selectedFilters.value
            _selectedFilters.value = currentFilters.copy(simulationMode = mode ?: "Graded")
            // Update filter values for sim dropdown based on the new mode
            _filterValues.value = repository.getUniqueFilterValues(simulationMode = mode ?: "<all>") // Pass "<all>" if mode is null to get all sims
            applyFilters()
        }
    }

    /**
     * Add an annotation
     * @param text The annotation text
     * @param type The annotation type
     * @param vitalSign Optional vital sign name
     * @param mannequin Optional mannequin name
     * @param relatedNonComplianceEventId Optional ID of a related non-compliance event
     */
    fun addAnnotation(
        text: String,
        type: AnnotationType = AnnotationType.NOTE,
        vitalSign: String? = null,
        mannequin: String? = null,
        relatedNonComplianceEventId: String? = null
    ) {
        val annotation = Annotation(
            text = text,
            type = type,
            vitalSign = vitalSign,
            mannequin = mannequin,
            relatedNonComplianceEventId = relatedNonComplianceEventId
        )
        repository.addAnnotation(annotation)
    }

    /**
     * Delete an annotation
     * @param annotationId The ID of the annotation to delete
     */
    fun deleteAnnotation(annotationId: String) {
        repository.deleteAnnotation(annotationId)
    }

    /**
     * Get annotations for a specific mannequin
     * @param mannequinName The mannequin name
     * @return List of annotations for the mannequin
     */
    fun getAnnotationsForMannequin(mannequinName: String): List<Annotation> {
        return repository.getAnnotationsForMannequin(mannequinName)
    }

    /**
     * Get annotations for a specific vital sign
     * @param vitalSign The vital sign name
     * @return List of annotations for the vital sign
     */
    fun getAnnotationsForVitalSign(vitalSign: String): List<Annotation> {
        return repository.getAnnotationsForVitalSign(vitalSign)
    }
}

/**
 * Tabs in the vital signs app
 */
enum class VitalSignsTab {
    HOME,
    DATA_EXPLORER,
    CPG_COMPLIANCE,
    VISUALIZATION,
    REAL_TIME_MONITOR
}

/**
 * Monitoring state
 */
sealed class MonitoringState {
    object Stopped : MonitoringState()
    object Starting : MonitoringState()
    object Running : MonitoringState()
    data class Error(val message: String) : MonitoringState()
}

/**
 * Compliance status
 */
enum class ComplianceStatus {
    EXCELLENT,
    GOOD,
    FAIR,
    POOR,
    UNKNOWN
}

/**
 * Statistics for a vital sign
 */
data class VitalSignStat(
    val name: String,
    val target: String,
    val actualRange: String,
    val meanValue: Double,
    val inRangePercent: Double,
    val coveragePercent: Double,
    val status: ComplianceStatus,
    val timeOutOfRangeMin: Double,
    val timeMonitoredMin: Double
)

/**
 * Compliance statistics
 */
data class ComplianceStats(
    val scenario: String = "",
    val durationMin: Double = 0.0,
    val overallScore: Double = 0.0,
    val overallStatus: ComplianceStatus = ComplianceStatus.UNKNOWN,
    val vitalStats: Map<String, VitalSignStat> = emptyMap(),
    val nonComplianceEvents: List<NonComplianceEvent> = emptyList()
)

/**
 * Selected filters data class
 */
data class SelectedFilters(
    val sim: String? = null,
    val date: String? = null,
    val mannequin: String? = null,
    val startTime: String? = null,
    val endTime: String? = null,
    val simulationMode: String = "Graded"  // Default to "Graded" (vs "Training")
)

/**
 * Session metadata class to store information about a monitoring session
 */
data class SessionMetadata(
    val type: String,      // Training or Graded
    val name: String,      // Simulation name
    val runNumber: String, // Run number
    val startTime: Date,   // Session start time
    val endTime: Date      // Session end time
)

/**
 * Data class to hold the range values for vital sign filtering
 */
data class VitalRangeFilter(
    val min: Float,
    val max: Float,
    val defaultMin: Float,
    val defaultMax: Float,
    val available: Boolean = false
)

/**
 * Data class to hold all vital range filters
 */
data class VitalRangeFilters(
    val heartRate: VitalRangeFilter = VitalRangeFilter(30f, 200f, 30f, 200f),
    val spo2: VitalRangeFilter = VitalRangeFilter(70f, 100f, 70f, 100f),
    val nibpSys: VitalRangeFilter = VitalRangeFilter(60f, 200f, 60f, 200f),
    val nibpDia: VitalRangeFilter = VitalRangeFilter(30f, 120f, 30f, 120f),
    val nibpMap: VitalRangeFilter = VitalRangeFilter(40f, 150f, 40f, 150f),
    val temp: VitalRangeFilter = VitalRangeFilter(35f, 42f, 35f, 42f),
    val respRate: VitalRangeFilter = VitalRangeFilter(4f, 40f, 4f, 40f),
    val etco2: VitalRangeFilter = VitalRangeFilter(10f, 60f, 10f, 60f)
)