package com.example.myapplication.ui.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.launch

/**
 * Dialog for database-related actions
 */
@Composable
fun DatabaseActionsDialog(
    recordCount: Int,
    onDismiss: () -> Unit,
    onSaveToDatabase: () -> Unit,
    onLoadFromDatabase: (() -> Unit)?,  // Made nullable
    onClearDatabase: () -> Unit,
    isDatabaseAvailable: Boolean = true // Add a parameter to check if database is available
) {
    var showConfirmClearDialog by remember { mutableStateOf(false) }
    var operationResult by remember { mutableStateOf<String?>(null) }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Database Actions") },
        text = {
            Column {
                if (!isDatabaseAvailable) {
                    // Show warning when database is not available
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 16.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.errorContainer
                        )
                    ) {
                        Column(modifier = Modifier.padding(16.dp)) {
                            Text(
                                text = "Database functionality is not available. This may be due to a Room configuration issue.",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onErrorContainer
                            )
                        }
                    }
                    Spacer(modifier = Modifier.height(8.dp))
                } else {
                    Text("Current database records: $recordCount")
                    Spacer(modifier = Modifier.height(16.dp))
                }
                
                if (operationResult != null) {
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 16.dp)
                    ) {
                        Column(modifier = Modifier.padding(16.dp)) {
                            Text(
                                text = operationResult!!,
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }
                    }
                    Spacer(modifier = Modifier.height(8.dp))
                }
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Button(
                        onClick = {
                            onSaveToDatabase()
                            operationResult = "Saving data to database..."
                        },
                        enabled = isDatabaseAvailable && recordCount == 0,
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("Save Current Data")
                    }
                    
                    // Only show the Load Data button if onLoadFromDatabase is not null
                    if (onLoadFromDatabase != null) {
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Button(
                            onClick = {
                                onLoadFromDatabase()
                                operationResult = "Loading data from database..."
                            },
                            enabled = isDatabaseAvailable,
                            modifier = Modifier.weight(1f)
                        ) {
                            Text("Load Data")
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                HorizontalDivider()
                Spacer(modifier = Modifier.height(16.dp))
                
                // Danger zone
                Text(
                    text = "Danger Zone",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.error
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Button(
                    onClick = { showConfirmClearDialog = true },
                    modifier = Modifier.align(Alignment.End),
                    enabled = isDatabaseAvailable
                ) {
                    Text("Clear Database")
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("Close")
            }
        },
        dismissButton = {}
    )
    
    // Confirmation dialog for clearing the database
    if (showConfirmClearDialog) {
        AlertDialog(
            onDismissRequest = { showConfirmClearDialog = false },
            title = { Text("Confirm Clear Database") },
            text = { Text("This will permanently delete all records from the database. This action cannot be undone.") },
            confirmButton = {
                TextButton(
                    onClick = {
                        onClearDatabase()
                        operationResult = "Database cleared"
                        showConfirmClearDialog = false
                    }
                ) {
                    Text("Clear", color = MaterialTheme.colorScheme.error)
                }
            },
            dismissButton = {
                TextButton(onClick = { showConfirmClearDialog = false }) {
                    Text("Cancel")
                }
            }
        )
    }
} 