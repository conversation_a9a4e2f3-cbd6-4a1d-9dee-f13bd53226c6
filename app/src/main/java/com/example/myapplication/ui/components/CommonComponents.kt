package com.example.myapplication.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.myapplication.data.repository.VitalSignsRepository
import com.example.myapplication.ui.theme.Danger
import com.example.myapplication.ui.theme.ExcellentColor
import com.example.myapplication.ui.theme.FairColor
import com.example.myapplication.ui.theme.GoodColor
import com.example.myapplication.ui.theme.Neutral
import com.example.myapplication.ui.theme.PoorColor
import com.example.myapplication.ui.theme.Success
import com.example.myapplication.ui.theme.UnknownColor
import com.example.myapplication.ui.viewmodel.ComplianceStatus

/**
 * Displays a vital sign in a styled card format
 */
@Composable
fun VitalSignBox(
    title: String,
    value: String,
    unit: String,
    status: String,
    normalRange: Pair<Double?, Double?>? = null,
    thresholds: Pair<Double?, Double?>? = null,
    accentColor: Color = MaterialTheme.colorScheme.primary,
    modifier: Modifier = Modifier
) {
    // Determine the color based on status and thresholds
    val (backgroundColor, valueColor) = when {
        status != "valid" -> Pair(Color.LightGray.copy(alpha = 0.2f), Neutral)
        thresholds != null -> {
            val (min, max) = thresholds
            val numValue = value.toDoubleOrNull()
            
            if (numValue != null && ((min != null && numValue < min) || (max != null && numValue > max))) {
                Pair(Danger.copy(alpha = 0.1f), Danger)
            } else {
                Pair(Success.copy(alpha = 0.1f), Success)
            }
        }
        normalRange != null -> {
            val (min, max) = normalRange
            val numValue = value.toDoubleOrNull()
            
            if (numValue != null && ((min != null && numValue < min) || (max != null && numValue > max))) {
                Pair(Color(0xFFFFF8E8), Color(0xFFFF9E00))
            } else {
                Pair(Color(0xFFEEFFEE), Success)
            }
        }
        else -> Pair(MaterialTheme.colorScheme.surfaceVariant, accentColor)
    }
    
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(4.dp),
        colors = CardDefaults.cardColors(
            containerColor = backgroundColor
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.Start
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )
            
            Row(
                verticalAlignment = Alignment.Bottom,
                modifier = Modifier.padding(top = 8.dp)
            ) {
                Text(
                    text = value,
                    style = MaterialTheme.typography.headlineLarge,
                    fontWeight = FontWeight.Bold,
                    color = valueColor
                )
                
                Spacer(modifier = Modifier.width(4.dp))
                
                Text(
                    text = unit,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                    modifier = Modifier.padding(bottom = 4.dp)
                )
            }
            
            // Add threshold or normal range information if available
            if (thresholds != null) {
                val (min, max) = thresholds
                val rangeText = when {
                    min != null && max != null -> "Target: $min-$max"
                    min != null -> "Target: ≥$min"
                    max != null -> "Target: ≤$max"
                    else -> ""
                }
                
                if (rangeText.isNotEmpty()) {
                    Text(
                        text = rangeText,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                    )
                }
            }
        }
    }
}

/**
 * Displays a connection status indicator
 */
@Composable
fun ConnectionStatusIndicator(status: VitalSignsRepository.ConnectionStatus) {
    val (color, text) = when (status) {
        VitalSignsRepository.ConnectionStatus.CONNECTED -> Pair(Success, "Connected")
        VitalSignsRepository.ConnectionStatus.CONNECTING -> Pair(FairColor, "Connecting...")
        VitalSignsRepository.ConnectionStatus.DISCONNECTED -> Pair(Neutral, "Disconnected")
        VitalSignsRepository.ConnectionStatus.ERROR -> Pair(Danger, "Error")
    }
    
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier
            .background(
                color = color.copy(alpha = 0.1f),
                shape = RoundedCornerShape(8.dp)
            )
            .padding(horizontal = 12.dp, vertical = 8.dp)
    ) {
        Box(
            modifier = Modifier
                .size(8.dp)
                .background(color, CircleShape)
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        Text(
            text = text,
            style = MaterialTheme.typography.bodyMedium,
            color = color,
            fontWeight = FontWeight.Medium
        )
    }
}

/**
 * Displays a compliance status indicator
 */
@Composable
fun ComplianceStatusIndicator(status: ComplianceStatus, score: Double) {
    val color = when (status) {
        ComplianceStatus.EXCELLENT -> ExcellentColor
        ComplianceStatus.GOOD -> GoodColor
        ComplianceStatus.FAIR -> FairColor
        ComplianceStatus.POOR -> PoorColor
        else -> UnknownColor
    }
    
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center,
        modifier = Modifier
            .clip(RoundedCornerShape(8.dp))
            .background(color.copy(alpha = 0.1f))
            .border(1.dp, color.copy(alpha = 0.3f), RoundedCornerShape(8.dp))
            .padding(16.dp)
    ) {
        Text(
            text = "${score.toInt()}%",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            color = color
        )
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = status.name,
            style = MaterialTheme.typography.bodyLarge,
            fontWeight = FontWeight.Medium,
            color = color
        )
    }
}

/**
 * Displays a section header
 */
@Composable
fun SectionHeader(title: String, modifier: Modifier = Modifier) {
    Column(modifier = modifier.fillMaxWidth()) {
        Text(
            text = title,
            style = MaterialTheme.typography.titleLarge,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary
        )
        
        Spacer(modifier = Modifier.height(4.dp))
        
        HorizontalDivider(
            color = MaterialTheme.colorScheme.primary.copy(alpha = 0.2f),
            thickness = 2.dp,
            modifier = Modifier.fillMaxWidth(0.3f)
        )
        
        Spacer(modifier = Modifier.height(16.dp))
    }
}

/**
 * Displays a metric card
 */
@Composable
fun MetricCard(
    title: String,
    value: String,
    subtitle: String? = null,
    color: Color = MaterialTheme.colorScheme.primary,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(4.dp),
        colors = CardDefaults.cardColors(
            containerColor = color.copy(alpha = 0.1f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleSmall,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                textAlign = TextAlign.Center
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = value,
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold,
                color = color,
                textAlign = TextAlign.Center
            )
            
            if (subtitle != null) {
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = subtitle,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f),
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

/**
 * Displays an event card
 */
@Composable
fun EventCard(
    title: String,
    description: String,
    timestamp: String,
    isUrgent: Boolean = false,
    modifier: Modifier = Modifier
) {
    val cardColor = if (isUrgent) Danger.copy(alpha = 0.1f) else MaterialTheme.colorScheme.surfaceVariant
    val borderColor = if (isUrgent) Danger.copy(alpha = 0.3f) else MaterialTheme.colorScheme.outlineVariant
    
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        colors = CardDefaults.cardColors(
            containerColor = cardColor
        ),
        shape = RoundedCornerShape(8.dp),
        border = androidx.compose.foundation.BorderStroke(1.dp, borderColor)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = if (isUrgent) Danger else MaterialTheme.colorScheme.onSurface
                )
                
                Text(
                    text = timestamp,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = description,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.8f)
            )
        }
    }
} 