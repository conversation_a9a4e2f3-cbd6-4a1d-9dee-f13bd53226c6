package com.example.myapplication.ui.screens

import android.content.Intent
import android.net.Uri
import android.provider.DocumentsContract
import android.util.Log
import android.widget.Toast
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Download
import androidx.compose.material.icons.filled.Folder
import androidx.compose.material.icons.filled.FolderOpen
import androidx.compose.material.icons.filled.Menu
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Save
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.filled.Sort
import androidx.compose.material.icons.filled.FileDownload
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Divider
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ExposedDropdownMenuBox
import androidx.compose.material3.ExposedDropdownMenuDefaults
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.RadioButton
import androidx.compose.material3.RangeSlider
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.myapplication.data.Constants
import com.example.myapplication.data.model.VitalSign
import com.example.myapplication.data.repository.VitalSignsRepository
import com.example.myapplication.ui.components.SectionHeader
import com.example.myapplication.ui.state.DataLoadingState
import com.example.myapplication.ui.viewmodel.VitalSignsViewModel
import com.example.myapplication.util.CrashLogger
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Locale
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.SnackbarDuration
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import com.example.myapplication.ui.viewmodel.VitalRangeFilters
import androidx.compose.ui.text.TextStyle
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.ExperimentalLayoutApi

// Top-level objects for logging events
object LogEvent {
    object Database {
        const val SaveToDatabase = "SAVE_TO_DATABASE"
        const val LoadFromDatabase = "LOAD_FROM_DATABASE"
        const val ClearDatabase = "CLEAR_DATABASE"
    }
}

/**
 * Temporary data class for vital range filters
 */
data class TempVitalRangeFilter(
    val min: Float = 0f,
    val max: Float = 100f,
    val defaultMin: Float = 0f,
    val defaultMax: Float = 100f,
    val available: Boolean = false
)

/**
 * Temporary data class for all vital range filters
 */
data class TempVitalRangeFilters(
    val heartRate: TempVitalRangeFilter = TempVitalRangeFilter(30f, 200f, 30f, 200f),
    val spo2: TempVitalRangeFilter = TempVitalRangeFilter(70f, 100f, 70f, 100f),
    val nibpSys: TempVitalRangeFilter = TempVitalRangeFilter(60f, 200f, 60f, 200f),
    val nibpDia: TempVitalRangeFilter = TempVitalRangeFilter(30f, 120f, 30f, 120f),
    val nibpMap: TempVitalRangeFilter = TempVitalRangeFilter(40f, 150f, 40f, 150f),
    val temp: TempVitalRangeFilter = TempVitalRangeFilter(35f, 42f, 35f, 42f),
    val respRate: TempVitalRangeFilter = TempVitalRangeFilter(4f, 40f, 4f, 40f),
    val etco2: TempVitalRangeFilter = TempVitalRangeFilter(10f, 60f, 10f, 60f),
    val fiCO2: TempVitalRangeFilter = TempVitalRangeFilter(0f, 20f, 0f, 20f)
)

/**
 * Screen for loading and exploring vital signs data from files
 */
@Composable
fun DataExplorerScreen(viewModel: VitalSignsViewModel) {
    val TAG = "DataExplorerScreen"
    Log.d(TAG, "DataExplorerScreen composable starting")

    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val snackbarHostState = remember { SnackbarHostState() }

    // State
    val dataLoadingState by viewModel.dataLoadingState.collectAsState()
    val vitalSigns by viewModel.vitalSigns.collectAsState()
    val filteredVitalSigns by viewModel.filteredVitalSigns.collectAsState()
    val filterValues by viewModel.filterValues.collectAsState()
    val selectedFilters by viewModel.selectedFilters.collectAsState()
    val statsSummary by viewModel.statsSummary.collectAsState()
    val databaseRecordCount by viewModel.databaseRecordCount.collectAsState()
    val detectedTimeframeSummary by viewModel.detectedTimeframeSummary.collectAsState()
    val sortConfig by viewModel.sortConfig.collectAsState()
    val vitalRangeFilters by viewModel.vitalRangeFilters.collectAsState()
    val availableVitalSigns by viewModel.availableVitalSigns.collectAsState()

    // Helper function for logging events
    fun logDataExplorerEvent(message: String) {
        Log.d(TAG, message)
    }

    // Placeholder for loggingService
    val loggingService = object {
        fun logEvent(event: String) {
            Log.d(TAG, "Event: $event")
        }
    }

    var directoryPath by remember { mutableStateOf("/sdcard/Download/propaq_data") }

    // UI state for filters
    var simFilterExpanded by remember { mutableStateOf(false) }
    var dateFilterExpanded by remember { mutableStateOf(false) }
    var mannequinFilterExpanded by remember { mutableStateOf(false) }
    var startTimeStr by remember { mutableStateOf("") }
    var endTimeStr by remember { mutableStateOf("") }

    // Apply filters state
    var simChoice by remember { mutableStateOf<String?>(null) }
    var dateChoice by remember { mutableStateOf<String?>(null) }
    var manChoice by remember { mutableStateOf<String?>(null) }

    // State for showing file picker dialog
    var showFilePickerDialog by remember { mutableStateOf(false) }

    var showClearConfirmDialog by remember { mutableStateOf(false) }

    var showFilterDialog by remember { mutableStateOf(false) }

    // Load database record count on initial composition
    LaunchedEffect(Unit) {
        viewModel.getDatabaseRecordCount()
    }

    // File picker launcher for directory
    val directoryPickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.OpenDocumentTree()
    ) { uri: Uri? ->
        if (uri != null) {
            try {
                Log.d(TAG, "Selected directory: $uri")
                // Take persistable URI permission
                val takeFlags = Intent.FLAG_GRANT_READ_URI_PERMISSION or
                        Intent.FLAG_GRANT_WRITE_URI_PERMISSION
                context.contentResolver.takePersistableUriPermission(uri, takeFlags)

                // Update the directory path
                directoryPath = uri.toString()

                // Show toast with selected directory
                Toast.makeText(
                    context,
                    "Directory selected: ${uri.lastPathSegment ?: uri}",
                    Toast.LENGTH_SHORT
                ).show()

                // Load data from selected directory
                viewModel.loadDataFromDirectory(uri.toString())
            } catch (e: Exception) {
                Log.e(TAG, "Error handling directory selection", e)
                Toast.makeText(context, "Error: ${e.message}", Toast.LENGTH_LONG).show()
            }
        }
    }

    // File picker launcher for single file
    val filePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.OpenDocument()
    ) { uri: Uri? ->
        if (uri != null) {
            try {
                Log.d(TAG, "Selected file: $uri")
                // Take persistable URI permission
                val takeFlags = Intent.FLAG_GRANT_READ_URI_PERMISSION or
                        Intent.FLAG_GRANT_WRITE_URI_PERMISSION
                context.contentResolver.takePersistableUriPermission(uri, takeFlags)

                // Load data from selected file
                viewModel.loadDataFromFile(uri.toString())
            } catch (e: Exception) {
                Log.e(TAG, "Error handling file selection", e)
                Toast.makeText(context, "Error: ${e.message}", Toast.LENGTH_LONG).show()
            }
        }
    }

    // File picker launcher for exporting CSV
    val exportCsvLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.CreateDocument("text/csv")
    ) { uri ->
        uri?.let {
            try {
                viewModel.exportToCSVFile(uri)
                Toast.makeText(context, "Exporting data...", Toast.LENGTH_SHORT).show()
            } catch (e: Exception) {
                Log.e("DataExplorerScreen", "Error exporting CSV", e)
                Toast.makeText(context, "Export error: ${e.message}", Toast.LENGTH_LONG).show()
            }
        }
    }

    // Show file picker dialog when needed
    if (showFilePickerDialog) {
        FilePickerDialog(
            onDismiss = { showFilePickerDialog = false },
            onSelectDirectory = {
                showFilePickerDialog = false
                directoryPickerLauncher.launch(null)
            },
            onSelectFile = {
                showFilePickerDialog = false
                filePickerLauncher.launch(arrayOf("application/json"))
            }
        )
    }

    // Get current colors from Material theme
    val primary = MaterialTheme.colorScheme.primary
    val surface = MaterialTheme.colorScheme.surface
    val error = MaterialTheme.colorScheme.error

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState())
    ) {
        // Header
        SectionHeader(title = "Data Explorer")

        Text(
            text = "Load vital signs data from Propaq JSON files",
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
        )

        Spacer(modifier = Modifier.height(24.dp))

        // Directory path input
        Text(
            text = "Data Directory",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Medium
        )

        Spacer(modifier = Modifier.height(8.dp))

        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Text(
                text = directoryPath,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                style = MaterialTheme.typography.bodyMedium
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        // File management actions
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 8.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            OutlinedButton(
                onClick = {
                    // Instead of calling viewModel.browseForDirectory
                    showFilePickerDialog = true
                },
                modifier = Modifier.weight(1f),
                shape = RoundedCornerShape(8.dp),
                border = BorderStroke(1.dp, primary)
            ) {
                Icon(
                    imageVector = Icons.Default.FolderOpen,
                    contentDescription = null,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text("Browse")
            }

            OutlinedButton(
                onClick = {
                    logDataExplorerEvent("Load Directory Button clicked")
                    viewModel.loadDataFromDirectory(directoryPath)
                },
                modifier = Modifier.weight(1f),
                shape = RoundedCornerShape(8.dp),
                border = BorderStroke(1.dp, primary)
            ) {
                Icon(
                    imageVector = Icons.Default.Download,
                    contentDescription = null,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text("Load Data")
            }
        }

        // Database actions
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 8.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Database Actions",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )

                    if (!viewModel.isDatabaseAvailable) {
                        Text(
                            text = "Database Unavailable",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.error
                        )
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                // Database availability warning
                if (!viewModel.isDatabaseAvailable) {
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 8.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.errorContainer
                        )
                    ) {
                        Column(modifier = Modifier.padding(12.dp)) {
                            Text(
                                text = "Database functionality is not available. The app will continue to work with in-memory data only.",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onErrorContainer
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(8.dp))
                }

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    // Save to Database Button
                    OutlinedButton(
                        onClick = {
                            loggingService.logEvent(LogEvent.Database.SaveToDatabase)
                            coroutineScope.launch {
                                try {
                                    val success = viewModel.saveCurrentDataToDatabase()
                                    if (success) {
                                        snackbarHostState.showSnackbar(
                                            message = "Successfully saved records to database",
                                            duration = SnackbarDuration.Short
                                        )
                                    } else {
                                        snackbarHostState.showSnackbar(
                                            message = "No records were saved to database",
                                            duration = SnackbarDuration.Short
                                        )
                                    }
                                } catch (e: Exception) {
                                    Log.e(TAG, "Error saving to database", e)
                                    CrashLogger.logError(TAG, "Error in database save operation", e)
                                    snackbarHostState.showSnackbar(
                                        message = "Error saving to database: ${e.message}",
                                        duration = SnackbarDuration.Long
                                    )
                                }
                            }
                        },
                        modifier = Modifier.weight(1f),
                        enabled = viewModel.isDatabaseAvailable
                    ) {
                        Icon(
                            imageVector = Icons.Default.Save,
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("Save to DB")
                    }

                    Spacer(modifier = Modifier.width(8.dp))

                    // Load from Database Button
                    OutlinedButton(
                        onClick = {
                            // Instead of showing the dialog, directly load data from database
                            coroutineScope.launch {
                                try {
                                    viewModel.loadDataFromDatabase()
                                    snackbarHostState.showSnackbar(
                                        message = "Data loaded successfully",
                                        duration = SnackbarDuration.Short
                                    )
                                } catch (e: Exception) {
                                    snackbarHostState.showSnackbar(
                                        message = "Error loading data: ${e.message}",
                                        duration = SnackbarDuration.Long
                                    )
                                }
                            }
                        },
                        modifier = Modifier.weight(1f),
                        enabled = viewModel.isDatabaseAvailable && databaseRecordCount > 0
                    ) {
                        Icon(
                            imageVector = Icons.Default.Download,
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("Load from DB")
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                OutlinedButton(
                    onClick = {
                        loggingService.logEvent(LogEvent.Database.ClearDatabase)
                        showClearConfirmDialog = true
                    },
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(8.dp),
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = error
                    ),
                    border = BorderStroke(1.dp, error),
                    enabled = viewModel.isDatabaseAvailable && databaseRecordCount > 0
                ) {
                    Icon(
                        imageVector = Icons.Default.Delete,
                        contentDescription = null,
                        tint = error,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("Clear Database", color = error)
                }

                if (databaseRecordCount > 0) {
                    Text(
                        text = "Database contains $databaseRecordCount records",
                        style = MaterialTheme.typography.bodySmall,
                        modifier = Modifier.padding(top = 8.dp)
                    )
                } else if (viewModel.isDatabaseAvailable) {
                    Text(
                        text = "Database is empty",
                        style = MaterialTheme.typography.bodySmall,
                        modifier = Modifier.padding(top = 8.dp)
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(24.dp))

        // Loading state
        when (dataLoadingState) {
            is DataLoadingState.Idle -> {
                if (vitalSigns.isEmpty()) {
                    // No data loaded yet
                    EmptyDataPlaceholder()
                } else {
                    // Data loaded but not actively loading
                    DataFilterAndView(
                        viewModel = viewModel,
                        onFilterReset = {
                            viewModel.resetFilters()
                        }
                    )
                }
            }
            is DataLoadingState.Loading -> {
                val message = (dataLoadingState as DataLoadingState.Loading).message
                LoadingIndicator(message)
            }
            is DataLoadingState.Success -> {
                val count = (dataLoadingState as DataLoadingState.Success).count
                DataFilterAndView(
                    viewModel = viewModel,
                    onFilterReset = {
                        viewModel.resetFilters()
                    }
                )
            }
            is DataLoadingState.Error -> {
                val errorMessage = (dataLoadingState as DataLoadingState.Error).message
                ErrorMessage(errorMessage, viewModel)
            }
        }
    }

    if (showClearConfirmDialog) {
        AlertDialog(
            onDismissRequest = { showClearConfirmDialog = false },
            title = { Text("Confirm Clear Database") },
            text = { Text("Are you sure you want to clear all database records? This action cannot be undone.") },
            confirmButton = {
                TextButton(
                    onClick = {
                        coroutineScope.launch {
                            try {
                                viewModel.clearDatabase()
                                snackbarHostState.showSnackbar(
                                    message = "Database cleared successfully",
                                    duration = SnackbarDuration.Short
                                )
                            } catch (e: Exception) {
                                Log.e(TAG, "Error clearing database", e)
                                CrashLogger.logError(TAG, "Error in database clear operation", e)
                                snackbarHostState.showSnackbar(
                                    message = "Error clearing database: ${e.message}",
                                    duration = SnackbarDuration.Long
                                )
                            }
                        }
                        showClearConfirmDialog = false
                    }
                ) {
                    Text("Clear", color = MaterialTheme.colorScheme.error)
                }
            },
            dismissButton = {
                TextButton(onClick = { showClearConfirmDialog = false }) {
                    Text("Cancel")
                }
            }
        )
    }
}

/**
 * Dialog to choose between directory and file selection
 */
@Composable
private fun FilePickerDialog(
    onDismiss: () -> Unit,
    onSelectDirectory: () -> Unit,
    onSelectFile: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Choose Import Type") },
        text = { Text("Would you like to select a directory with multiple JSON files, or a single JSON file?") },
        confirmButton = {
            TextButton(onClick = onSelectDirectory) {
                Text("Directory (Multiple Files)")
            }
        },
        dismissButton = {
            TextButton(onClick = onSelectFile) {
                Text("Single File")
            }
        }
    )
}

/**
 * Get file system path from Uri
 */
private fun getPathFromUri(uri: Uri): String? {
    return when {
        DocumentsContract.isDocumentUri(null, uri) -> {
            // For document URIs, format as a displayable path
            val docId = DocumentsContract.getDocumentId(uri)
            uri.toString()
        }
        uri.scheme == "content" -> {
            // For content URIs, return as is
            uri.toString()
        }
        uri.scheme == "file" -> {
            // For file URIs, extract the path
            uri.path
        }
        else -> {
            // Fallback
            uri.toString()
        }
    }
}

@Composable
private fun EmptyDataPlaceholder() {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(200.dp),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "No data loaded",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onSurface
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = "Please select a folder containing Propaq JSON files using the 'Browse' button, then click 'Load Data'.",
                textAlign = TextAlign.Center,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                style = MaterialTheme.typography.bodyMedium
            )
        }
    }
}

@Composable
private fun LoadingIndicator(message: String = "Loading data...") {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(200.dp),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            CircularProgressIndicator(
                modifier = Modifier.padding(16.dp)
            )
            Text(
                text = message,
                style = MaterialTheme.typography.bodyLarge
            )
        }
    }
}

@Composable
private fun ErrorMessage(errorMessage: String, viewModel: VitalSignsViewModel) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .background(
                    color = MaterialTheme.colorScheme.errorContainer,
                    shape = MaterialTheme.shapes.medium
                )
                .padding(16.dp)
    ) {
        Text(
                text = "Error Loading Data",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onErrorContainer,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = errorMessage,
                color = MaterialTheme.colorScheme.onErrorContainer,
            textAlign = TextAlign.Center
        )

            Spacer(modifier = Modifier.height(16.dp))

            Button(
                onClick = {
                    // Reset the data loading state to idle to allow the user to try again
                    viewModel.resetDataLoadingState()
                },
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.onErrorContainer,
                    contentColor = MaterialTheme.colorScheme.errorContainer
                )
            ) {
                Text("Please try again")
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun DataFilterAndView(
    viewModel: VitalSignsViewModel,
    onFilterReset: () -> Unit
) {
    // Collect necessary states from ViewModel
    val filteredVitalSigns by viewModel.filteredVitalSigns.collectAsState()
    val filterValues by viewModel.filterValues.collectAsState()
    val selectedFilters by viewModel.selectedFilters.collectAsState()
    val statsSummary by viewModel.statsSummary.collectAsState()
    val detectedTimeframeSummary by viewModel.detectedTimeframeSummary.collectAsState()
    val sortConfig by viewModel.sortConfig.collectAsState()
    val vitalRangeFilters by viewModel.vitalRangeFilters.collectAsState()
    val availableVitalSigns by viewModel.availableVitalSigns.collectAsState()

    // Local UI state for dropdown expansion and timeframe toggle
    var simFilterExpanded by remember { mutableStateOf(false) }
    var dateFilterExpanded by remember { mutableStateOf(false) }
    var mannequinFilterExpanded by remember { mutableStateOf(false) }

    // Local state for time text fields (could potentially move to ViewModel if complex validation needed)
    var startTimeStr by remember { mutableStateOf(selectedFilters.startTime ?: "") }
    var endTimeStr by remember { mutableStateOf(selectedFilters.endTime ?: "") }

    // Local state for animation
    var visible by remember { mutableStateOf(true) }

    // Context for file operations
    val context = LocalContext.current

    // CSV export launcher
    val exportCsvLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.CreateDocument("text/csv")
    ) { uri ->
        uri?.let {
            try {
                viewModel.exportToCSVFile(uri)
                Toast.makeText(context, "Exporting data...", Toast.LENGTH_SHORT).show()
            } catch (e: Exception) {
                Log.e("DataExplorerScreen", "Error exporting CSV", e)
                Toast.makeText(context, "Export error: ${e.message}", Toast.LENGTH_LONG).show()
            }
        }
    }

    // Effect to animate when filters change
    LaunchedEffect(selectedFilters) {
        visible = false
        kotlinx.coroutines.delay(100)
        visible = true
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
    ) {
        // Data summary card
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Data Summary",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )

                    Text(
                        text = "${filteredVitalSigns.size} records",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.primary
                    )
                }

                HorizontalDivider(
                    modifier = Modifier.padding(vertical = 8.dp),
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.1f)
                )

                // Detected Timeframe Section
                if (detectedTimeframeSummary.isNotEmpty()) {
                    Text(
                        text = "Detected Timeframes",
                        style = MaterialTheme.typography.titleSmall,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier.padding(top = 8.dp, bottom = 4.dp)
                    )

                    // For each mannequin in the detected timeframes
                    detectedTimeframeSummary.forEach { (mannequin, timeframe) ->
                        Card(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 4.dp),
                            colors = CardDefaults.cardColors(
                                containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
                            )
                        ) {
                            Column(modifier = Modifier.padding(8.dp)) {
                                Text(
                                    text = "Mannequin: $mannequin",
                                    fontWeight = FontWeight.Medium,
                                    style = MaterialTheme.typography.bodyMedium
                                )

                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    // Highlight the start time if it doesn't have actual data yet
                                    val startHasNoData = timeframe.first.contains("no valid data")
                                    Text(
                                        text = "Start: ${timeframe.first}",
                                        style = MaterialTheme.typography.bodySmall,
                                        color = if (startHasNoData)
                                                MaterialTheme.colorScheme.error
                                            else
                                                MaterialTheme.colorScheme.onSurface
                                    )
                                    Text(
                                        text = "End: ${timeframe.second}",
                                        style = MaterialTheme.typography.bodySmall
                                    )
                                }
                            }
                        }
                                }

                    // Show a helpful message about what these timeframes mean
                                Text(
                        text = "Note: These timeframes represent when valid data was detected. Start times may show later than when the mannequin was first connected.",
                                    style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                        modifier = Modifier.padding(top = 4.dp, bottom = 8.dp)
                                )
                }

                Spacer(modifier = Modifier.height(8.dp))

                // Filters section
                Text(
                    text = "Filters",
                    style = MaterialTheme.typography.titleSmall
                )

                Spacer(modifier = Modifier.height(8.dp))

                // Simulation mode selector
                Text(
                    text = "Simulation Mode:",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.padding(bottom = 4.dp)
                )

                // Use Row for simulation mode options
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 12.dp),
                    horizontalArrangement = Arrangement.Start,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Graded option
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier
                            .clickable {
                                viewModel.setSimulationModeFilter("Graded")
                            }
                            .padding(end = 16.dp)
                    ) {
                        RadioButton(
                            selected = selectedFilters.simulationMode == "Graded",
                            onClick = {
                                viewModel.setSimulationModeFilter("Graded")
                            }
                        )

                        Spacer(modifier = Modifier.width(4.dp))

                        Text(
                            text = "Graded",
                            style = MaterialTheme.typography.bodyMedium,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                    }

                    // Training option
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier
                            .clickable {
                                viewModel.setSimulationModeFilter("Training")
                            }
                            .padding(end = 16.dp)
                    ) {
                        RadioButton(
                            selected = selectedFilters.simulationMode == "Training",
                            onClick = {
                                viewModel.setSimulationModeFilter("Training")
                            }
                        )

                        Spacer(modifier = Modifier.width(4.dp))

                        Text(
                            text = "Training",
                            style = MaterialTheme.typography.bodyMedium,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                    }

                    // All option
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier
                            .clickable {
                                viewModel.setSimulationModeFilter(null)
                            }
                    ) {
                        RadioButton(
                            selected = selectedFilters.simulationMode == null || selectedFilters.simulationMode == "<all>",
                            onClick = {
                                viewModel.setSimulationModeFilter(null)
                            }
                        )

                        Spacer(modifier = Modifier.width(4.dp))

                        Text(
                            text = "All Types",
                            style = MaterialTheme.typography.bodyMedium,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                    }
                }

                // Sim Filter
                FilterDropdown(
                    label = "Simulation",
                    options = filterValues?.simValues ?: listOf("<all>"),
                    selectedOption = selectedFilters.sim,
                    onOptionSelected = { option ->
                        viewModel.setSimFilter(if (option == "<all>") null else option)
                    },
                    enabled = true
                )

                // Date Filter
                FilterDropdown(
                    label = "Date",
                    options = filterValues?.dateValues ?: listOf("<all>"),
                    selectedOption = selectedFilters.date,
                    onOptionSelected = { option ->
                        viewModel.setDateFilter(if (option == "<all>") null else option)
                    },
                    enabled = true
                )

                // Mannequin filter
                FilterDropdown(
                    label = "Mannequin",
                    options = getFilteredMannequinOptions(
                        filterValues?.mannequinValues ?: listOf("<all>"),
                        selectedFilters.sim
                    ),
                    selectedOption = selectedFilters.mannequin,
                    onOptionSelected = { option ->
                        viewModel.setMannequinFilter(if (option == "<all>") null else option)
                    },
                    enabled = true
                )

                Spacer(modifier = Modifier.height(8.dp))

                // Time filter
                Row(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    // Start time input
                    OutlinedTextField(
                        value = startTimeStr,
                        onValueChange = { newValue ->
                            startTimeStr = newValue
                            viewModel.updateSelectedFilters(selectedFilters.copy(
                                startTime = if (newValue.isEmpty()) null else newValue
                            ))
                            // Auto-apply filters
                            viewModel.applyFilters()
                        },
                        label = { Text("Start Time") },
                        modifier = Modifier.weight(1f),
                        singleLine = true,
                        placeholder = { Text("YYYY-MM-DD HH:MM") }
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    // End time input
                    OutlinedTextField(
                        value = endTimeStr,
                        onValueChange = { newValue ->
                            endTimeStr = newValue
                            viewModel.updateSelectedFilters(selectedFilters.copy(
                                endTime = if (newValue.isEmpty()) null else newValue
                            ))
                            // Auto-apply filters
                            viewModel.applyFilters()
                        },
                        label = { Text("End Time") },
                        modifier = Modifier.weight(1f),
                        singleLine = true,
                        placeholder = { Text("YYYY-MM-DD HH:MM") }
                    )
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Active filters and buttons
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    // "Active filters" display using ViewModel state
                    val activeFilterText = buildAnnotatedString {
                        append("Active filters:\n")
                        val filters = selectedFilters

                        // Show sim filter
                        if (filters.sim != null) {
                            append("Sim: ")
                            withStyle(style = SpanStyle(color = MaterialTheme.colorScheme.primary)) {
                                append(filters.sim)
                            }
                        }

                        // Show date filter
                        if (filters.date != null) {
                            if (filters.sim != null) append(", ")
                            append("Date: ")
                            withStyle(style = SpanStyle(color = MaterialTheme.colorScheme.primary)) {
                                append(filters.date)
                            }
                        }

                        // Show mannequin filter
                        if (filters.mannequin != null) {
                            if (filters.sim != null || filters.date != null) append(", ")
                            append("Mannequin: ")
                            withStyle(style = SpanStyle(color = MaterialTheme.colorScheme.primary)) {
                                append(filters.mannequin)
                            }
                        }

                        // If no filters are active, show "None"
                        if (filters.sim == null && filters.date == null && filters.mannequin == null) {
                            append("None")
                        }
                    }

                    Text(
                        text = activeFilterText,
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(vertical = 8.dp)
                    )

                    // Reset button in a horizontal position below the active filters
                    Button(
                        onClick = {
                            // Reset local UI state for time
                            startTimeStr = ""
                            endTimeStr = ""
                            // Call ViewModel reset
                            onFilterReset()
                        },
                        modifier = Modifier.align(Alignment.End)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Refresh,
                            contentDescription = "Reset Filters",
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("Reset Filters")
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Add the Vital Range Filters section
                if (availableVitalSigns.isNotEmpty()) {
                    Text(
                        text = "Vital Sign Range Filters",
                        style = MaterialTheme.typography.titleSmall,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )

                    // Show range sliders for each available vital sign
                    VitalRangeFilters(
                        vitalRangeFilters = TempVitalRangeFilters(), // Use our temp class
                        availableVitalSigns = availableVitalSigns,
                        onRangeChange = { vitalName, min, max ->
                            viewModel.updateVitalRangeFilter(vitalName, min, max)
                        },
                        onResetRanges = {
                            viewModel.resetVitalRangeFilters()
                        }
                    )
                }

                // Add Export CSV button at the very bottom of the card
                Spacer(modifier = Modifier.height(24.dp))

                HorizontalDivider(
                    modifier = Modifier.padding(bottom = 16.dp),
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.1f)
                )

                // Export CSV button row
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    OutlinedButton(
                        onClick = {
                            if (filteredVitalSigns.isNotEmpty()) {
                                // Suggest filename based on selected filters
                                val sim = selectedFilters.sim ?: "all"
                                val date = selectedFilters.date ?: "all_dates"
                                val mannequin = selectedFilters.mannequin ?: "all"
                                val filename = "vitals_${sim}_${date}_${mannequin}.csv"

                                exportCsvLauncher.launch(filename)
                            } else {
                                Toast.makeText(context, "No data to export", Toast.LENGTH_SHORT).show()
                            }
                        }
                    ) {
                        Icon(
                            imageVector = Icons.Default.FileDownload,
                            contentDescription = "Export CSV",
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("Export CSV")
                    }
                }
            }
        }

        // Data Preview section
        Spacer(modifier = Modifier.height(16.dp))

        SectionHeader(title = "Data Preview")

        if (filteredVitalSigns.isEmpty()) {
                Text(
                text = "No data matches the current filters.\nTry different filter criteria.",
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(vertical = 16.dp)
            )
        } else {
            // Apply animation when showing data
            AnimatedVisibility(
                visible = visible,
                enter = fadeIn(animationSpec = tween(300)),
                exit = fadeOut(animationSpec = tween(300))
            ) {
                // Use all filtered records instead of preview records
                DataPreviewTable(
                    vitalSigns = filteredVitalSigns,
                    sortConfig = sortConfig,
                    onSortChanged = { field, ascending ->
                        viewModel.setSortConfig(SortConfig(field, ascending))
                    }
                )
            }
        }

        // Statistical summary
        Spacer(modifier = Modifier.height(16.dp))
        SectionHeader(title = "Statistical Summary")
        VitalStatsTable(statsSummary)
    }
}

/**
 * Filter dropdown composable function
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FilterDropdown(
    label: String,
    options: List<String>,
    selectedOption: String?,
    onOptionSelected: (String) -> Unit,
    enabled: Boolean = true
) {
    var expanded by remember { mutableStateOf(false) }

    // Make sure we have <all> in the options
    val allOptions = if ("<all>" !in options) {
        listOf("<all>") + options
    } else {
        options
    }

    ExposedDropdownMenuBox(
        expanded = expanded,
        onExpandedChange = { expanded = it }
    ) {
        OutlinedTextField(
            value = selectedOption ?: "<all>",
            onValueChange = {},
            readOnly = true,
            label = { Text(label) },
            trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded) },
            modifier = Modifier
                .fillMaxWidth()
                .menuAnchor(),
            enabled = enabled
        )

        ExposedDropdownMenu(
            expanded = expanded,
            onDismissRequest = { expanded = false }
        ) {
            // Add all options
            allOptions.forEach { option ->
                DropdownMenuItem(
                    text = { Text(option) },
                    onClick = {
                        onOptionSelected(option)
                        expanded = false
                    }
                )
            }
        }
    }
}

@Composable
private fun VitalStatsTable(statsSummary: Map<String, VitalSignsRepository.StatsSummary>) {
    if (statsSummary.isEmpty()) {
        Text(
            text = "No statistics available",
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
        )
        return
    }

    // Headers
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .background(MaterialTheme.colorScheme.surfaceVariant)
            .padding(8.dp)
    ) {
        Text(
            text = "Vital",
            fontWeight = FontWeight.Bold,
            modifier = Modifier.weight(1f)
        )
        Text(
            text = "Count",
            fontWeight = FontWeight.Bold,
            modifier = Modifier.weight(1f)
        )
        Text(
            text = "Mean",
            fontWeight = FontWeight.Bold,
            modifier = Modifier.weight(1f)
        )
        Text(
            text = "Min-Max",
            fontWeight = FontWeight.Bold,
            modifier = Modifier.weight(1.5f)
        )
    }

    // Rows
    statsSummary.forEach { (vital, stats) ->
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 4.dp, horizontal = 8.dp)
        ) {
            Text(
                text = vital,
                modifier = Modifier.weight(1f)
            )
            Text(
                text = "${stats.count}",
                modifier = Modifier.weight(1f)
            )
            Text(
                text = "%.1f".format(stats.mean),
                modifier = Modifier.weight(1f)
            )
            Text(
                text = "%.1f - %.1f".format(stats.min, stats.max),
                modifier = Modifier.weight(1.5f)
            )
        }

        HorizontalDivider()
    }
}

@Composable
private fun DataPreviewTable(vitalSigns: List<VitalSign>, sortConfig: SortConfig? = null, onSortChanged: (String, Boolean) -> Unit = { _, _ -> }) {
    if (vitalSigns.isEmpty()) {
        Text(
            text = "No data available",
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
        )
        return
    }

    // Get the time format for display
    val timeFormat = remember { java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.US) }

    // State to track horizontal scroll position for header alignment
    val scrollState = rememberScrollState()

    Column(modifier = Modifier.fillMaxWidth()) {
        // Headers with horizontal scrolling
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .background(MaterialTheme.colorScheme.surfaceVariant)
        ) {
            // Fixed Time header with sort icon
            Box(
                modifier = Modifier
                    .width(120.dp)
                    .padding(8.dp)
                    .clickable { onSortChanged("time", sortConfig?.field == "time" && !sortConfig.ascending) },
    ) {
                Row(verticalAlignment = Alignment.CenterVertically) {
        Text(
            text = "Time",
                    fontWeight = FontWeight.Bold
                )
                if (sortConfig?.field == "time") {
                    Icon(
                        imageVector = Icons.Default.Sort,
                        contentDescription = if (sortConfig.ascending) "Sort Descending" else "Sort Ascending",
                        modifier = Modifier.size(16.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )
                    }
                }
            }

            // Scrollable headers
            Row(
                modifier = Modifier
                    .horizontalScroll(scrollState)
                    .padding(vertical = 8.dp)
            ) {
                // HR header with sort icon
                Box(
                    modifier = Modifier
                        .width(80.dp)
                        .clickable { onSortChanged("hr", sortConfig?.field == "hr" && !sortConfig.ascending) }
                ) {
                    Row(verticalAlignment = Alignment.CenterVertically) {
        Text(
            text = "HR",
                    fontWeight = FontWeight.Bold
                )
                if (sortConfig?.field == "hr") {
                    Icon(
                        imageVector = Icons.Default.Sort,
                        contentDescription = if (sortConfig.ascending) "Sort Descending" else "Sort Ascending",
                        modifier = Modifier.size(16.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )
                        }
                }
            }

            // SpO2 header with sort icon
                Box(
                modifier = Modifier
                        .width(80.dp)
                        .clickable { onSortChanged("spO2", sortConfig?.field == "spO2" && !sortConfig.ascending) }
            ) {
                    Row(verticalAlignment = Alignment.CenterVertically) {
        Text(
            text = "SpO2",
                    fontWeight = FontWeight.Bold
                )
                if (sortConfig?.field == "spO2") {
                    Icon(
                        imageVector = Icons.Default.Sort,
                        contentDescription = if (sortConfig.ascending) "Sort Descending" else "Sort Ascending",
                        modifier = Modifier.size(16.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )
                        }
                }
            }

                // NIBP header
                Box(
                    modifier = Modifier.width(100.dp)
                ) {
        Text(
            text = "NIBP",
                        fontWeight = FontWeight.Bold
        )
                }

            // Temp header with sort icon
                Box(
                modifier = Modifier
                        .width(80.dp)
                        .clickable { onSortChanged("temp", sortConfig?.field == "temp" && !sortConfig.ascending) }
            ) {
                    Row(verticalAlignment = Alignment.CenterVertically) {
        Text(
            text = "Temp",
                    fontWeight = FontWeight.Bold
                )
                if (sortConfig?.field == "temp") {
                    Icon(
                        imageVector = Icons.Default.Sort,
                        contentDescription = if (sortConfig.ascending) "Sort Descending" else "Sort Ascending",
                        modifier = Modifier.size(16.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )
                        }
                }
            }

            // Respiratory Rate header with sort icon
                Box(
                modifier = Modifier
                        .width(80.dp)
                        .clickable { onSortChanged("respRate", sortConfig?.field == "respRate" && !sortConfig.ascending) }
            ) {
                    Row(verticalAlignment = Alignment.CenterVertically) {
                Text(
                    text = "Resp",
                    fontWeight = FontWeight.Bold
                )
                if (sortConfig?.field == "respRate") {
                    Icon(
                        imageVector = Icons.Default.Sort,
                        contentDescription = if (sortConfig.ascending) "Sort Descending" else "Sort Ascending",
                        modifier = Modifier.size(16.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )
                        }
                }
            }

            // EtCO2 header with sort icon
                Box(
                modifier = Modifier
                        .width(80.dp)
                        .clickable { onSortChanged("etCO2", sortConfig?.field == "etCO2" && !sortConfig.ascending) }
            ) {
                    Row(verticalAlignment = Alignment.CenterVertically) {
                Text(
                    text = "EtCO2",
                    fontWeight = FontWeight.Bold
                )
                if (sortConfig?.field == "etCO2") {
                    Icon(
                        imageVector = Icons.Default.Sort,
                        contentDescription = if (sortConfig.ascending) "Sort Descending" else "Sort Ascending",
                        modifier = Modifier.size(16.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )
                        }
                }
            }

            // FiCO2 header with sort icon
                Box(
                modifier = Modifier
                        .width(80.dp)
                        .clickable { onSortChanged("fiCO2", sortConfig?.field == "fiCO2" && !sortConfig.ascending) }
            ) {
                    Row(verticalAlignment = Alignment.CenterVertically) {
                Text(
                    text = "FiCO2",
                    fontWeight = FontWeight.Bold
                )
                if (sortConfig?.field == "fiCO2") {
                    Icon(
                        imageVector = Icons.Default.Sort,
                        contentDescription = if (sortConfig.ascending) "Sort Descending" else "Sort Ascending",
                        modifier = Modifier.size(16.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )
                        }
                }
            }

            // IBP header
                Box(modifier = Modifier.width(100.dp)) {
            Text(
                text = "IBP",
                        fontWeight = FontWeight.Bold
            )
                }

                // SpMet header
                Box(modifier = Modifier.width(80.dp)) {
            Text(
                        text = "SpMet",
                        fontWeight = FontWeight.Bold
                    )
                }

                // SpCO header
                Box(modifier = Modifier.width(80.dp)) {
                    Text(
                        text = "SpCO",
                        fontWeight = FontWeight.Bold
                    )
                }

                // PI header
                Box(modifier = Modifier.width(80.dp)) {
                    Text(
                        text = "PI",
                        fontWeight = FontWeight.Bold
                    )
                }

                // PVI header
                Box(modifier = Modifier.width(80.dp)) {
                    Text(
                        text = "PVI",
                        fontWeight = FontWeight.Bold
                    )
                }

                // SpHb header
                Box(modifier = Modifier.width(80.dp)) {
                    Text(
                        text = "SpHb",
                        fontWeight = FontWeight.Bold
                    )
                }

                // SpOC header
                Box(modifier = Modifier.width(80.dp)) {
                    Text(
                        text = "SpOC",
                        fontWeight = FontWeight.Bold
                    )
                }
            }
    }

    // Group records by mannequin
    val groupedRecords = vitalSigns.groupBy { it.overrideMannequin }

        // Scrollable content
        LazyColumn(
            modifier = Modifier
                .fillMaxWidth()
                .height(400.dp) // Fixed height for the scrollable area
        ) {
            // For each mannequin group
    groupedRecords.forEach { (mannequin, mannequinRecords) ->
                // Mannequin header
        if (mannequin != null) {
                    item {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.3f))
                    .padding(vertical = 4.dp, horizontal = 8.dp)
            ) {
                Text(
                    text = "Mannequin: $mannequin${
                        mannequinRecords.firstOrNull()?.sim?.let { sim -> " (Sim: $sim)" } ?: ""
                    }",
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.fillMaxWidth(),
                    color = MaterialTheme.colorScheme.onSecondaryContainer
                )
                        }
            }
        }

        // Group by time blocks if possible
        val timeBlockGroups = identifyTimeBlockGroups(mannequinRecords)

        timeBlockGroups.forEach { (blockInfo, blockRecords) ->
                    // Time block header if available
            if (blockInfo.isNotEmpty()) {
                        item {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(MaterialTheme.colorScheme.tertiaryContainer.copy(alpha = 0.2f))
                        .padding(vertical = 2.dp, horizontal = 8.dp)
                ) {
                    Text(
                        text = "Time Block: $blockInfo",
                        fontStyle = FontStyle.Italic,
                        fontSize = 12.sp,
                        modifier = Modifier.fillMaxWidth(),
                        color = MaterialTheme.colorScheme.onTertiaryContainer
                    )
                }
                        }
            }

                    // Records for this time block
                    itemsIndexed(blockRecords) { index, vital ->
                        val isFirstRecord = index == 0
                        val isLastRecord = index == blockRecords.size - 1

                        // Determine background color for highlighting first/last records
                        val backgroundColor = when {
                            isFirstRecord -> MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.2f)
                            isLastRecord -> MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.2f)
                            else -> Color.Transparent
                        }

                        // Row with fixed time column and scrollable data columns
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                                .background(backgroundColor)
                ) {
                            // Format time for display (fixed column)
                    val timeStr = remember(vital.timeObj) { timeFormat.format(vital.timeObj) }

                            // Apply text decoration for first/last records
                            val textStyle = when {
                                isFirstRecord -> TextStyle(
                                    fontWeight = FontWeight.Bold,
                                    color = MaterialTheme.colorScheme.primary
                                )
                                isLastRecord -> TextStyle(
                                    fontWeight = FontWeight.Bold,
                                    color = MaterialTheme.colorScheme.primary
                                )
                                else -> TextStyle()
                            }

                            // Fixed time column
                            Box(
                                modifier = Modifier
                                    .width(120.dp)
                                    .padding(vertical = 4.dp, horizontal = 8.dp)
                            ) {
                    Text(
                                    text = if (isFirstRecord) "$timeStr (Start)"
                                           else if (isLastRecord) "$timeStr (End)"
                                           else timeStr,
                                style = textStyle
                    )
                            }

                            // Scrollable data columns - linked to the same scroll state as the header
                            Row(
                                modifier = Modifier
                                    .horizontalScroll(scrollState)
                                    .padding(vertical = 4.dp)
                            ) {
                                // Heart Rate
                                Box(modifier = Modifier.width(80.dp)) {
                    Text(
                        text = vital.hr?.toInt()?.toString() ?: "---",
                                style = textStyle
                    )
                                }

                                // SpO2
                                Box(modifier = Modifier.width(80.dp)) {
                    Text(
                        text = vital.spO2?.toInt()?.toString() ?: "---",
                                style = textStyle
                    )
                                }

                                // NIBP
                                Box(modifier = Modifier.width(100.dp)) {
                    // Show BP as SYS/DIA
                    val bpText = if (vital.nibpSys != null && vital.nibpDia != null) {
                        "${vital.nibpSys.toInt()}/${vital.nibpDia.toInt()}"
                    } else {
                        "---/---"
                    }
                    Text(
                        text = bpText,
                                style = textStyle
                    )
                                }

                                // Temperature
                                Box(modifier = Modifier.width(80.dp)) {
                    Text(
                        text = vital.temp1?.let { "%.1f".format(it) } ?: "---",
                                style = textStyle
                    )
                                }

                    // Respiratory Rate
                                Box(modifier = Modifier.width(80.dp)) {
                    Text(
                        text = vital.respRate?.toInt()?.toString() ?: "---",
                        style = textStyle
                    )
                                }

                    // EtCO2
                                Box(modifier = Modifier.width(80.dp)) {
                    Text(
                        text = vital.etCO2?.toInt()?.toString() ?: "---",
                        style = textStyle
                    )
                                }

                    // FiCO2
                                Box(modifier = Modifier.width(80.dp)) {
                    Text(
                        text = vital.fiCO2?.toInt()?.toString() ?: "---",
                        style = textStyle
                    )
                                }

                                // IBP
                                Box(modifier = Modifier.width(100.dp)) {
                    // Show IBP as SYS/DIA
                    val ibpText = if (vital.ibp1Sys != null && vital.ibp1Dia != null) {
                        "${vital.ibp1Sys.toInt()}/${vital.ibp1Dia.toInt()}"
                    } else {
                        "---/---"
                    }
                    Text(
                        text = ibpText,
                        style = textStyle
                    )
                                }

                                // SpMet
                                Box(modifier = Modifier.width(80.dp)) {
                                    Text(
                                        text = vital.spMet?.let { "%.1f".format(it) } ?: "---",
                                        style = textStyle
                                    )
                                }

                                // SpCO
                                Box(modifier = Modifier.width(80.dp)) {
                    Text(
                                        text = vital.spCo?.let { "%.1f".format(it) } ?: "---",
                                        style = textStyle
                                    )
                                }

                                // PI
                                Box(modifier = Modifier.width(80.dp)) {
                                    Text(
                                        text = vital.pi?.let { "%.1f".format(it) } ?: "---",
                                        style = textStyle
                                    )
                                }

                                // PVI
                                Box(modifier = Modifier.width(80.dp)) {
                                    Text(
                                        text = vital.pvi?.let { "%.1f".format(it) } ?: "---",
                                        style = textStyle
                                    )
                                }

                                // SpHb
                                Box(modifier = Modifier.width(80.dp)) {
                                    Text(
                                        text = vital.spHb?.let { "%.1f".format(it) } ?: "---",
                                        style = textStyle
                                    )
                                }

                                // SpOC
                                Box(modifier = Modifier.width(80.dp)) {
                                    Text(
                                        text = vital.spOC?.let { "%.1f".format(it) } ?: "---",
                                        style = textStyle
                                    )
                                }
                            }
                }

                        // Divider after each row
                HorizontalDivider()
            }
        }
    }
}

        // Add scrolling indicator/hint
        Text(
            text = "← Scroll horizontally to see more vital signs →",
            style = MaterialTheme.typography.bodySmall,
            textAlign = TextAlign.Center,
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 8.dp),
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
        )
    }
}

/**
 * Sort configuration for vital signs table
 */
data class SortConfig(
    val field: String,
    val ascending: Boolean
)

/**
 * Helper function to identify time block groups within a list of vital signs
 * @param vitalSigns List of vital signs
 * @return Map of time block info to list of vital signs in that block
 */
private fun identifyTimeBlockGroups(vitalSigns: List<VitalSign>): Map<String, List<VitalSign>> {
    // If no sim, or too few records, just return all records in one group
    if (vitalSigns.isEmpty() || vitalSigns.first().sim == null) {
        return mapOf("" to vitalSigns)
    }

    val sim = vitalSigns.first().sim
    val simSchedules = when (sim) {
        "Sim1" -> listOf(
            "08:30-09:55", "09:55-11:20", "11:20-12:45",
            "13:05-14:30", "14:30-15:55", "15:55-17:20"
        )
        "Sim2" -> listOf(
            "08:00-09:20", "09:20-10:40", "10:40-12:00",
            "12:20-13:40", "13:40-15:00", "15:00-16:20"
        )
        "Sim3" -> listOf(
            "08:00-09:30", "09:30-11:00", "11:00-12:30",
            "12:50-14:20", "14:20-15:50", "15:50-17:20"
        )
        "Sim4" -> listOf(
            "08:00-09:25", "09:25-10:50", "10:50-12:15",
            "12:35-14:00", "14:00-15:25", "15:25-16:50"
        )
        "Sim5" -> listOf(
            "08:00-09:10", "09:10-10:20", "10:20-11:30",
            "11:50-13:00", "13:00-14:10", "14:10-15:20"
        )
        else -> emptyList()
    }

    // If no schedule info available, return all in one group
    if (simSchedules.isEmpty()) {
        return mapOf("" to vitalSigns)
    }

    // Group records by which time block they fall into
    val result = mutableMapOf<String, MutableList<VitalSign>>()

    // Time formatter for comparing hours and minutes
    val timeFormat = SimpleDateFormat("HH:mm", Locale.US)

    vitalSigns.forEach { vitalSign ->
        // Format time as HH:mm for comparison
        val timeStr = timeFormat.format(vitalSign.timeObj)
        val hour = timeStr.split(":")[0].toInt()
        val minute = timeStr.split(":")[1].toInt()
        val timeMinutes = hour * 60 + minute

        // Find which block this time falls into
        var foundBlock = false
        for (blockStr in simSchedules) {
            val (startStr, endStr) = blockStr.split("-")

            val startHour = startStr.split(":")[0].toInt()
            val startMinute = startStr.split(":")[1].toInt()
            val startMinutes = startHour * 60 + startMinute

            val endHour = endStr.split(":")[0].toInt()
            val endMinute = endStr.split(":")[1].toInt()
            val endMinutes = endHour * 60 + endMinute

            if (timeMinutes in startMinutes..endMinutes) {
                result.getOrPut(blockStr) { mutableListOf() }.add(vitalSign)
                foundBlock = true
                break
            }
        }

        // If no block found, add to "Other" group
        if (!foundBlock) {
            result.getOrPut("Other") { mutableListOf() }.add(vitalSign)
        }
    }

    // Convert mutable lists to immutable lists
    return result.mapValues { it.value.toList() }
}

@Composable
private fun EmptyFilteredDataMessage() {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(100.dp)
            .padding(16.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = "No data matches the current filters.\nTry different filter criteria.",
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
        )
    }
}

// Add this helper function to get relevant mannequins based on selected sim
private fun getFilteredMannequinOptions(
    allMannequins: List<String>,
    selectedSim: String?
): List<String> {
    // If no simulation selected or "all" selected, show all mannequins
    if (selectedSim == null || selectedSim == "<all>") {
        // Return all mannequins in a predefined order
        val mannequinOrder = listOf("Dave", "Chuck", "Freddy", "Oscar", "Matt")
        // Sort the list to put known mannequins in the desired order, followed by any others
        return listOf("<all>") + mannequinOrder.filter { it in allMannequins } + allMannequins.filter { it !in mannequinOrder }
    }

    // Get mannequins for the selected simulation
    val mannequinsForSim = when (selectedSim) {
        "Sim1" -> listOf("Dave", "Chuck")
        "Sim2" -> listOf("Freddy", "Oscar")
        "Sim3" -> listOf("Dave", "Chuck")
        "Sim4" -> listOf("Dave", "Chuck")
        "Sim5" -> listOf("Freddy", "Oscar", "Matt")
        else -> allMannequins
    }

    // Return filtered list with "all" option first
    return listOf("<all>") + mannequinsForSim.filter { it in allMannequins }
}

/**
 * Composable to display range sliders for vital sign filtering
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun VitalRangeFilters(
    vitalRangeFilters: Any, // Just use Any for now
    availableVitalSigns: Set<String>,
    onRangeChange: (String, Float, Float) -> Unit,
    onResetRanges: () -> Unit
) {
    // Use a temporary object for now
    val tempFilters = TempVitalRangeFilters()

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
    ) {
        // Heart Rate
        if ("heartRate" in availableVitalSigns) {
            VitalRangeSlider(
                title = "Heart Rate (bpm)",
                minValue = tempFilters.heartRate.min,
                maxValue = tempFilters.heartRate.max,
                valueRange = tempFilters.heartRate.defaultMin..tempFilters.heartRate.defaultMax,
                onValueChange = { min, max ->
                    onRangeChange("heartRate", min, max)
                }
            )
        }

        // SpO2
        if ("spo2" in availableVitalSigns) {
            VitalRangeSlider(
                title = "SpO₂ (%)",
                minValue = tempFilters.spo2.min,
                maxValue = tempFilters.spo2.max,
                valueRange = tempFilters.spo2.defaultMin..tempFilters.spo2.defaultMax,
                onValueChange = { min, max ->
                    onRangeChange("spo2", min, max)
                }
            )
        }

        // NIBP Systolic
        if ("nibpSys" in availableVitalSigns) {
            VitalRangeSlider(
                title = "NIBP Systolic (mmHg)",
                minValue = tempFilters.nibpSys.min,
                maxValue = tempFilters.nibpSys.max,
                valueRange = tempFilters.nibpSys.defaultMin..tempFilters.nibpSys.defaultMax,
                onValueChange = { min, max ->
                    onRangeChange("nibpSys", min, max)
                }
            )
        }

        // NIBP Diastolic
        if ("nibpDia" in availableVitalSigns) {
            VitalRangeSlider(
                title = "NIBP Diastolic (mmHg)",
                minValue = tempFilters.nibpDia.min,
                maxValue = tempFilters.nibpDia.max,
                valueRange = tempFilters.nibpDia.defaultMin..tempFilters.nibpDia.defaultMax,
                onValueChange = { min, max ->
                    onRangeChange("nibpDia", min, max)
                }
            )
        }

        // NIBP MAP
        if ("nibpMap" in availableVitalSigns) {
            VitalRangeSlider(
                title = "Mean Arterial Pressure (mmHg)",
                minValue = tempFilters.nibpMap.min,
                maxValue = tempFilters.nibpMap.max,
                valueRange = tempFilters.nibpMap.defaultMin..tempFilters.nibpMap.defaultMax,
                onValueChange = { min, max ->
                    onRangeChange("nibpMap", min, max)
                }
            )
        }

        // Temperature
        if ("temp" in availableVitalSigns) {
            VitalRangeSlider(
                title = "Temperature (°C)",
                minValue = tempFilters.temp.min,
                maxValue = tempFilters.temp.max,
                valueRange = tempFilters.temp.defaultMin..tempFilters.temp.defaultMax,
                onValueChange = { min, max ->
                    onRangeChange("temp", min, max)
                }
            )
        }

        // Respiratory Rate
        if ("respRate" in availableVitalSigns) {
            VitalRangeSlider(
                title = "Respiratory Rate (breaths/min)",
                minValue = tempFilters.respRate.min,
                maxValue = tempFilters.respRate.max,
                valueRange = tempFilters.respRate.defaultMin..tempFilters.respRate.defaultMax,
                onValueChange = { min, max ->
                    onRangeChange("respRate", min, max)
                }
            )
        }

        // ETCO2
        if ("etco2" in availableVitalSigns) {
            VitalRangeSlider(
                title = "End-Tidal CO₂ (mmHg)",
                minValue = tempFilters.etco2.min,
                maxValue = tempFilters.etco2.max,
                valueRange = tempFilters.etco2.defaultMin..tempFilters.etco2.defaultMax,
                onValueChange = { min, max ->
                    onRangeChange("etCO2", min, max)
                }
            )
        }

        // FiCO2
        if ("fiCO2" in availableVitalSigns) {
            VitalRangeSlider(
                title = "Fraction of Inspired CO₂ (mmHg)",
                minValue = tempFilters.fiCO2.min,
                maxValue = tempFilters.fiCO2.max,
                valueRange = tempFilters.fiCO2.defaultMin..tempFilters.fiCO2.defaultMax,
                onValueChange = { min, max ->
                    onRangeChange("fiCO2", min, max)
                }
            )
        }

        // Reset button
        if (availableVitalSigns.isNotEmpty()) {
            Spacer(modifier = Modifier.height(8.dp))

            OutlinedButton(
                onClick = onResetRanges,
                modifier = Modifier.align(Alignment.End)
            ) {
                Icon(
                    imageVector = Icons.Default.Refresh,
                    contentDescription = "Reset Vital Ranges",
                    modifier = Modifier.size(18.dp)
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text("Reset Vital Ranges")
            }
        }
    }
}

/**
 * Composable for a single vital sign range slider
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun VitalRangeSlider(
    title: String,
    minValue: Float,
    maxValue: Float,
    valueRange: ClosedFloatingPointRange<Float>,
    onValueChange: (Float, Float) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
    ) {
        // Title with current range values
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyMedium
            )

            Text(
                text = "${minValue.toInt()} - ${maxValue.toInt()}",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.primary
            )
        }

        // Range slider
        RangeSlider(
            value = minValue..maxValue,
            onValueChange = { range ->
                onValueChange(range.start, range.endInclusive)
            },
            valueRange = valueRange,
            steps = 0,
            modifier = Modifier.fillMaxWidth()
        )
    }
}