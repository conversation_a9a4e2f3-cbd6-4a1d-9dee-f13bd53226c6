package com.example.myapplication.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.myapplication.data.model.VitalSignStatus

/**
 * Card component to display a vital sign with its status indicator
 *
 * @param label The name/label of the vital sign
 * @param value The value of the vital sign
 * @param unit The unit of measurement
 * @param status The current status of the vital sign
 */
@Composable
fun VitalSignCard(
    label: String,
    value: Double?,
    unit: String,
    status: VitalSignStatus = VitalSignStatus.UNKNOWN
) {
    // Use theme colors for status indicators for consistency
    val backgroundColor = when (status) {
        VitalSignStatus.NORMAL -> MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.7f)
        VitalSignStatus.WARNING_HIGH, VitalSignStatus.WARNING_LOW ->
            MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.7f)
        VitalSignStatus.CRITICAL_HIGH, VitalSignStatus.CRITICAL_LOW ->
            MaterialTheme.colorScheme.errorContainer.copy(alpha = 0.7f)
        VitalSignStatus.UNKNOWN -> MaterialTheme.colorScheme.surface
    }

    // Use theme colors for text based on status
    val valueTextColor = when (status) {
        VitalSignStatus.NORMAL -> MaterialTheme.colorScheme.onPrimaryContainer
        VitalSignStatus.WARNING_HIGH, VitalSignStatus.WARNING_LOW ->
            MaterialTheme.colorScheme.error.copy(alpha = 0.8f)
        VitalSignStatus.CRITICAL_HIGH, VitalSignStatus.CRITICAL_LOW ->
            MaterialTheme.colorScheme.error
        VitalSignStatus.UNKNOWN -> MaterialTheme.colorScheme.onSurfaceVariant
    }

    // Fixed width and height for consistent card sizing
    val cardWidth = 90.dp
    val cardHeight = 120.dp

    Card(
        modifier = Modifier
            .padding(4.dp)
            .width(cardWidth)
            .height(cardHeight),
        colors = CardDefaults.cardColors(
            containerColor = backgroundColor
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 2.dp
        ),
        shape = RoundedCornerShape(10.dp) // Slightly more rounded for a modern medical look
    ) {
        Column(
            modifier = Modifier
                .padding(8.dp)
                .fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.SpaceBetween
        ) {
            // Label with consistent styling - ensure it doesn't wrap awkwardly
            Text(
                text = label,
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onSurface,
                maxLines = 1,
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )

            // Value with improved styling and theme-based colors
            Text(
                text = value?.let { String.format("%.1f", it) } ?: "--",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold,
                color = valueTextColor, // Using the theme-based color defined above
                textAlign = TextAlign.Center // Ensure proper centering
            )

            // Unit with improved styling
            Text(
                text = unit,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                fontWeight = FontWeight.Medium, // Slightly bolder for better readability
                textAlign = TextAlign.Center
            )

            // Status indicator with improved styling
            if (status != VitalSignStatus.NORMAL && status != VitalSignStatus.UNKNOWN) {
                Box(
                    modifier = Modifier
                        .background(
                            color = when (status) {
                                VitalSignStatus.WARNING_HIGH, VitalSignStatus.WARNING_LOW ->
                                    MaterialTheme.colorScheme.error.copy(alpha = 0.7f)
                                VitalSignStatus.CRITICAL_HIGH, VitalSignStatus.CRITICAL_LOW ->
                                    MaterialTheme.colorScheme.error
                                else -> MaterialTheme.colorScheme.primary // Fallback
                            },
                            shape = RoundedCornerShape(12.dp) // More rounded for a modern medical look
                        )
                        .padding(horizontal = 8.dp, vertical = 3.dp) // Slightly more padding for better touch target
                ) {
                    Text(
                        text = status.getLabel(),
                        style = MaterialTheme.typography.labelSmall,
                        color = Color.White,
                        fontWeight = FontWeight.Bold,
                        textAlign = TextAlign.Center // Ensure proper centering
                    )
                }
            }
        }
    }
}

/**
 * Row of vital sign cards with horizontal scrolling
 */
@Composable
fun VitalSignsRow(
    vitalSigns: List<VitalSignCardData>,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier.fillMaxWidth()) {
        // Use LazyRow for horizontal scrolling
        LazyRow(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp), // Add spacing between items
            verticalAlignment = Alignment.CenterVertically,
            contentPadding = PaddingValues(horizontal = 8.dp) // Add padding at the edges
        ) {
            items(vitalSigns) { vitalSign ->
                VitalSignCard(
                    label = vitalSign.label,
                    value = vitalSign.value,
                    unit = vitalSign.unit,
                    status = vitalSign.status
                )
            }
        }

        // Add scroll indicator if there are more than 3 items
        if (vitalSigns.size > 3) {
            Spacer(modifier = Modifier.height(4.dp))
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.Center
            ) {
                Text(
                    text = "Swipe to see more →",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f),
                    fontSize = 10.sp
                )
            }
        }
    }
}

/**
 * Data class for vital sign card
 */
data class VitalSignCardData(
    val label: String,
    val value: Double?,
    val unit: String,
    val status: VitalSignStatus = VitalSignStatus.UNKNOWN
)