package com.example.myapplication.ui.theme

import androidx.compose.ui.graphics.Color

/**
 * Professional medical-grade color palette for the vital signs app
 * Using a more refined and consistent color scheme with softer blues and greens
 * that convey a sense of trust, professionalism, and clinical precision.
 */

// Primary colors - Softer medical blue tones
val Primary = Color(0xFF0277BD) // Deeper medical blue
val PrimaryLight = Color(0xFF58A5F0) // Lighter medical blue
val PrimaryDark = Color(0xFF004C8C) // Dark medical blue
val PrimaryContainer = Color(0xFFD6E8FF) // Very light blue container
val OnPrimaryContainer = Color(0xFF001E36) // Dark text for containers

// Secondary colors - Complementary teal tones
val Secondary = Color(0xFF00838F) // Medical teal
val SecondaryLight = Color(0xFF4FB3BF) // Lighter teal
val SecondaryDark = Color(0xFF005662) // Darker teal
val SecondaryContainer = Color(0xFFCCF4FF) // Very light teal container

// Accent color - For highlights and emphasis
val Accent = Color(0xFF03A9F4) // Bright blue accent

// Semantic status colors - For vital signs and alerts
val Success = Color(0xFF2E7D32) // Darker green for better contrast
val Warning = Color(0xFFEF6C00) // Orange warning
val Danger = Color(0xFFD32F2F) // Red alert/danger
val Info = Color(0xFF1976D2) // Information blue

// Neutral colors - For backgrounds, text, and surfaces
val NeutralDark = Color(0xFF263238) // Almost black for text
val Neutral = Color(0xFF607D8B) // Medium gray with blue tint
val NeutralLight = Color(0xFFECEFF1) // Very light gray with blue tint
val Background = Color(0xFFF5F7F9) // Off-white background
val BackgroundVariant = Color(0xFFE1E5E9) // Slightly darker background variant
val Surface = Color(0xFFFFFFFF) // Pure white for cards and surfaces
val SurfaceVariant = Color(0xFFF8F9FA) // Very subtle off-white for variant surfaces

// Colors for vital sign displays - More refined medical colors
val HeartRateColor = Color(0xFFD32F2F) // Deeper red for heart rate
val SpO2Color = Color(0xFF1E88E5) // Oxygen blue
val BloodPressureColor = Color(0xFF0D47A1) // Deep blue for blood pressure
val TemperatureColor = Color(0xFFE65100) // Orange for temperature
val RespiratoryRateColor = Color(0xFF2E7D32) // Green for respiratory
val EtCO2Color = Color(0xFF424242) // Dark gray for EtCO2

// Status colors - For indicators and compliance
val ExcellentColor = Color(0xFF2E7D32) // Dark green
val GoodColor = Color(0xFF689F38) // Medium green
val FairColor = Color(0xFFEF6C00) // Orange
val PoorColor = Color(0xFFD32F2F) // Red
val UnknownColor = Color(0xFF757575) // Medium gray

// Elevation colors - For shadows and depth
val ShadowLight = Color(0x1A000000) // 10% black shadow
val ShadowMedium = Color(0x33000000) // 20% black shadow

// Legacy colors for backward compatibility
val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)
val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)

// Green shades - More medical-focused
val Green50 = Color(0xFFE8F5E9)
val Green100 = Color(0xFFC8E6C9)
val Green500 = Color(0xFF2E7D32) // Adjusted to match Success
val Green700 = Color(0xFF1B5E20)
val Green900 = Color(0xFF0A3D12)

// Yellow/Orange shades - More medical-focused
val Yellow500 = Color(0xFFFBC02D)
val Yellow700 = Color(0xFFF57F17)
val Orange500 = Color(0xFFEF6C00) // Adjusted to match Warning
val Orange700 = Color(0xFFE65100)

// Red shades - More medical-focused
val Red500 = Color(0xFFD32F2F) // Adjusted to match Danger
val Red700 = Color(0xFFC62828)
val Red900 = Color(0xFFB71C1C)