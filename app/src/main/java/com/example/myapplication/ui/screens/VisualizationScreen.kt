package com.example.myapplication.ui.screens

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.net.Uri
import android.widget.Toast
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ShowChart
import androidx.compose.material.icons.filled.ArrowDropDown
import androidx.compose.material.icons.filled.BarChart
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material.icons.filled.LineAxis
import androidx.compose.material.icons.filled.PieChart
import androidx.compose.material.icons.filled.Save
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Checkbox
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FilterChip
import androidx.compose.material3.FilterChipDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.RadioButton
import androidx.compose.material3.Slider
import androidx.compose.material3.Surface
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.view.drawToBitmap
import com.example.myapplication.data.model.VitalSign
import com.example.myapplication.ui.components.SectionHeader
import com.example.myapplication.ui.components.VitalSignsBarChart
import com.example.myapplication.ui.components.VitalSignsLineChart
import com.example.myapplication.ui.components.VitalSignsScatterChart
import com.example.myapplication.ui.theme.Primary
import com.example.myapplication.ui.viewmodel.VitalSignsViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * Screen for visualizing vital signs data
 */
@Composable
fun VisualizationScreen(viewModel: VitalSignsViewModel) {
    // State
    val vitalSigns by viewModel.vitalSigns.collectAsState()
    val filteredVitalSigns by viewModel.filteredVitalSigns.collectAsState()
    val selectedFilters by viewModel.selectedFilters.collectAsState()
    val vitalRangeFilters by viewModel.vitalRangeFilters.collectAsState()
    val availableVitalSigns by viewModel.availableVitalSigns.collectAsState()
    val filterValues by viewModel.filterValues.collectAsState()
    
    // Local UI state
    var selectedTabIndex by remember { mutableIntStateOf(0) }
    val tabs = listOf("Line Chart", "Bar Chart", "Scatter Plot")
    
    // State for selected vital signs to visualize
    val availableVitalSignOptions = listOf("Heart Rate", "SpO2", "Blood Pressure", "Temperature")
    val selectedVitalSigns = remember { mutableStateListOf("Heart Rate") }
    
    // Time range selection - these values will be updated from the filtered data
    var startTimeIndex by remember { mutableFloatStateOf(0f) }
    var endTimeIndex by remember { mutableFloatStateOf(filteredVitalSigns.size.toFloat()) }
    
    // Chart customization options
    var showLegend by remember { mutableStateOf(true) }
    var showGridLines by remember { mutableStateOf(true) }
    var showDataLabels by remember { mutableStateOf(false) }
    var showTrendLine by remember { mutableStateOf(true) }
    var showThresholdHighlights by remember { mutableStateOf(true) }
    
    // Get scenario from filtered vital signs if available
    val currentScenario = remember(filteredVitalSigns) {
        if (filteredVitalSigns.isNotEmpty()) {
            filteredVitalSigns.firstOrNull()?.scenario ?: "TBI"
        } else {
            "TBI"
        }
    }
    
    // Launch effect to ensure filters are applied when the screen is first shown
    LaunchedEffect(Unit) {
        // Make sure filter values are loaded
        if (filterValues == null && vitalSigns.isNotEmpty()) {
            viewModel.updateFilterValues()
        }
        
        // Ensure that the filter state is applied
        if (filteredVitalSigns.isEmpty() && vitalSigns.isNotEmpty()) {
            viewModel.applyFilters()
        }
        
        // Update the time range indices based on filtered data
        if (filteredVitalSigns.isNotEmpty()) {
            startTimeIndex = 0f
            endTimeIndex = filteredVitalSigns.size.toFloat()
        }
    }
    
    // Listen for changes in the filtered data to update the time range sliders
    LaunchedEffect(filteredVitalSigns.size) {
        if (filteredVitalSigns.isNotEmpty()) {
            // Keep the proportions the same but adjust to the new data size
            val oldSize = endTimeIndex - startTimeIndex
            val newSize = filteredVitalSigns.size.toFloat()
            
            if (oldSize > 0 && newSize > 0) {
                val startProportion = startTimeIndex / oldSize
                val endProportion = endTimeIndex / oldSize
                
                startTimeIndex = (startProportion * newSize).coerceIn(0f, newSize - 1)
                endTimeIndex = (endProportion * newSize).coerceIn(startTimeIndex + 1, newSize)
            } else {
                startTimeIndex = 0f
                endTimeIndex = newSize
            }
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState())
    ) {
        // Header with export button
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            SectionHeader(
                title = "Visualization",
                modifier = Modifier.weight(1f)
            )
            
            if (filteredVitalSigns.isNotEmpty()) {
                // Export info button
                Button(
                    onClick = { /* This will be handled by the chart's export button */ },
                    modifier = Modifier.padding(start = 8.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Save,
                        contentDescription = "Export Visualization",
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("Export for Debrief")
                }
            }
        }
        
        Text(
            text = "Visualize vital signs data with interactive charts",
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        if (vitalSigns.isEmpty()) {
            // No data loaded
            EmptyDataMessage()
        } else {
            // Data loaded, show visualization options and chart
            
            // Data filter section
            DataFilterSection(
                viewModel = viewModel,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Visualization controls
            VisualizationControls(
                viewModel = viewModel,
                vitalSigns = filteredVitalSigns,
                selectedVitalSigns = selectedVitalSigns,
                availableVitalSigns = availableVitalSignOptions,
                onVitalSignSelected = { vitalSign, isSelected ->
                    if (isSelected) {
                        if (!selectedVitalSigns.contains(vitalSign)) {
                            selectedVitalSigns.add(vitalSign)
                        }
                    } else {
                        selectedVitalSigns.remove(vitalSign)
                        // Ensure at least one vital sign is selected
                        if (selectedVitalSigns.isEmpty()) {
                            selectedVitalSigns.add("Heart Rate")
                        }
                    }
                },
                startTimeIndex = startTimeIndex,
                endTimeIndex = endTimeIndex,
                onTimeRangeChanged = { start, end ->
                    startTimeIndex = start
                    endTimeIndex = end
                },
                showLegend = showLegend,
                showGridLines = showGridLines,
                showDataLabels = showDataLabels,
                showTrendLine = showTrendLine,
                showThresholdHighlights = showThresholdHighlights,
                onShowLegendChanged = { showLegend = it },
                onShowGridLinesChanged = { showGridLines = it },
                onShowDataLabelsChanged = { showDataLabels = it },
                onShowTrendLineChanged = { showTrendLine = it },
                onShowThresholdHighlightsChanged = { showThresholdHighlights = it }
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Chart type selection tabs
            TabRow(selectedTabIndex = selectedTabIndex) {
                tabs.forEachIndexed { index, title ->
                    Tab(
                        selected = selectedTabIndex == index,
                        onClick = { selectedTabIndex = index },
                        text = { Text(title) },
                        icon = {
                            when (index) {
                                0 -> Icon(Icons.AutoMirrored.Filled.ShowChart, contentDescription = null)
                                1 -> Icon(Icons.Filled.BarChart, contentDescription = null)
                                2 -> Icon(Icons.Filled.LineAxis, contentDescription = null)
                            }
                        }
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Display the selected chart
            ChartDisplay(
                vitalSigns = filteredVitalSigns,
                selectedVitalSigns = selectedVitalSigns,
                chartType = selectedTabIndex,
                startTimeIndex = startTimeIndex.toInt(),
                endTimeIndex = endTimeIndex.toInt(),
                showLegend = showLegend,
                showGridLines = showGridLines,
                showDataLabels = showDataLabels,
                showTrendLine = showTrendLine,
                showThresholdHighlights = showThresholdHighlights,
                scenario = currentScenario
            )
        }
    }
}

@Composable
fun EmptyDataMessage() {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
            .height(200.dp),
                contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "No vital signs data loaded.",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "Please load data in the Data Explorer tab or start a monitoring session in the Real-Time Monitor tab.",
                textAlign = TextAlign.Center,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                modifier = Modifier.padding(horizontal = 32.dp)
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun VisualizationControls(
    viewModel: VitalSignsViewModel,
    vitalSigns: List<VitalSign>,
    selectedVitalSigns: List<String>,
    availableVitalSigns: List<String>,
    onVitalSignSelected: (String, Boolean) -> Unit,
    startTimeIndex: Float,
    endTimeIndex: Float,
    onTimeRangeChanged: (Float, Float) -> Unit,
    showLegend: Boolean,
    showGridLines: Boolean,
    showDataLabels: Boolean,
    showTrendLine: Boolean,
    showThresholdHighlights: Boolean,
    onShowLegendChanged: (Boolean) -> Unit,
    onShowGridLinesChanged: (Boolean) -> Unit,
    onShowDataLabelsChanged: (Boolean) -> Unit,
    onShowTrendLineChanged: (Boolean) -> Unit,
    onShowThresholdHighlightsChanged: (Boolean) -> Unit
) {
    var showChartOptions by remember { mutableStateOf(false) }
    
    // Define the time change handler here to call the view model
    val handleTimeChange = { startIdx: Float, endIdx: Float ->
        // Call the original callback to update local slider indices
        onTimeRangeChanged(startIdx, endIdx) 

        // Get the corresponding Date objects from the vitalSigns list
        val startVitalSign = vitalSigns.getOrNull(startIdx.toInt().coerceIn(0, vitalSigns.size - 1))
        val endVitalSign = vitalSigns.getOrNull(endIdx.toInt().coerceIn(0, vitalSigns.size - 1))

        // Format the dates into strings (using existing formatTime or similar)
        // Handle null cases if indices are out of bounds or timeObj is null
        val startTimeString = startVitalSign?.timeObj?.let { formatTime(it) }
        val endTimeString = endVitalSign?.timeObj?.let { formatTime(it) }

        // Call the ViewModel function to update filters and trigger re-filtering
        viewModel.setTimeRangeFilter(startTimeString, endTimeString)
    }

    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // Data source indicator
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Data Source:",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "Data Explorer",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Primary
                )
                
                Spacer(modifier = Modifier.weight(1f))
                
                // Information about Data Explorer filters
                Text(
                    text = "Filters applied from Data Explorer",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Vital Signs Selection
            Text(
                text = "Select Vital Signs to Visualize:",
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                availableVitalSigns.forEach { vitalSign ->
                    FilterChip(
                        selected = selectedVitalSigns.contains(vitalSign),
                        onClick = { 
                            onVitalSignSelected(vitalSign, !selectedVitalSigns.contains(vitalSign))
                        },
                        label = { Text(vitalSign) },
                        leadingIcon = {
                            if (selectedVitalSigns.contains(vitalSign)) {
                                Icon(
                                    imageVector = Icons.AutoMirrored.Filled.ShowChart,
                                    contentDescription = null,
                                    modifier = Modifier.size(FilterChipDefaults.IconSize)
                                )
                            }
                        }
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Time Range Selection
            if (vitalSigns.isNotEmpty()) {
                Text(
                    text = "Adjust Time Window:",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // Convert indices to actual times for display
                val startTimeText = if (vitalSigns.isNotEmpty() && startTimeIndex.toInt() < vitalSigns.size) {
                    formatTime(vitalSigns[startTimeIndex.toInt().coerceIn(0, vitalSigns.size - 1)].timeObj)
                } else {
                    "Start"
                }
                
                val endTimeText = if (vitalSigns.isNotEmpty() && endTimeIndex.toInt() < vitalSigns.size) {
                    formatTime(vitalSigns[endTimeIndex.toInt().coerceIn(0, vitalSigns.size - 1)].timeObj)
        } else {
                    "End"
                }
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = startTimeText,
                        style = MaterialTheme.typography.bodySmall
                    )
                    
                    Slider(
                        value = startTimeIndex,
                        onValueChange = { newValue ->
                            // Ensure start time is before end time
                            if (newValue < endTimeIndex) {
                                handleTimeChange(newValue, endTimeIndex) // Use the new handler
                            }
                        },
                        valueRange = 0f..vitalSigns.size.toFloat(),
                        modifier = Modifier.weight(1f)
                    )
                }
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = endTimeText,
                        style = MaterialTheme.typography.bodySmall
                    )
                    
                    Slider(
                        value = endTimeIndex,
                        onValueChange = { newValue ->
                            // Ensure end time is after start time
                            if (newValue > startTimeIndex) {
                                handleTimeChange(startTimeIndex, newValue) // Use the new handler
                            }
                        },
                        valueRange = 0f..vitalSigns.size.toFloat(),
                        modifier = Modifier.weight(1f)
                    )
                }
                
                // Small explanation of time window
                Text(
                    text = "Time window adjustment preserves other Data Explorer filters",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.padding(top = 4.dp)
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // Chart Options button
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.End
            ) {
                Button(
                    onClick = { showChartOptions = !showChartOptions }
                ) {
                    Icon(
                        imageVector = Icons.Filled.Settings,
                        contentDescription = "Chart Options"
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("Chart Options")
                }
            }
            
            // Chart Options collapsible section
            if (showChartOptions) {
                Spacer(modifier = Modifier.height(8.dp))
                HorizontalDivider()
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "Chart Options",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium
                )
                
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Checkbox(
                        checked = showLegend,
                        onCheckedChange = onShowLegendChanged
                    )
                    Text("Show Legend")
                    
                    Spacer(modifier = Modifier.width(16.dp))
                    
                    Checkbox(
                        checked = showGridLines,
                        onCheckedChange = onShowGridLinesChanged
                    )
                    Text("Show Grid Lines")
                }
                
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Checkbox(
                        checked = showDataLabels,
                        onCheckedChange = onShowDataLabelsChanged
                    )
                    Text("Show Data Labels")
                    
                    Spacer(modifier = Modifier.width(16.dp))
                    
                    Checkbox(
                        checked = showTrendLine,
                        onCheckedChange = onShowTrendLineChanged
                    )
                    Text("Show Trend Line")
                }
                
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Checkbox(
                        checked = showThresholdHighlights,
                        onCheckedChange = onShowThresholdHighlightsChanged
                    )
                    Text("Show Threshold Highlights")
                }
            }
        }
    }
}

@Composable
fun ChartDisplay(
    vitalSigns: List<VitalSign>,
    selectedVitalSigns: List<String>,
    chartType: Int,
    startTimeIndex: Int,
    endTimeIndex: Int,
    showLegend: Boolean,
    showGridLines: Boolean = false,
    showDataLabels: Boolean = false,
    showTrendLine: Boolean = false,
    showThresholdHighlights: Boolean = false,
    scenario: String
) {
    if (vitalSigns.isEmpty() || selectedVitalSigns.isEmpty()) {
        return
    }
    
    // Context for file operations
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    
    // Reference to the chart container view
    val chartContainerView = LocalView.current
    
    // State for export dialog
    var showExportDialog by remember { mutableStateOf(false) }
    var exportFileName by remember { mutableStateOf("vital_signs_chart") }
    var exportFormat by remember { mutableStateOf("PNG") }
    val exportFormats = listOf("PNG", "JPG", "PDF")
    var isExporting by remember { mutableStateOf(false) }
    
    // File export launcher
    val exportLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.CreateDocument("image/png")
    ) { uri ->
        uri?.let {
            // Show processing indicator
            isExporting = true
            
            // Export in background
            coroutineScope.launch(Dispatchers.IO) {
                try {
                    // Capture the chart view as a bitmap
                    val bitmap = withContext(Dispatchers.Main) {
                        chartContainerView.drawToBitmap()
                    }
                    
                    // Write to the URI
                    context.contentResolver.openOutputStream(uri)?.use { outputStream ->
                        when (exportFormat) {
                            "PNG" -> {
                                bitmap.compress(Bitmap.CompressFormat.PNG, 100, outputStream)
                            }
                            "JPG" -> {
                                bitmap.compress(Bitmap.CompressFormat.JPEG, 90, outputStream)
                            }
                            "PDF" -> {
                                // In a real implementation, you'd use PdfDocument APIs
                                // For now, we'll just use PNG as a fallback
                                bitmap.compress(Bitmap.CompressFormat.PNG, 100, outputStream)
                            }
                            else -> {
                                bitmap.compress(Bitmap.CompressFormat.PNG, 100, outputStream)
                            }
                        }
                    }
                    
                    withContext(Dispatchers.Main) {
                        Toast.makeText(context, "Chart exported successfully", Toast.LENGTH_SHORT).show()
                    }
                } catch (e: Exception) {
                    withContext(Dispatchers.Main) {
                        Toast.makeText(context, "Export failed: ${e.message}", Toast.LENGTH_LONG).show()
                    }
                } finally {
                    // Hide processing indicator
                    isExporting = false
                }
            }
        }
    }
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(400.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Chart title with export button
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
        ) {
            val chartTypeText = when (chartType) {
                0 -> "Line Chart"
                1 -> "Bar Chart"
                2 -> "Scatter Plot"
                else -> "Chart"
            }
            
            Text(
                text = chartTypeText,
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )
                
                // Export button
                IconButton(
                    onClick = { showExportDialog = true }
                ) {
                    Icon(
                        imageVector = Icons.Default.Save,
                        contentDescription = "Export Chart",
                        tint = MaterialTheme.colorScheme.primary
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Chart area
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .clip(RoundedCornerShape(8.dp))
                    .border(
                        width = 1.dp,
                        color = MaterialTheme.colorScheme.outline,
                        shape = RoundedCornerShape(8.dp)
                    ),
                contentAlignment = Alignment.Center
            ) {
                when {
                    vitalSigns.isEmpty() -> {
                        Text("No data to display")
                    }
                    else -> {
                        // The actual index values need to be clamped to avoid out of bounds errors
                        val clampedStartIndex = startTimeIndex.coerceIn(0, vitalSigns.size - 1)
                        val clampedEndIndex = endTimeIndex.coerceIn(clampedStartIndex + 1, vitalSigns.size)
                        
                        // These indices represent the actual slicing of the data
                        val effectiveStartIndex = if (clampedStartIndex < vitalSigns.size) clampedStartIndex else 0
                        val effectiveEndIndex = if (clampedEndIndex < vitalSigns.size) clampedEndIndex else vitalSigns.size
                        
                        // Show the appropriate chart based on selection
                        when (chartType) {
                            0 -> {
                                // Line Chart
                                VitalSignsLineChart(
                                    vitalSigns = vitalSigns,
                                    selectedVitalSigns = selectedVitalSigns,
                                    startTimeIndex = effectiveStartIndex,
                                    endTimeIndex = effectiveEndIndex,
                                    showLegend = showLegend,
                                    showGridLines = showGridLines,
                                    showDataLabels = showDataLabels,
                                    showTrendLine = showTrendLine,
                                    showThresholdHighlights = showThresholdHighlights,
                                    scenario = scenario,
                                    modifier = Modifier.fillMaxSize()
                                )
                            }
                            1 -> {
                                // Bar Chart
                                VitalSignsBarChart(
                                    vitalSigns = vitalSigns,
                                    selectedVitalSigns = selectedVitalSigns,
                                    startTimeIndex = effectiveStartIndex,
                                    endTimeIndex = effectiveEndIndex,
                                    showLegend = showLegend,
                                    showGridLines = showGridLines,
                                    showDataLabels = showDataLabels,
                                    showThresholdHighlights = showThresholdHighlights,
                                    scenario = scenario,
                                    modifier = Modifier.fillMaxSize()
                                )
                            }
                            else -> {
                                // Scatter Plot
                                VitalSignsScatterChart(
                                    vitalSigns = vitalSigns,
                                    selectedVitalSigns = selectedVitalSigns,
                                    startTimeIndex = effectiveStartIndex,
                                    endTimeIndex = effectiveEndIndex,
                                    showLegend = showLegend,
                                    showGridLines = showGridLines,
                                    showDataLabels = showDataLabels,
                                    showThresholdHighlights = showThresholdHighlights,
                                    scenario = scenario,
                                    modifier = Modifier.fillMaxSize()
                                )
                            }
                        }
                    }
                }
                
                // Show loading overlay when exporting
                if (isExporting) {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(Color.Black.copy(alpha = 0.5f)),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            CircularProgressIndicator(
                                color = MaterialTheme.colorScheme.onPrimary
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                "Exporting chart...",
                                color = MaterialTheme.colorScheme.onPrimary
                            )
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Calculate the number of data points that will be displayed
            val displayedCount = if (vitalSigns.isNotEmpty()) {
                val start = startTimeIndex.coerceIn(0, vitalSigns.size - 1)
                val end = endTimeIndex.coerceIn(start + 1, vitalSigns.size)
                end - start
            } else {
                0
            }
            
            // Chart configuration summary
            Text(
                text = "Displaying ${selectedVitalSigns.joinToString(", ")} for $displayedCount readings",
                style = MaterialTheme.typography.bodyMedium,
                textAlign = TextAlign.Center
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // Options summary
            Text(
                text = "Options: ${if (showLegend) "Legend, " else ""}${if (showGridLines) "Grid Lines, " else ""}${if (showDataLabels) "Data Labels, " else ""}${if (showTrendLine) "Trend Line, " else ""}${if (showThresholdHighlights) "Threshold Highlights" else ""}",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                textAlign = TextAlign.Center
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // Total filtered data count
            Text(
                text = "Total filtered data: ${vitalSigns.size} readings",
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.primary
            )
        }
    }
    
    // Export dialog
    if (showExportDialog) {
        AlertDialog(
            onDismissRequest = { showExportDialog = false },
            title = { Text("Export Visualization") },
            text = {
                Column {
                    Text("Enter details for the export:")
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    OutlinedTextField(
                        value = exportFileName,
                        onValueChange = { exportFileName = it },
                        label = { Text("File Name") },
                        modifier = Modifier.fillMaxWidth()
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    Text("Select Format:")
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        exportFormats.forEach { format ->
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                modifier = Modifier.clickable { exportFormat = format }
                            ) {
                                RadioButton(
                                    selected = exportFormat == format,
                                    onClick = { exportFormat = format }
                                )
                                Text(format)
                            }
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    Text(
                        "The chart will be exported with the current visualization settings.",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            },
            confirmButton = {
                Button(
                    onClick = {
                        showExportDialog = false
                        // Launch file creation
                        val mimeType = when (exportFormat) {
                            "PNG" -> "image/png"
                            "JPG" -> "image/jpeg"
                            "PDF" -> "application/pdf"
                            else -> "image/png"
                        }
                        exportLauncher.launch("$exportFileName.${exportFormat.lowercase()}")
                    }
                ) {
                    Text("Export")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showExportDialog = false }
                ) {
                    Text("Cancel")
                }
            }
        )
    }
}

@Composable
fun DataFilterSection(
    viewModel: VitalSignsViewModel,
    modifier: Modifier = Modifier
) {
    val filterValues by viewModel.filterValues.collectAsState()
    val selectedFilters by viewModel.selectedFilters.collectAsState()
    val vitalRangeFilters by viewModel.vitalRangeFilters.collectAsState()
    val availableVitalSigns by viewModel.availableVitalSigns.collectAsState()
    
    OutlinedCard(
        modifier = modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                    text = "Applied Data Filters",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
                // Navigation hint
                Text(
                    text = "Edit in Data Explorer tab",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.primary
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Show applied category filters
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "Simulation:",
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = selectedFilters.sim ?: "All",
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
                
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "Date:",
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = selectedFilters.date ?: "All",
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
                
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "Mannequin:",
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = selectedFilters.mannequin ?: "All",
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Show time range filter if set
            if (selectedFilters.startTime != null || selectedFilters.endTime != null) {
            Text(
                    text = "Time Range:",
                style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium
                )
                
                Text(
                    text = "${selectedFilters.startTime ?: "Start"} to ${selectedFilters.endTime ?: "End"}",
                    style = MaterialTheme.typography.bodyMedium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            }
            
            // Vital sign range filters section (if available)
            if (availableVitalSigns.isNotEmpty()) {
                // Check if any vital range filters are active
                val hasActiveRangeFilters = vitalRangeFilters.run {
                    (heartRate.available && (heartRate.min > heartRate.defaultMin || heartRate.max < heartRate.defaultMax)) ||
                    (spo2.available && (spo2.min > spo2.defaultMin || spo2.max < spo2.defaultMax)) ||
                    (nibpSys.available && (nibpSys.min > nibpSys.defaultMin || nibpSys.max < nibpSys.defaultMax)) ||
                    (temp.available && (temp.min > temp.defaultMin || temp.max < temp.defaultMax))
                }
                
                if (hasActiveRangeFilters) {
        Text(
                        text = "Vital Sign Ranges:",
            style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium
                    )
                    
                    // Display active vital sign filters
                    Column(modifier = Modifier.padding(start = 8.dp)) {
                        if (vitalRangeFilters.heartRate.available &&
                            (vitalRangeFilters.heartRate.min > vitalRangeFilters.heartRate.defaultMin ||
                            vitalRangeFilters.heartRate.max < vitalRangeFilters.heartRate.defaultMax)) {
                    Text(
                                text = "• Heart Rate: ${vitalRangeFilters.heartRate.min.toInt()}-${vitalRangeFilters.heartRate.max.toInt()} bpm",
                                style = MaterialTheme.typography.bodySmall
                            )
                        }
                        
                        if (vitalRangeFilters.spo2.available &&
                            (vitalRangeFilters.spo2.min > vitalRangeFilters.spo2.defaultMin ||
                            vitalRangeFilters.spo2.max < vitalRangeFilters.spo2.defaultMax)) {
                            Text(
                                text = "• SpO₂: ${vitalRangeFilters.spo2.min.toInt()}-${vitalRangeFilters.spo2.max.toInt()}%",
                                style = MaterialTheme.typography.bodySmall
                    )
                }
                        
                        if (vitalRangeFilters.nibpSys.available &&
                            (vitalRangeFilters.nibpSys.min > vitalRangeFilters.nibpSys.defaultMin ||
                            vitalRangeFilters.nibpSys.max < vitalRangeFilters.nibpSys.defaultMax)) {
                            Text(
                                text = "• BP Systolic: ${vitalRangeFilters.nibpSys.min.toInt()}-${vitalRangeFilters.nibpSys.max.toInt()} mmHg",
                                style = MaterialTheme.typography.bodySmall
                            )
                        }
                        
                        if (vitalRangeFilters.temp.available &&
                            (vitalRangeFilters.temp.min > vitalRangeFilters.temp.defaultMin ||
                            vitalRangeFilters.temp.max < vitalRangeFilters.temp.defaultMax)) {
                            Text(
                                text = "• Temperature: ${vitalRangeFilters.temp.min.toInt()}-${vitalRangeFilters.temp.max.toInt()} °C",
                                style = MaterialTheme.typography.bodySmall
                            )
                        }
                    }
                }
            }
            
            // Reset filters option redirects to Data Explorer
            Button(
                onClick = { viewModel.updateCurrentTab(com.example.myapplication.ui.viewmodel.VitalSignsTab.DATA_EXPLORER) },
                modifier = Modifier.align(Alignment.End).padding(top = 8.dp)
            ) {
                Text("Manage Filters")
            }
        }
    }
}

private fun formatTime(date: Date): String {
    val sdf = SimpleDateFormat("HH:mm:ss", Locale.getDefault())
    return sdf.format(date)
} 