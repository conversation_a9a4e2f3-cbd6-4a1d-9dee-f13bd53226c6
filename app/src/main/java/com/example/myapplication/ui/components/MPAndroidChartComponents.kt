package com.example.myapplication.ui.components

import android.graphics.Color
import android.graphics.Typeface
import android.graphics.drawable.ColorDrawable
import android.view.MotionEvent
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Remove
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import com.example.myapplication.data.ClinicalPracticeGuidelines
import com.example.myapplication.data.model.VitalSign
import com.example.myapplication.data.model.ScenarioThresholds
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.charts.BarChart
import com.github.mikephil.charting.charts.ScatterChart
import com.github.mikephil.charting.components.Legend
import com.github.mikephil.charting.components.LimitLine
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.data.BarData
import com.github.mikephil.charting.data.BarDataSet
import com.github.mikephil.charting.data.BarEntry
import com.github.mikephil.charting.data.ScatterData
import com.github.mikephil.charting.data.ScatterDataSet
import com.github.mikephil.charting.formatter.IFillFormatter
import com.github.mikephil.charting.formatter.ValueFormatter
import com.github.mikephil.charting.listener.ChartTouchListener
import com.github.mikephil.charting.listener.OnChartGestureListener
import androidx.compose.ui.graphics.Color as ComposeColor
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * A composable that wraps MPAndroidChart's LineChart
 */
@Composable
fun VitalSignsLineChart(
    vitalSigns: List<VitalSign>,
    selectedVitalSigns: List<String>,
    startTimeIndex: Int,
    endTimeIndex: Int,
    showLegend: Boolean = true,
    showGridLines: Boolean = true,
    showDataLabels: Boolean = false,
    showTrendLine: Boolean = false,
    showThresholdHighlights: Boolean = true,
    scenario: String = "TBI", // Default to TBI if not specified
    modifier: Modifier = Modifier
) {
    // Filter data by time range
    val dataToDisplay = if (vitalSigns.isNotEmpty()) {
        vitalSigns.subList(
            startTimeIndex.coerceIn(0, vitalSigns.size - 1),
            endTimeIndex.coerceIn(startTimeIndex + 1, vitalSigns.size)
        ).sortedBy { it.timeObj.time }
    } else {
        emptyList()
    }

    // Get the CPG thresholds for the current scenario
    val thresholds = remember(scenario) {
        ClinicalPracticeGuidelines.ALL_THRESHOLDS[scenario] ?: ClinicalPracticeGuidelines.TBI
    }

    // Get the newer format thresholds that include warning levels
    val detailedThresholds = remember(scenario) {
        ScenarioThresholds.getThresholdsByScenario(scenario)
    }

    // Create entries for each vital sign
    val entries = remember(dataToDisplay, selectedVitalSigns) {
        val result = mutableMapOf<String, List<Entry>>()
        
        if (dataToDisplay.isNotEmpty()) {
            // Get the earliest timestamp for reference
            val firstTimestamp = dataToDisplay.first().timeObj.time
            
            selectedVitalSigns.forEach { vitalSign ->
                when (vitalSign) {
                    "Heart Rate" -> {
                        val entries = dataToDisplay.mapIndexedNotNull { index, vs ->
                            vs.hr?.let { hr ->
                                val timeInSeconds = (vs.timeObj.time - firstTimestamp) / 1000f
                                Entry(timeInSeconds, hr.toFloat())
                            }
                        }
                        if (entries.isNotEmpty()) {
                            result["Heart Rate"] = entries
                        }
                    }
                    "SpO2" -> {
                        val entries = dataToDisplay.mapIndexedNotNull { index, vs ->
                            vs.spO2?.let { spo2 ->
                                val timeInSeconds = (vs.timeObj.time - firstTimestamp) / 1000f
                                Entry(timeInSeconds, spo2.toFloat())
                            }
                        }
                        if (entries.isNotEmpty()) {
                            result["SpO2"] = entries
                        }
                    }
                    "Blood Pressure" -> {
                        val entries = dataToDisplay.mapIndexedNotNull { index, vs ->
                            vs.nibpSys?.let { bp ->
                                val timeInSeconds = (vs.timeObj.time - firstTimestamp) / 1000f
                                Entry(timeInSeconds, bp.toFloat())
                            }
                        }
                        if (entries.isNotEmpty()) {
                            result["Blood Pressure"] = entries
                        }
                    }
                    "Temperature" -> {
                        val entries = dataToDisplay.mapIndexedNotNull { index, vs ->
                            vs.temp1?.let { temp ->
                                val timeInSeconds = (vs.timeObj.time - firstTimestamp) / 1000f
                                Entry(timeInSeconds, temp.toFloat())
                            }
                        }
                        if (entries.isNotEmpty()) {
                            result["Temperature"] = entries
                        }
                    }
                }
            }
        }
        
        result
    }

    // Format timestamps for X-axis
    val timeFormatter = remember(dataToDisplay) {
        object : ValueFormatter() {
            private val dateFormat = SimpleDateFormat("HH:mm:ss", Locale.getDefault())
            
            override fun getFormattedValue(value: Float): String {
                if (dataToDisplay.isEmpty()) return ""
                
                val firstTimestamp = dataToDisplay.first().timeObj.time
                val timeInMillis = firstTimestamp + (value * 1000).toLong()
                val date = Date(timeInMillis)
                return dateFormat.format(date)
            }
        }
    }

    // Define chart colors for different vital signs
    val chartColors = remember {
        mapOf(
            "Heart Rate" to ComposeColor.Red.toArgb(),
            "SpO2" to ComposeColor.Blue.toArgb(),
            "Blood Pressure" to ComposeColor.Green.toArgb(),
            "Temperature" to ComposeColor(0xFFFFA500).toArgb() // Orange
        )
    }
    
    // Define threshold colors with more granularity
    val thresholdColors = remember {
        mapOf(
            "critical_high" to Color.argb(60, 255, 0, 0),      // Light red for critical high
            "warning_high" to Color.argb(60, 255, 165, 0),     // Light orange for warning high
            "normal" to Color.TRANSPARENT,                      // Transparent for normal range
            "warning_low" to Color.argb(60, 255, 165, 0),      // Light orange for warning low
            "critical_low" to Color.argb(60, 255, 0, 0)        // Light red for critical low
        )
    }

    // Reference to the chart to control it from zoom buttons
    var lineChartRef: LineChart? = null

    Box(
        modifier = modifier.fillMaxSize()
    ) {
        // Create the LineChart
        AndroidView(
            modifier = Modifier.fillMaxSize(),
            factory = { context ->
                LineChart(context).apply {
                    description.isEnabled = false
                    setDrawGridBackground(false)
                    setTouchEnabled(true)
                    isDragEnabled = true
                    isScaleXEnabled = true
                    isScaleYEnabled = true
                    setPinchZoom(true)
                    setScaleEnabled(true)
                    isDoubleTapToZoomEnabled = true  // Enable double tap to zoom
                    
                    // Configure X-axis
                    xAxis.apply {
                        position = XAxis.XAxisPosition.BOTTOM
                        setDrawGridLines(showGridLines)
                        valueFormatter = timeFormatter
                        labelRotationAngle = -45f
                        granularity = 1f
                        textColor = Color.DKGRAY
                    }

                    // Configure Y-axis
                    axisLeft.apply {
                        setDrawGridLines(showGridLines)
                        axisMinimum = 0f
                        textColor = Color.DKGRAY
                    }
                    
                    axisRight.isEnabled = false

                    // Configure legend
                    legend.apply {
                        isEnabled = showLegend
                        form = Legend.LegendForm.LINE
                        textColor = Color.DKGRAY
                        verticalAlignment = Legend.LegendVerticalAlignment.BOTTOM
                        horizontalAlignment = Legend.LegendHorizontalAlignment.CENTER
                        orientation = Legend.LegendOrientation.HORIZONTAL
                        setDrawInside(false)
                    }
                    
                    // Add double tap listener for better zoom control
                    setOnChartGestureListener(object : OnChartGestureListener {
                        override fun onChartGestureStart(me: MotionEvent?, lastPerformedGesture: ChartTouchListener.ChartGesture?) {}
                        override fun onChartGestureEnd(me: MotionEvent?, lastPerformedGesture: ChartTouchListener.ChartGesture?) {}
                        override fun onChartLongPressed(me: MotionEvent?) {}
                        override fun onChartDoubleTapped(me: MotionEvent?) {
                            // Zoom in on double tap
                            fitScreen() // Reset zoom first
                            zoom(1.5f, 1.5f, me?.x ?: width/2f, me?.y ?: height/2f)
                        }
                        override fun onChartSingleTapped(me: MotionEvent?) {}
                        override fun onChartFling(me1: MotionEvent?, me2: MotionEvent?, velocityX: Float, velocityY: Float) {}
                        override fun onChartScale(me: MotionEvent?, scaleX: Float, scaleY: Float) {}
                        override fun onChartTranslate(me: MotionEvent?, dX: Float, dY: Float) {}
                    })
                    
                    // Save reference to the chart
                    lineChartRef = this
                }
            },
            update = { chart ->
                // Update the chart with new data
                val dataSets = entries.map { (name, entryList) ->
                    LineDataSet(entryList, name).apply {
                        color = chartColors[name] ?: Color.BLACK
                        setDrawValues(showDataLabels)
                        setDrawCircles(true)
                        circleRadius = 4f
                        circleColors = listOf(chartColors[name] ?: Color.BLACK)
                        lineWidth = 2f
                        
                        // Apply trend line (curve smoothness)
                        mode = if (showTrendLine) {
                            LineDataSet.Mode.CUBIC_BEZIER
                        } else {
                            LineDataSet.Mode.LINEAR
                        }

                        // If showing data labels, configure them
                        if (showDataLabels) {
                            valueTextSize = 9f
                            valueTextColor = Color.DKGRAY
                            valueTypeface = Typeface.DEFAULT_BOLD
                        }
                        
                        // Add threshold highlighting if enabled
                        if (showThresholdHighlights) {
                            // Get the threshold range for this vital sign
                            val thresholdKey = when (name) {
                                "Heart Rate" -> "hr"
                                "SpO2" -> "spo2"
                                "Blood Pressure" -> "nibp_sys"
                                "Temperature" -> "temp"
                                else -> null
                            }
                            
                            // Get the traditional min/max thresholds for the vital sign
                            val thresholdRange = thresholdKey?.let { 
                                val legacyKey = when (name) {
                                    "Heart Rate" -> "Hr"
                                    "SpO2" -> "SpO2"
                                    "Blood Pressure" -> "NIBP_SYS"
                                    "Temperature" -> "TempF"
                                    else -> null
                                }
                                legacyKey?.let { thresholds[it] }
                            }
                            
                            // Get the detailed thresholds with warning levels
                            val detailedThreshold = thresholdKey?.let { detailedThresholds[it] }
                            
                            // If detailed thresholds are available, use them for more granular coloring
                            if (detailedThreshold != null) {
                                // Create a list to hold fill colors for each data point
                                val fillColors = ArrayList<Int>()
                                
                                // Determine the fill color for each data point based on its value
                                entryList.forEach { entry ->
                                    val value = entry.y
                                    val fillColor = when {
                                        value > detailedThreshold.criticalHigh -> thresholdColors["critical_high"]!!
                                        value > detailedThreshold.warningHigh -> thresholdColors["warning_high"]!!
                                        value < detailedThreshold.criticalLow -> thresholdColors["critical_low"]!!
                                        value < detailedThreshold.warningLow -> thresholdColors["warning_low"]!!
                                        else -> thresholdColors["normal"]!!
                                    }
                                    fillColors.add(fillColor)
                                }
                                
                                // Apply colors to LineDataSet
                                if (fillColors.isNotEmpty()) {
                                    // For LineDataSet, use different approach for colors
                                    setDrawFilled(true)
                                    fillFormatter = IFillFormatter { dataSet, dataProvider -> 
                                        // Return the chart's y-minimum as the fill-line position
                                        dataProvider.getYChartMin()
                                    }
                                    
                                    // Set a base fill color (from first value)
                                    if (fillColors.any { it != Color.TRANSPARENT }) {
                                        fillDrawable = ColorDrawable(fillColors.first())
                                    }
                                }
                            }
                            // Fallback to basic thresholds if detailed ones aren't available
                            else if (thresholdRange != null) {
                                val minThreshold = thresholdRange.first?.toFloat()
                                val maxThreshold = thresholdRange.second?.toFloat()
                                
                                // Create a list to hold fill colors for each data point
                                val fillColors = ArrayList<Int>()
                                
                                // Determine the fill color for each data point based on its value
                                entryList.forEach { entry ->
                                    val value = entry.y
                                    val fillColor = when {
                                        maxThreshold != null && value > maxThreshold -> Color.argb(60, 255, 0, 0)    // Light red for above upper threshold
                                        minThreshold != null && value < minThreshold -> Color.argb(60, 255, 192, 0)   // Light yellow/amber for below lower threshold
                                        else -> Color.TRANSPARENT           // Transparent for normal range
                                    }
                                    fillColors.add(fillColor)
                                }
                                
                                // Apply colors to LineDataSet
                                if (fillColors.isNotEmpty()) {
                                    // For LineDataSet, use different approach for colors
                                    setDrawFilled(true)
                                    fillFormatter = IFillFormatter { dataSet, dataProvider -> 
                                        // Return the chart's y-minimum as the fill-line position
                                        dataProvider.getYChartMin()
                                    }
                                    
                                    // Set a base fill color (from first value)
                                    if (fillColors.any { it != Color.TRANSPARENT }) {
                                        fillDrawable = ColorDrawable(fillColors.first())
                                    }
                                }
                            }
                        }
                    }
                }

                // Create and set LineData
                val lineData = LineData(dataSets)
                chart.data = lineData
                
                // Add threshold limit lines if enabled
                if (showThresholdHighlights) {
                    // Clear existing limit lines
                    chart.axisLeft.removeAllLimitLines()
                    
                    // Add limit lines for selected vital signs
                    selectedVitalSigns.forEach { vitalSign ->
                        val basicThresholdKey = when (vitalSign) {
                            "Heart Rate" -> "Hr"
                            "SpO2" -> "SpO2"
                            "Blood Pressure" -> "NIBP_SYS"
                            "Temperature" -> "TempF"
                            else -> null
                        }
                        
                        val detailedThresholdKey = when (vitalSign) {
                            "Heart Rate" -> "hr"
                            "SpO2" -> "spo2"
                            "Blood Pressure" -> "nibp_sys"
                            "Temperature" -> "temp"
                            else -> null
                        }
                        
                        val basicThresholdRange = basicThresholdKey?.let { thresholds[it] }
                        val detailedThreshold = detailedThresholdKey?.let { detailedThresholds[it] }
                        
                        // Prefer detailed thresholds when available
                        if (detailedThreshold != null) {
                            // Add critical high limit line
                            val criticalHighLimit = LimitLine(detailedThreshold.criticalHigh.toFloat(), "$vitalSign Critical High: ${detailedThreshold.criticalHigh}")
                            criticalHighLimit.lineWidth = 1f
                            criticalHighLimit.lineColor = Color.RED
                            criticalHighLimit.textColor = Color.RED
                            criticalHighLimit.textSize = 10f
                            criticalHighLimit.labelPosition = LimitLine.LimitLabelPosition.RIGHT_TOP
                            chart.axisLeft.addLimitLine(criticalHighLimit)
                            
                            // Add warning high limit line
                            val warningHighLimit = LimitLine(detailedThreshold.warningHigh.toFloat(), "$vitalSign Warning High: ${detailedThreshold.warningHigh}")
                            warningHighLimit.lineWidth = 1f
                            warningHighLimit.lineColor = Color.rgb(255, 165, 0) // Orange
                            warningHighLimit.textColor = Color.rgb(255, 165, 0)
                            warningHighLimit.textSize = 10f
                            warningHighLimit.labelPosition = LimitLine.LimitLabelPosition.RIGHT_TOP
                            warningHighLimit.enableDashedLine(10f, 5f, 0f) // Dashed line
                            chart.axisLeft.addLimitLine(warningHighLimit)
                            
                            // Add warning low limit line
                            val warningLowLimit = LimitLine(detailedThreshold.warningLow.toFloat(), "$vitalSign Warning Low: ${detailedThreshold.warningLow}")
                            warningLowLimit.lineWidth = 1f
                            warningLowLimit.lineColor = Color.rgb(255, 165, 0) // Orange
                            warningLowLimit.textColor = Color.rgb(255, 165, 0)
                            warningLowLimit.textSize = 10f
                            warningLowLimit.labelPosition = LimitLine.LimitLabelPosition.RIGHT_BOTTOM
                            warningLowLimit.enableDashedLine(10f, 5f, 0f) // Dashed line
                            chart.axisLeft.addLimitLine(warningLowLimit)
                            
                            // Add critical low limit line
                            val criticalLowLimit = LimitLine(detailedThreshold.criticalLow.toFloat(), "$vitalSign Critical Low: ${detailedThreshold.criticalLow}")
                            criticalLowLimit.lineWidth = 1f
                            criticalLowLimit.lineColor = Color.RED
                            criticalLowLimit.textColor = Color.RED
                            criticalLowLimit.textSize = 10f
                            criticalLowLimit.labelPosition = LimitLine.LimitLabelPosition.RIGHT_BOTTOM
                            chart.axisLeft.addLimitLine(criticalLowLimit)
                        }
                        // Fallback to basic thresholds if detailed ones aren't available
                        else if (basicThresholdRange != null) {
                            // Add upper limit line if defined
                            basicThresholdRange.second?.let { maxValue ->
                                val upperLimit = LimitLine(maxValue.toFloat(), "$vitalSign Upper: $maxValue")
                                upperLimit.lineWidth = 1f
                                upperLimit.lineColor = Color.RED
                                upperLimit.textColor = Color.RED
                                upperLimit.textSize = 10f
                                upperLimit.labelPosition = LimitLine.LimitLabelPosition.RIGHT_TOP
                                chart.axisLeft.addLimitLine(upperLimit)
                            }
                            
                            // Add lower limit line if defined
                            basicThresholdRange.first?.let { minValue ->
                                val lowerLimit = LimitLine(minValue.toFloat(), "$vitalSign Lower: $minValue")
                                lowerLimit.lineWidth = 1f
                                lowerLimit.lineColor = Color.rgb(255, 150, 0) // Orange
                                lowerLimit.textColor = Color.rgb(255, 150, 0)
                                lowerLimit.textSize = 10f
                                lowerLimit.labelPosition = LimitLine.LimitLabelPosition.RIGHT_BOTTOM
                                chart.axisLeft.addLimitLine(lowerLimit)
                            }
                        }
                    }
                }
                
                // Refresh the chart
                chart.invalidate()
                
                // Ensure chart is visible with appropriate zoom
                if (dataSets.isNotEmpty()) {
                    chart.fitScreen()
                }
                
                // Update our reference
                lineChartRef = chart
            }
        )
        
        // Add zoom controls as floating action buttons
        Column(
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // Zoom in button
            FloatingActionButton(
                onClick = { 
                    lineChartRef?.apply {
                        zoomIn()  // MPAndroidChart's built-in zoom in function
                    }
                },
                modifier = Modifier.size(40.dp),
                containerColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.8f),
                contentColor = MaterialTheme.colorScheme.onPrimary
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "Zoom In",
                    modifier = Modifier.size(24.dp)
                )
            }
            
            // Zoom out button
            FloatingActionButton(
                onClick = { 
                    lineChartRef?.apply {
                        zoomOut() // MPAndroidChart's built-in zoom out function
                    }
                },
                modifier = Modifier.size(40.dp),
                containerColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.8f),
                contentColor = MaterialTheme.colorScheme.onPrimary
            ) {
                Icon(
                    imageVector = Icons.Default.Remove,
                    contentDescription = "Zoom Out",
                    modifier = Modifier.size(24.dp)
                )
            }
            
            // Reset zoom button
            FloatingActionButton(
                onClick = { 
                    lineChartRef?.apply {
                        fitScreen() // Reset zoom to show all data
                        invalidate() // Refresh the chart
                    }
                },
                modifier = Modifier.size(40.dp),
                containerColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.8f),
                contentColor = MaterialTheme.colorScheme.onPrimary
            ) {
                Icon(
                    imageVector = Icons.Default.Home,
                    contentDescription = "Reset Zoom",
                    modifier = Modifier.size(20.dp)
                )
            }
        }
    }
}

/**
 * A composable that wraps MPAndroidChart's BarChart
 */
@Composable
fun VitalSignsBarChart(
    vitalSigns: List<VitalSign>,
    selectedVitalSigns: List<String>,
    startTimeIndex: Int,
    endTimeIndex: Int,
    showLegend: Boolean = true,
    showGridLines: Boolean = true,
    showDataLabels: Boolean = false,
    showThresholdHighlights: Boolean = true,
    scenario: String = "TBI", // Default to TBI if not specified
    modifier: Modifier = Modifier
) {
    // Filter data by time range
    val dataToDisplay = if (vitalSigns.isNotEmpty()) {
        vitalSigns.subList(
            startTimeIndex.coerceIn(0, vitalSigns.size - 1),
            endTimeIndex.coerceIn(startTimeIndex + 1, vitalSigns.size)
        ).sortedBy { it.timeObj.time }
    } else {
        emptyList()
    }
    
    // Get the CPG thresholds for the current scenario
    val thresholds = remember(scenario) {
        ClinicalPracticeGuidelines.ALL_THRESHOLDS[scenario] ?: ClinicalPracticeGuidelines.TBI
    }

    // Create bar entries for each vital sign
    val entries = remember(dataToDisplay, selectedVitalSigns) {
        val result = mutableMapOf<String, List<BarEntry>>()
        
        if (dataToDisplay.isNotEmpty()) {
            // Get the earliest timestamp for reference
            val firstTimestamp = dataToDisplay.first().timeObj.time
            
            selectedVitalSigns.forEach { vitalSign ->
                when (vitalSign) {
                    "Heart Rate" -> {
                        val barEntries = dataToDisplay.mapIndexedNotNull { index, vs ->
                            vs.hr?.let { hr ->
                                val timeInSeconds = (vs.timeObj.time - firstTimestamp) / 1000f
                                BarEntry(timeInSeconds, hr.toFloat())
                            }
                        }
                        if (barEntries.isNotEmpty()) {
                            result["Heart Rate"] = barEntries
                        }
                    }
                    "SpO2" -> {
                        val barEntries = dataToDisplay.mapIndexedNotNull { index, vs ->
                            vs.spO2?.let { spo2 ->
                                val timeInSeconds = (vs.timeObj.time - firstTimestamp) / 1000f
                                BarEntry(timeInSeconds, spo2.toFloat())
                            }
                        }
                        if (barEntries.isNotEmpty()) {
                            result["SpO2"] = barEntries
                        }
                    }
                    "Blood Pressure" -> {
                        val barEntries = dataToDisplay.mapIndexedNotNull { index, vs ->
                            vs.nibpSys?.let { bp ->
                                val timeInSeconds = (vs.timeObj.time - firstTimestamp) / 1000f
                                BarEntry(timeInSeconds, bp.toFloat())
                            }
                        }
                        if (barEntries.isNotEmpty()) {
                            result["Blood Pressure"] = barEntries
                        }
                    }
                    "Temperature" -> {
                        val barEntries = dataToDisplay.mapIndexedNotNull { index, vs ->
                            vs.temp1?.let { temp ->
                                val timeInSeconds = (vs.timeObj.time - firstTimestamp) / 1000f
                                BarEntry(timeInSeconds, temp.toFloat())
                            }
                        }
                        if (barEntries.isNotEmpty()) {
                            result["Temperature"] = barEntries
                        }
                    }
                }
            }
        }
        
        result
    }

    // Format timestamps for X-axis
    val timeFormatter = remember(dataToDisplay) {
        object : ValueFormatter() {
            private val dateFormat = SimpleDateFormat("HH:mm:ss", Locale.getDefault())
            
            override fun getFormattedValue(value: Float): String {
                if (dataToDisplay.isEmpty()) return ""
                
                val firstTimestamp = dataToDisplay.first().timeObj.time
                val timeInMillis = firstTimestamp + (value * 1000).toLong()
                val date = Date(timeInMillis)
                return dateFormat.format(date)
            }
        }
    }

    // Define chart colors for different vital signs
    val chartColors = remember {
        mapOf(
            "Heart Rate" to ComposeColor.Red.toArgb(),
            "SpO2" to ComposeColor.Blue.toArgb(),
            "Blood Pressure" to ComposeColor.Green.toArgb(),
            "Temperature" to ComposeColor(0xFFFFA500).toArgb() // Orange
        )
    }

    // Define threshold colors
    val thresholdColors = remember {
        mapOf(
            "high" to Color.argb(120, 255, 0, 0),    // Semi-transparent red for above upper threshold
            "low" to Color.argb(120, 255, 192, 0),   // Semi-transparent yellow/amber for below lower threshold
            "normal" to Color.TRANSPARENT           // Transparent for normal range
        )
    }

    // Reference to the chart to control it from zoom buttons
    var barChartRef: BarChart? = null

    Box(
        modifier = modifier.fillMaxSize()
    ) {
        // Create the BarChart
        AndroidView(
            modifier = Modifier.fillMaxSize(),
            factory = { context ->
                BarChart(context).apply {
                    description.isEnabled = false
                    setDrawGridBackground(false)
                    setTouchEnabled(true)
                    isDragEnabled = true
                    isScaleXEnabled = true
                    isScaleYEnabled = true
                    setPinchZoom(true)

                    // Configure X-axis
                    xAxis.apply {
                        position = XAxis.XAxisPosition.BOTTOM
                        setDrawGridLines(showGridLines)
                        valueFormatter = timeFormatter
                        labelRotationAngle = -45f
                        granularity = 1f
                        textColor = Color.DKGRAY
                    }

                    // Configure Y-axis
                    axisLeft.apply {
                        setDrawGridLines(showGridLines)
                        axisMinimum = 0f
                        textColor = Color.DKGRAY
                    }
                    
                    axisRight.isEnabled = false

                    // Configure legend
                    legend.apply {
                        isEnabled = showLegend
                        form = Legend.LegendForm.SQUARE
                        textColor = Color.DKGRAY
                        verticalAlignment = Legend.LegendVerticalAlignment.BOTTOM
                        horizontalAlignment = Legend.LegendHorizontalAlignment.CENTER
                        orientation = Legend.LegendOrientation.HORIZONTAL
                        setDrawInside(false)
                    }
                }
            },
            update = { chart ->
                // Update chart with new data
                val dataSets = entries.map { (name, entryList) ->
                    BarDataSet(entryList, name).apply {
                        color = chartColors[name] ?: Color.BLACK
                        setDrawValues(showDataLabels)
                        
                        // If showing data labels, configure them
                        if (showDataLabels) {
                            valueTextSize = 9f
                            valueTextColor = Color.DKGRAY
                            valueTypeface = Typeface.DEFAULT_BOLD
                        }
                        
                        // Add threshold highlighting if enabled
                        if (showThresholdHighlights) {
                            // Get the threshold range for this vital sign
                            val thresholdKey = when (name) {
                                "Heart Rate" -> "Hr"
                                "SpO2" -> "SpO2"
                                "Blood Pressure" -> "NIBP_SYS"
                                "Temperature" -> "TempF"
                                else -> null
                            }
                            
                            // Get the min/max thresholds for the vital sign
                            val thresholdRange = thresholdKey?.let { thresholds[it] }
                            
                            // If thresholds are available, color entries based on range
                            if (thresholdRange != null) {
                                val minThreshold = thresholdRange.first?.toFloat()
                                val maxThreshold = thresholdRange.second?.toFloat()
                                
                                // Use colors from threshold colors
                                // Create a list to hold colors for each data point
                                val colors = entryList.map { entry ->
                                    val value = entry.y
                                    when {
                                        maxThreshold != null && value > maxThreshold -> thresholdColors["high"]!!
                                        minThreshold != null && value < minThreshold -> thresholdColors["low"]!!
                                        else -> chartColors[name] ?: Color.BLACK
                                    }
                                }
                                
                                // Apply colors to bars
                                if (colors.isNotEmpty()) {
                                    // For BarDataSet, use spread operator to convert array to vararg
                                    setColors(*colors.toIntArray())
                                }
                            }
                        }
                    }
                }
                
                // Create and set the data
                val barData = BarData(dataSets)
                
                // Adjust bar width based on number of bars
                if (dataSets.isNotEmpty()) {
                    // The smaller the number, the wider the bars
                    barData.barWidth = 0.9f / dataSets.size 
                }
                
                chart.data = barData
                
                // Add threshold limit lines if enabled
                if (showThresholdHighlights) {
                    // Clear existing limit lines
                    chart.axisLeft.removeAllLimitLines()
                    
                    // Add limit lines for selected vital signs
                    selectedVitalSigns.forEach { vitalSign ->
                        val thresholdKey = when (vitalSign) {
                            "Heart Rate" -> "Hr"
                            "SpO2" -> "SpO2"
                            "Blood Pressure" -> "NIBP_SYS"
                            "Temperature" -> "TempF"
                            else -> null
                        }
                        
                        val thresholdRange = thresholdKey?.let { thresholds[it] }
                        
                        if (thresholdRange != null) {
                            // Add upper limit line if defined
                            thresholdRange.second?.let { maxValue ->
                                val upperLimit = LimitLine(maxValue.toFloat(), "$vitalSign Upper: $maxValue")
                                upperLimit.lineWidth = 1f
                                upperLimit.lineColor = Color.RED
                                upperLimit.textColor = Color.RED
                                upperLimit.textSize = 10f
                                upperLimit.labelPosition = LimitLine.LimitLabelPosition.RIGHT_TOP
                                chart.axisLeft.addLimitLine(upperLimit)
                            }
                            
                            // Add lower limit line if defined
                            thresholdRange.first?.let { minValue ->
                                val lowerLimit = LimitLine(minValue.toFloat(), "$vitalSign Lower: $minValue")
                                lowerLimit.lineWidth = 1f
                                lowerLimit.lineColor = Color.rgb(255, 150, 0) // Orange
                                lowerLimit.textColor = Color.rgb(255, 150, 0)
                                lowerLimit.textSize = 10f
                                lowerLimit.labelPosition = LimitLine.LimitLabelPosition.RIGHT_BOTTOM
                                chart.axisLeft.addLimitLine(lowerLimit)
                            }
                        }
                    }
                }
                
                // Group the bars
                chart.groupBars(0f, 0.1f, 0.05f)
                
                // Refresh the chart
                chart.invalidate()
                
                // Ensure chart is visible with appropriate zoom
                if (dataSets.isNotEmpty()) {
                    chart.setVisibleXRangeMaximum(50f)
                    chart.moveViewToX(0f)
                }
            }
        )
        
        // Add zoom controls as floating action buttons
        Column(
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // Zoom in button
            FloatingActionButton(
                onClick = { 
                    barChartRef?.apply {
                        zoomIn()  // MPAndroidChart's built-in zoom in function
                    }
                },
                modifier = Modifier.size(40.dp),
                containerColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.8f),
                contentColor = MaterialTheme.colorScheme.onPrimary
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "Zoom In",
                    modifier = Modifier.size(24.dp)
                )
            }
            
            // Zoom out button
            FloatingActionButton(
                onClick = { 
                    barChartRef?.apply {
                        zoomOut() // MPAndroidChart's built-in zoom out function
                    }
                },
                modifier = Modifier.size(40.dp),
                containerColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.8f),
                contentColor = MaterialTheme.colorScheme.onPrimary
            ) {
                Icon(
                    imageVector = Icons.Default.Remove,
                    contentDescription = "Zoom Out",
                    modifier = Modifier.size(24.dp)
                )
            }
            
            // Reset zoom button
            FloatingActionButton(
                onClick = { 
                    barChartRef?.apply {
                        fitScreen() // Reset zoom to show all data
                        invalidate() // Refresh the chart
                    }
                },
                modifier = Modifier.size(40.dp),
                containerColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.8f),
                contentColor = MaterialTheme.colorScheme.onPrimary
            ) {
                Icon(
                    imageVector = Icons.Default.Home,
                    contentDescription = "Reset Zoom",
                    modifier = Modifier.size(20.dp)
                )
            }
        }
    }
}

/**
 * A composable that wraps MPAndroidChart's ScatterChart
 */
@Composable
fun VitalSignsScatterChart(
    vitalSigns: List<VitalSign>,
    selectedVitalSigns: List<String>,
    startTimeIndex: Int,
    endTimeIndex: Int,
    showLegend: Boolean = true,
    showGridLines: Boolean = true,
    showDataLabels: Boolean = false,
    showThresholdHighlights: Boolean = true,
    scenario: String = "TBI", // Default to TBI if not specified
    modifier: Modifier = Modifier
) {
    // Filter data by time range
    val dataToDisplay = if (vitalSigns.isNotEmpty()) {
        vitalSigns.subList(
            startTimeIndex.coerceIn(0, vitalSigns.size - 1),
            endTimeIndex.coerceIn(startTimeIndex + 1, vitalSigns.size)
        ).sortedBy { it.timeObj.time }
    } else {
        emptyList()
    }
    
    // Get the CPG thresholds for the current scenario
    val thresholds = remember(scenario) {
        ClinicalPracticeGuidelines.ALL_THRESHOLDS[scenario] ?: ClinicalPracticeGuidelines.TBI
    }

    // Get the newer format thresholds that include warning levels
    val detailedThresholds = remember(scenario) {
        ScenarioThresholds.getThresholdsByScenario(scenario)
    }

    // Create entries for each vital sign
    val entries = remember(dataToDisplay, selectedVitalSigns) {
        val result = mutableMapOf<String, List<Entry>>()
        
        if (dataToDisplay.isNotEmpty()) {
            // Get the earliest timestamp for reference
            val firstTimestamp = dataToDisplay.first().timeObj.time
            
            selectedVitalSigns.forEach { vitalSign ->
                when (vitalSign) {
                    "Heart Rate" -> {
                        val scatterEntries = dataToDisplay.mapIndexedNotNull { index, vs ->
                            vs.hr?.let { hr ->
                                val timeInSeconds = (vs.timeObj.time - firstTimestamp) / 1000f
                                Entry(timeInSeconds, hr.toFloat())
                            }
                        }
                        if (scatterEntries.isNotEmpty()) {
                            result["Heart Rate"] = scatterEntries
                        }
                    }
                    "SpO2" -> {
                        val scatterEntries = dataToDisplay.mapIndexedNotNull { index, vs ->
                            vs.spO2?.let { spo2 ->
                                val timeInSeconds = (vs.timeObj.time - firstTimestamp) / 1000f
                                Entry(timeInSeconds, spo2.toFloat())
                            }
                        }
                        if (scatterEntries.isNotEmpty()) {
                            result["SpO2"] = scatterEntries
                        }
                    }
                    "Blood Pressure" -> {
                        val scatterEntries = dataToDisplay.mapIndexedNotNull { index, vs ->
                            vs.nibpSys?.let { bp ->
                                val timeInSeconds = (vs.timeObj.time - firstTimestamp) / 1000f
                                Entry(timeInSeconds, bp.toFloat())
                            }
                        }
                        if (scatterEntries.isNotEmpty()) {
                            result["Blood Pressure"] = scatterEntries
                        }
                    }
                    "Temperature" -> {
                        val scatterEntries = dataToDisplay.mapIndexedNotNull { index, vs ->
                            vs.temp1?.let { temp ->
                                val timeInSeconds = (vs.timeObj.time - firstTimestamp) / 1000f
                                Entry(timeInSeconds, temp.toFloat())
                            }
                        }
                        if (scatterEntries.isNotEmpty()) {
                            result["Temperature"] = scatterEntries
                        }
                    }
                }
            }
        }
        
        result
    }

    // Format timestamps for X-axis
    val timeFormatter = remember(dataToDisplay) {
        object : ValueFormatter() {
            private val dateFormat = SimpleDateFormat("HH:mm:ss", Locale.getDefault())
            
            override fun getFormattedValue(value: Float): String {
                if (dataToDisplay.isEmpty()) return ""
                
                val firstTimestamp = dataToDisplay.first().timeObj.time
                val timeInMillis = firstTimestamp + (value * 1000).toLong()
                val date = Date(timeInMillis)
                return dateFormat.format(date)
            }
        }
    }

    // Define chart colors for each vital sign
    val chartColors = remember {
        mapOf(
            "Heart Rate" to ComposeColor.Red.toArgb(),
            "SpO2" to ComposeColor.Blue.toArgb(),
            "Blood Pressure" to ComposeColor.Green.toArgb(),
            "Temperature" to ComposeColor(0xFFFFA500).toArgb() // Orange
        )
    }
    
    // Define threshold colors with more granularity
    val thresholdColors = remember {
        mapOf(
            "critical_high" to Color.RED,
            "warning_high" to Color.rgb(255, 165, 0), // Orange
            "normal" to Color.rgb(0, 200, 83),        // Green
            "warning_low" to Color.rgb(255, 165, 0),  // Orange
            "critical_low" to Color.RED
        )
    }
    
    // Reference to the chart to control it from zoom buttons
    var scatterChartRef: ScatterChart? = null

    Box(
        modifier = modifier.fillMaxSize()
    ) {
        // Create the ScatterChart
        AndroidView(
            modifier = Modifier.fillMaxSize(),
            factory = { context ->
                ScatterChart(context).apply {
                    description.isEnabled = false
                    setDrawGridBackground(false)
                    setTouchEnabled(true)
                    isDragEnabled = true
                    isScaleXEnabled = true
                    isScaleYEnabled = true
                    setPinchZoom(true)

                    // Configure X-axis
                    xAxis.apply {
                        position = XAxis.XAxisPosition.BOTTOM
                        setDrawGridLines(showGridLines)
                        valueFormatter = timeFormatter
                        labelRotationAngle = -45f
                        granularity = 1f
                        textColor = Color.DKGRAY
                    }

                    // Configure Y-axis
                    axisLeft.apply {
                        setDrawGridLines(showGridLines)
                        axisMinimum = 0f
                        textColor = Color.DKGRAY
                    }
                    
                    axisRight.isEnabled = false

                    // Configure legend
                    legend.apply {
                        isEnabled = showLegend
                        form = Legend.LegendForm.CIRCLE
                        textColor = Color.DKGRAY
                        verticalAlignment = Legend.LegendVerticalAlignment.BOTTOM
                        horizontalAlignment = Legend.LegendHorizontalAlignment.CENTER
                        orientation = Legend.LegendOrientation.HORIZONTAL
                        setDrawInside(false)
                    }
                }
            },
            update = { chart ->
                // Update chart with new data
                val dataSets = entries.map { (name, entryList) ->
                    ScatterDataSet(entryList, name).apply {
                        color = chartColors[name] ?: Color.BLACK
                        setDrawValues(showDataLabels)
                        setScatterShape(ScatterChart.ScatterShape.CIRCLE)
                        scatterShapeSize = 10f
                        
                        // If showing data labels, configure them
                        if (showDataLabels) {
                            valueTextSize = 9f
                            valueTextColor = Color.DKGRAY
                            valueTypeface = Typeface.DEFAULT_BOLD
                        }
                        
                        // Add threshold highlighting with improved gradients if enabled
                        if (showThresholdHighlights) {
                            // Get the threshold range for this vital sign
                            val thresholdKey = when (name) {
                                "Heart Rate" -> "hr"
                                "SpO2" -> "spo2"
                                "Blood Pressure" -> "nibp_sys"
                                "Temperature" -> "temp"
                                else -> null
                            }
                            
                            // Get the min/max thresholds for the vital sign
                            val thresholdRange = thresholdKey?.let { thresholds[it] }
                            
                            // Get the detailed thresholds with warning levels
                            val detailedThreshold = thresholdKey?.let { detailedThresholds[it] }
                            
                            // If detailed thresholds are available, use them for more granular coloring
                            if (detailedThreshold != null) {
                                // Custom icon for each point based on threshold
                                val colors = entryList.map { entry ->
                                    val value = entry.y
                                    when {
                                        value > detailedThreshold.criticalHigh -> thresholdColors["critical_high"]!!
                                        value > detailedThreshold.warningHigh -> thresholdColors["warning_high"]!!
                                        value < detailedThreshold.criticalLow -> thresholdColors["critical_low"]!!
                                        value < detailedThreshold.warningLow -> thresholdColors["warning_low"]!!
                                        else -> thresholdColors["normal"]!!
                                    }
                                }
                                
                                // Apply colors to scatter points - fix for ScatterDataSet
                                if (colors.isNotEmpty()) {
                                    color = colors.firstOrNull() ?: Color.BLACK
                                    colors.forEachIndexed { index, color ->
                                        if (index < entryList.size) {
                                            // Color individual points if supported, otherwise use first color
                                            getEntryForIndex(index).icon = ColorDrawable(color)
                                        }
                                    }
                                }
                            }
                            // Fallback to basic thresholds if detailed ones aren't available
                            else if (thresholdRange != null) {
                                val minThreshold = thresholdRange.first?.toFloat()
                                val maxThreshold = thresholdRange.second?.toFloat()
                                
                                // Custom icon for each point based on threshold
                                val colors = entryList.map { entry ->
                                    val value = entry.y
                                    when {
                                        maxThreshold != null && value > maxThreshold -> Color.RED
                                        minThreshold != null && value < minThreshold -> Color.rgb(255, 150, 0) // Orange
                                        else -> chartColors[name] ?: Color.BLACK
                                    }
                                }
                                
                                // Apply colors to scatter points - fix for ScatterDataSet
                                if (colors.isNotEmpty()) {
                                    color = colors.firstOrNull() ?: Color.BLACK
                                    colors.forEachIndexed { index, color ->
                                        if (index < entryList.size) {
                                            // Color individual points if supported, otherwise use first color
                                            getEntryForIndex(index).icon = ColorDrawable(color)
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                
                // Create and set the data
                val scatterData = ScatterData(dataSets)
                chart.data = scatterData
                
                // Add threshold limit lines if enabled
                if (showThresholdHighlights) {
                    // Clear existing limit lines
                    chart.axisLeft.removeAllLimitLines()
                    
                    // Add limit lines for selected vital signs
                    selectedVitalSigns.forEach { vitalSign ->
                        val basicThresholdKey = when (vitalSign) {
                            "Heart Rate" -> "Hr"
                            "SpO2" -> "SpO2"
                            "Blood Pressure" -> "NIBP_SYS"
                            "Temperature" -> "TempF"
                            else -> null
                        }
                        
                        val detailedThresholdKey = when (vitalSign) {
                            "Heart Rate" -> "hr"
                            "SpO2" -> "spo2"
                            "Blood Pressure" -> "nibp_sys"
                            "Temperature" -> "temp"
                            else -> null
                        }
                        
                        val basicThresholdRange = basicThresholdKey?.let { thresholds[it] }
                        val detailedThreshold = detailedThresholdKey?.let { detailedThresholds[it] }
                        
                        // Prefer detailed thresholds when available
                        if (detailedThreshold != null) {
                            // Add critical high limit line
                            val criticalHighLimit = LimitLine(detailedThreshold.criticalHigh.toFloat(), "$vitalSign Critical High: ${detailedThreshold.criticalHigh}")
                            criticalHighLimit.lineWidth = 1f
                            criticalHighLimit.lineColor = Color.RED
                            criticalHighLimit.textColor = Color.RED
                            criticalHighLimit.textSize = 10f
                            criticalHighLimit.labelPosition = LimitLine.LimitLabelPosition.RIGHT_TOP
                            chart.axisLeft.addLimitLine(criticalHighLimit)
                            
                            // Add warning high limit line
                            val warningHighLimit = LimitLine(detailedThreshold.warningHigh.toFloat(), "$vitalSign Warning High: ${detailedThreshold.warningHigh}")
                            warningHighLimit.lineWidth = 1f
                            warningHighLimit.lineColor = Color.rgb(255, 165, 0) // Orange
                            warningHighLimit.textColor = Color.rgb(255, 165, 0)
                            warningHighLimit.textSize = 10f
                            warningHighLimit.labelPosition = LimitLine.LimitLabelPosition.RIGHT_TOP
                            warningHighLimit.enableDashedLine(10f, 5f, 0f) // Dashed line
                            chart.axisLeft.addLimitLine(warningHighLimit)
                            
                            // Add warning low limit line
                            val warningLowLimit = LimitLine(detailedThreshold.warningLow.toFloat(), "$vitalSign Warning Low: ${detailedThreshold.warningLow}")
                            warningLowLimit.lineWidth = 1f
                            warningLowLimit.lineColor = Color.rgb(255, 165, 0) // Orange
                            warningLowLimit.textColor = Color.rgb(255, 165, 0)
                            warningLowLimit.textSize = 10f
                            warningLowLimit.labelPosition = LimitLine.LimitLabelPosition.RIGHT_BOTTOM
                            warningLowLimit.enableDashedLine(10f, 5f, 0f) // Dashed line
                            chart.axisLeft.addLimitLine(warningLowLimit)
                            
                            // Add critical low limit line
                            val criticalLowLimit = LimitLine(detailedThreshold.criticalLow.toFloat(), "$vitalSign Critical Low: ${detailedThreshold.criticalLow}")
                            criticalLowLimit.lineWidth = 1f
                            criticalLowLimit.lineColor = Color.RED
                            criticalLowLimit.textColor = Color.RED
                            criticalLowLimit.textSize = 10f
                            criticalLowLimit.labelPosition = LimitLine.LimitLabelPosition.RIGHT_BOTTOM
                            chart.axisLeft.addLimitLine(criticalLowLimit)
                        }
                        // Fallback to basic thresholds if detailed ones aren't available
                        else if (basicThresholdRange != null) {
                            // Add upper limit line if defined
                            basicThresholdRange.second?.let { maxValue ->
                                val upperLimit = LimitLine(maxValue.toFloat(), "$vitalSign Upper: $maxValue")
                                upperLimit.lineWidth = 1f
                                upperLimit.lineColor = Color.RED
                                upperLimit.textColor = Color.RED
                                upperLimit.textSize = 10f
                                upperLimit.labelPosition = LimitLine.LimitLabelPosition.RIGHT_TOP
                                chart.axisLeft.addLimitLine(upperLimit)
                            }
                            
                            // Add lower limit line if defined
                            basicThresholdRange.first?.let { minValue ->
                                val lowerLimit = LimitLine(minValue.toFloat(), "$vitalSign Lower: $minValue")
                                lowerLimit.lineWidth = 1f
                                lowerLimit.lineColor = Color.rgb(255, 150, 0) // Orange
                                lowerLimit.textColor = Color.rgb(255, 150, 0)
                                lowerLimit.textSize = 10f
                                lowerLimit.labelPosition = LimitLine.LimitLabelPosition.RIGHT_BOTTOM
                                chart.axisLeft.addLimitLine(lowerLimit)
                            }
                        }
                    }
                }
                
                // Refresh the chart
                chart.invalidate()
                
                // Ensure chart is visible with appropriate zoom
                if (dataSets.isNotEmpty()) {
                    chart.setVisibleXRangeMaximum(50f)
                    chart.moveViewToX(0f)
                }
            }
        )
        
        // Add zoom controls as floating action buttons
        Column(
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // Zoom in button
            FloatingActionButton(
                onClick = { 
                    scatterChartRef?.apply {
                        zoomIn()  // MPAndroidChart's built-in zoom in function
                    }
                },
                modifier = Modifier.size(40.dp),
                containerColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.8f),
                contentColor = MaterialTheme.colorScheme.onPrimary
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "Zoom In",
                    modifier = Modifier.size(24.dp)
                )
            }
            
            // Zoom out button
            FloatingActionButton(
                onClick = { 
                    scatterChartRef?.apply {
                        zoomOut() // MPAndroidChart's built-in zoom out function
                    }
                },
                modifier = Modifier.size(40.dp),
                containerColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.8f),
                contentColor = MaterialTheme.colorScheme.onPrimary
            ) {
                Icon(
                    imageVector = Icons.Default.Remove,
                    contentDescription = "Zoom Out",
                    modifier = Modifier.size(24.dp)
                )
            }
            
            // Reset zoom button
            FloatingActionButton(
                onClick = { 
                    scatterChartRef?.apply {
                        fitScreen() // Reset zoom to show all data
                        invalidate() // Refresh the chart
                    }
                },
                modifier = Modifier.size(40.dp),
                containerColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.8f),
                contentColor = MaterialTheme.colorScheme.onPrimary
            ) {
                Icon(
                    imageVector = Icons.Default.Home,
                    contentDescription = "Reset Zoom",
                    modifier = Modifier.size(20.dp)
                )
            }
        }
    }
} 