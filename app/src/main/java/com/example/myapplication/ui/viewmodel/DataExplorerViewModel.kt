package com.example.myapplication.ui.viewmodel

import android.content.Context
import android.net.Uri
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.myapplication.data.model.VitalSign
import com.example.myapplication.data.repository.VitalSignsRepository
import com.example.myapplication.ui.state.DataLoadingState
import com.example.myapplication.util.CrashLogger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.util.Date
import android.util.Log

/**
 * NOTE: This ViewModel is not actually used in the app.
 * The main app uses VitalSignsViewModel directly.
 * This class exists only as a placeholder to satisfy compilation.
 */
class DataExplorerViewModel(
    private val vitalSignsRepository: VitalSignsRepository,
    private val context: Context
) : ViewModel() {
    
    companion object {
        private const val TAG = "DataExplorerViewModel"
    }
    
    // Loading state
    private val _loadingState = mutableStateOf<DataLoadingState>(DataLoadingState.Idle)
    val loadingState: State<DataLoadingState> = _loadingState
    
    // Database availability status
    private val _isDatabaseAvailable = mutableStateOf(true)
    val isDatabaseAvailable: Boolean
        get() = _isDatabaseAvailable.value
    
    // Available filters
    private val _availableSimulations = mutableStateOf<List<String>>(emptyList())
    val availableSimulations: State<List<String>> = _availableSimulations
    
    private val _availableDates = mutableStateOf<List<Date>>(emptyList())
    val availableDates: State<List<Date>> = _availableDates
    
    private val _availableMannequins = mutableStateOf<List<String>>(emptyList())
    val availableMannequins: State<List<String>> = _availableMannequins
    
    // Selected filters
    private val _selectedSimulation = mutableStateOf<String?>(null)
    val selectedSimulation: State<String?> = _selectedSimulation
    
    private val _selectedDate = mutableStateOf<Date?>(null)
    val selectedDate: State<Date?> = _selectedDate
    
    private val _selectedMannequin = mutableStateOf<String?>(null)
    val selectedMannequin: State<String?> = _selectedMannequin
    
    init {
        Log.d(TAG, "DataExplorerViewModel initialized, but it is not used in the app.")
        
        // Check if database is available by attempting to get record count
        viewModelScope.launch {
            try {
                withContext(Dispatchers.IO) {
                    vitalSignsRepository.getDatabaseRecordCount()
                }
                _isDatabaseAvailable.value = true
                Log.d(TAG, "Database is available")
            } catch (e: Exception) {
                _isDatabaseAvailable.value = false
                Log.e(TAG, "Database is not available", e)
                CrashLogger.logError(TAG, "Database is not available in DataExplorerViewModel", e)
            }
        }
    }
    
    // Update available filters based on current data
    private fun updateAvailableFilters() {
        viewModelScope.launch {
            try {
                _availableSimulations.value = vitalSignsRepository.vitalSigns.value
                    .mapNotNull { it.sim }
                    .distinct()
                    .sorted()
                
                _availableDates.value = vitalSignsRepository.vitalSigns.value
                    .mapNotNull { it.timeObj }
                    .map { Date(it.time) } // Create new Date object to get date part only
                    .distinct()
                    .sorted()
                
                _availableMannequins.value = vitalSignsRepository.vitalSigns.value
                    .mapNotNull { it.overrideMannequin }
                    .distinct()
                    .sorted()
            } catch (e: Exception) {
                // Handle errors if any
                Log.e(TAG, "Error updating available filters", e)
                CrashLogger.logError(TAG, "Error updating available filters", e)
            }
        }
    }
    
    // Reset filters to default (all selected)
    fun resetFilters() {
        _selectedSimulation.value = null
        _selectedDate.value = null  
        _selectedMannequin.value = null
    }
    
    // Set filters
    fun setSimulationFilter(simulation: String?) {
        _selectedSimulation.value = simulation
    }
    
    fun setDateFilter(date: Date?) {
        _selectedDate.value = date
    }
    
    fun setMannequinFilter(mannequin: String?) {
        _selectedMannequin.value = mannequin
    }
    
    // Load data from a directory
    fun loadDataFromDirectory(directoryUri: String) {
        viewModelScope.launch {
            _loadingState.value = DataLoadingState.Loading("Loading data from directory...")
            
            try {
                val uri = Uri.parse(directoryUri)
                
                // Load data using repository
                val result = withContext(Dispatchers.IO) {
                    vitalSignsRepository.loadDataFromDocumentUri(uri, context)
                }
                
                if (result.isNotEmpty()) {
                    _loadingState.value = DataLoadingState.Success(result.size)
                    
                    // Update available filters based on loaded data
                    updateAvailableFilters()
                    
                    // Apply default filters
                    resetFilters()
                } else {
                    _loadingState.value = DataLoadingState.Error("No data found in directory")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error loading data from directory", e)
                CrashLogger.logError(TAG, "Error loading data from directory", e)
                _loadingState.value = DataLoadingState.Error("Error loading data: ${e.message}")
            }
        }
    }
    
    // Load data from a file
    fun loadDataFromFile(fileUri: String) {
        viewModelScope.launch {
            _loadingState.value = DataLoadingState.Loading("Loading data from file...")
            
            try {
                val uri = Uri.parse(fileUri)
                
                // Load data using repository
                val result = withContext(Dispatchers.IO) {
                    vitalSignsRepository.loadDataFromFileUri(uri, context)
                }
                
                if (result.isNotEmpty()) {
                    _loadingState.value = DataLoadingState.Success(result.size)
                    
                    // Update available filters based on loaded data
                    updateAvailableFilters()
                    
                    // Apply default filters
                    resetFilters()
                } else {
                    _loadingState.value = DataLoadingState.Error("No data found in file")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error loading data from file", e)
                CrashLogger.logError(TAG, "Error loading data from file", e)
                _loadingState.value = DataLoadingState.Error("Error loading data: ${e.message}")
            }
        }
    }
    
    // Database operations
    
    /**
     * Save current vital signs data to the database
     * @return Number of records saved
     */
    suspend fun saveCurrentDataToDatabase(): Int {
        if (!isDatabaseAvailable) {
            Log.w(TAG, "Cannot save to database: Database is not available")
            _loadingState.value = DataLoadingState.Error("Database is not available")
            return 0
        }
        
        _loadingState.value = DataLoadingState.Loading("Saving data to database...")
        
        try {
            val recordsSaved = withContext(Dispatchers.IO) {
                vitalSignsRepository.saveCurrentDataToDatabase()
            }
            
            if (recordsSaved > 0) {
                _loadingState.value = DataLoadingState.Success(recordsSaved)
                Log.d(TAG, "Successfully saved $recordsSaved records to database")
            } else {
                _loadingState.value = DataLoadingState.Error("No records were saved to database")
                Log.w(TAG, "No records were saved to database")
            }
            
            return recordsSaved
        } catch (e: Exception) {
            Log.e(TAG, "Error saving to database", e)
            CrashLogger.logError(TAG, "Error saving to database in DataExplorerViewModel", e)
            _loadingState.value = DataLoadingState.Error("Error saving to database: ${e.message}")
            return 0
        }
    }
    
    /**
     * Load data from the database
     * @return List of vital signs
     */
    suspend fun loadDataFromDatabase(): List<VitalSign> {
        if (!isDatabaseAvailable) {
            Log.w(TAG, "Cannot load from database: Database is not available")
            _loadingState.value = DataLoadingState.Error("Database is not available")
            return emptyList()
        }
        
        _loadingState.value = DataLoadingState.Loading("Loading data from database...")
        
        try {
            val data = withContext(Dispatchers.IO) {
                vitalSignsRepository.loadDataFromDatabase()
            }
            
            if (data.isNotEmpty()) {
                // Update state with loaded data
                _loadingState.value = DataLoadingState.Success(data.size)
                Log.d(TAG, "Successfully loaded ${data.size} records from database")
                
                // Update available filters
                updateAvailableFilters()
                
                // Apply default filters
                resetFilters()
                
                return data
            } else {
                _loadingState.value = DataLoadingState.Error("No data found in database")
                Log.w(TAG, "No data found in database")
                return emptyList()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error loading from database", e)
            CrashLogger.logError(TAG, "Error loading from database in DataExplorerViewModel", e)
            _loadingState.value = DataLoadingState.Error("Error loading from database: ${e.message}")
            return emptyList()
        }
    }
    
    /**
     * Get count of records in the database
     * @return Count of records
     */
    suspend fun getDatabaseRecordCount(): Int {
        if (!isDatabaseAvailable) {
            return 0
        }
        
        return try {
            withContext(Dispatchers.IO) {
                vitalSignsRepository.getDatabaseRecordCount()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting database record count", e)
            CrashLogger.logError(TAG, "Error getting database record count", e)
            // Set database as unavailable if there's an error
            _isDatabaseAvailable.value = false
            0
        }
    }
    
    /**
     * Clear all data from the database
     */
    suspend fun clearDatabase() {
        if (!isDatabaseAvailable) {
            Log.w(TAG, "Cannot clear database: Database is not available")
            _loadingState.value = DataLoadingState.Error("Database is not available")
            return
        }
        
        _loadingState.value = DataLoadingState.Loading("Clearing database...")
        
        try {
            withContext(Dispatchers.IO) {
                vitalSignsRepository.clearDatabase()
            }
            _loadingState.value = DataLoadingState.Success(0)
            Log.d(TAG, "Database cleared successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error clearing database", e)
            CrashLogger.logError(TAG, "Error clearing database in DataExplorerViewModel", e)
            _loadingState.value = DataLoadingState.Error("Error clearing database: ${e.message}")
        }
    }
    
    /**
     * Reset loading state to idle
     */
    fun resetDataLoadingState() {
        _loadingState.value = DataLoadingState.Idle
    }
} 