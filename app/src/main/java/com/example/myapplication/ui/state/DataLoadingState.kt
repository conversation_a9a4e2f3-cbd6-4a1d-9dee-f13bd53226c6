package com.example.myapplication.ui.state

import android.util.Log

/**
 * State class for data loading operations
 */
sealed class DataLoadingState {
    object Idle : DataLoadingState() {
        override fun toString(): String = "Idle"
    }
    
    data class Loading(val message: String = "Loading...") : DataLoadingState() {
        init {
            Log.d("DataLoadingState", "State changed to Loading: $message")
        }
        override fun toString(): String = "Loading: $message"
    }
    
    data class Success(val count: Int) : DataLoadingState() {
        init {
            Log.d("DataLoadingState", "State changed to Success: count=$count")
        }
        override fun toString(): String = "Success: count=$count"
    }
    
    data class Error(val message: String) : DataLoadingState() {
        init {
            Log.e("DataLoadingState", "State changed to Error: $message")
        }
        override fun toString(): String = "Error: $message"
    }
} 