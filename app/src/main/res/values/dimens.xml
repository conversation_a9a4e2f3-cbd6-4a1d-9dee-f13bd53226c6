<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Base dimensions -->
    <dimen name="spacing_tiny">2dp</dimen>
    <dimen name="spacing_small">4dp</dimen>
    <dimen name="spacing_medium">8dp</dimen>
    <dimen name="spacing_normal">12dp</dimen>
    <dimen name="spacing_large">16dp</dimen>
    <dimen name="spacing_xlarge">24dp</dimen>
    <dimen name="spacing_xxlarge">32dp</dimen>
    
    <!-- Text sizes -->
    <dimen name="text_caption">12sp</dimen>
    <dimen name="text_body_small">14sp</dimen>
    <dimen name="text_body">16sp</dimen>
    <dimen name="text_title">18sp</dimen>
    <dimen name="text_headline">22sp</dimen>
    <dimen name="text_display">28sp</dimen>
    
    <!-- Component specific dimensions -->
    <dimen name="card_corner_radius">12dp</dimen>
    <dimen name="card_elevation">2dp</dimen>
    <dimen name="card_padding">16dp</dimen>
    <dimen name="card_spacing">8dp</dimen>
    
    <!-- Navigation dimensions -->
    <dimen name="nav_item_min_width">64dp</dimen>
    <dimen name="nav_item_padding">8dp</dimen>
    <dimen name="nav_label_padding">2dp</dimen>
    <dimen name="nav_icon_size">24dp</dimen>
    
    <!-- Bottom insets for gesture navigation -->
    <dimen name="gesture_navigation_bottom_padding">16dp</dimen>
</resources>
